'use client';

import React, { useState } from 'react';
import { ChevronDown, HelpCircle, Sparkles, MessageCircle, Gamepad2 } from 'lucide-react';
import { gamingFaqData, FAQItem } from '@/components/data/faq-data';
import Image from 'next/image';

// Custom SVG Gaming Icons
const GameControllerSVG = () => (
  <svg viewBox="0 0 24 24" className="w-6 h-6" fill="currentColor">
    <path d="M7.5 6.5C7.5 8.981 9.519 11 12 11s4.5-2.019 4.5-4.5S14.481 2 12 2 7.5 4.019 7.5 6.5zM20 21h1v-1c0-3.859-3.141-7-7-7h-4c-3.859 0-7 3.141-7 7v1h1 1 14z"/>
  </svg>
);

const GamepadSVG = () => (
  <svg viewBox="0 0 24 24" className="w-6 h-6" fill="currentColor">
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
  </svg>
);

const ConsoleSVG = () => (
  <svg viewBox="0 0 24 24" className="w-6 h-6" fill="currentColor">
    <path d="M21 6H3c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm-10 7H8v3H6v-3H3v-2h3V8h2v3h3v2zm4.5 2c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zm3-3c-.83 0-1.5-.67-1.5-1.5S17.67 9 18.5 9s1.5.67 1.5 1.5-.67 1.5-1.5 1.5z"/>
  </svg>
);

interface FAQItemProps {
  faq: FAQItem;
  isOpen: boolean;
  onToggle: () => void;
}

const FAQItemComponent: React.FC<FAQItemProps> = ({ faq, isOpen, onToggle }) => {
  const renderAnswer = (answer: string | string[]) => {
    if (Array.isArray(answer)) {
      return (
        <div className="space-y-3">
          {answer.map((line, index) => (
            <p key={index} className="text-gray-600 dark:text-gray-300 leading-relaxed">
              {index === 0 && line.includes('develop games for all major platforms') ? (
                <span className="font-medium text-sky-500 dark:text-sky-400">{line}</span>
              ) : (
                line
              )}
            </p>
          ))}
        </div>
      );
    }
    return <p className="text-gray-600 dark:text-gray-300 leading-relaxed">{answer}</p>;
  };

  return (
    <div className={`group relative border-2 rounded-xl mb-4 overflow-hidden transition-all duration-500 hover:shadow-xl ${
      isOpen
        ? 'border-sky-500 dark:border-sky-400 shadow-lg shadow-sky-500/20'
        : 'border-gray-200 dark:border-gray-700 hover:border-sky-300 dark:hover:border-sky-600'
    }`}>
      {/* Gradient background overlay */}
      <div className={`absolute inset-0 bg-gradient-to-r from-sky-50 via-purple-50 to-emerald-50 dark:from-sky-900/10 dark:via-purple-900/10 dark:to-emerald-900/10 transition-opacity duration-500 ${
        isOpen ? 'opacity-100' : 'opacity-0 group-hover:opacity-50'
      }`} />

      <button
        className="relative w-full px-6 py-5 text-left bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm hover:bg-white dark:hover:bg-gray-800 transition-all duration-300 flex items-center justify-between group"
        onClick={onToggle}
        aria-expanded={isOpen}
      >
        <div className="flex items-center space-x-4">
          <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold transition-all duration-300 ${
            isOpen
              ? 'bg-sky-500 text-white shadow-lg'
              : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 group-hover:bg-sky-100 dark:group-hover:bg-sky-900/30'
          }`}>
            {faq.id}
          </div>
          <span className="font-semibold text-gray-900 dark:text-white pr-4 group-hover:text-sky-500 dark:group-hover:text-sky-400 transition-colors duration-300">
            {faq.question}
          </span>
        </div>
        <div className="flex-shrink-0">
          <div className={`p-2 rounded-full transition-all duration-300 ${
            isOpen
              ? 'bg-sky-500 text-white rotate-180'
              : 'bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 group-hover:bg-sky-100 dark:group-hover:bg-sky-900/30 group-hover:text-sky-500'
          }`}>
            <ChevronDown className="h-4 w-4 transition-transform duration-300" />
          </div>
        </div>
      </button>

      <div
        className={`overflow-hidden transition-all duration-500 ease-out ${
          isOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
        }`}
      >
        <div className="relative px-6 py-6 bg-gradient-to-br from-gray-50 via-sky-50/30 to-purple-50/30 dark:from-gray-900 dark:via-sky-900/10 dark:to-purple-900/10 border-t border-gray-200 dark:border-gray-700">
          <div className="relative z-10">
            {renderAnswer(faq.answer)}
          </div>
          {/* Decorative corner element */}
          <div className="absolute top-4 right-4 w-2 h-2 bg-sky-400 rounded-full opacity-60" />
        </div>
      </div>
    </div>
  );
};

export default function GamingFAQSection() {
  const [openItems, setOpenItems] = useState<Set<number>>(new Set());

  const toggleItem = (id: number) => {
    const newOpenItems = new Set(openItems);
    if (newOpenItems.has(id)) {
      newOpenItems.delete(id);
    } else {
      newOpenItems.add(id);
    }
    setOpenItems(newOpenItems);
  };

  return (
    <section className="relative py-8 xs:py-10 sm:py-12 md:py-16 lg:py-20 px-2 xs:px-3 sm:px-4 overflow-hidden bg-white dark:bg-slate-950">
      {/* Clean Background for Light Mode */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-20 xs:-top-30 sm:-top-40 -right-20 xs:-right-30 sm:-right-40 w-40 xs:w-60 sm:w-80 h-40 xs:h-60 sm:h-80 bg-sky-500/5 dark:bg-gradient-to-br dark:from-sky-500/30 dark:to-purple-700/20 rounded-full blur-2xl xs:blur-3xl" />
        <div className="absolute -bottom-20 xs:-bottom-30 sm:-bottom-40 -left-20 xs:-left-30 sm:-left-40 w-40 xs:w-60 sm:w-80 h-40 xs:h-60 sm:h-80 bg-purple-600/5 dark:bg-gradient-to-tr dark:from-purple-600/25 dark:to-emerald-800/15 rounded-full blur-2xl xs:blur-3xl" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-48 xs:w-72 sm:w-96 h-48 xs:h-72 sm:h-96 bg-emerald-400/5 dark:bg-gradient-to-r dark:from-emerald-400/15 dark:to-sky-600/10 rounded-full blur-2xl xs:blur-3xl" />
      </div>

      <div className="relative container-responsive">
        {/* Enhanced Responsive Header */}
        <div className="text-center mb-8 xs:mb-10 sm:mb-12 md:mb-16">
          <div className="flex items-center justify-center mb-4 xs:mb-5 sm:mb-6">
            <div className="relative flex items-center space-x-4">
              {/* Gaming Icons */}
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-sky-500 to-purple-700 rounded-full blur-lg opacity-40" />
                <div className="relative bg-gradient-to-br from-sky-50 to-sky-100 dark:from-sky-900 dark:to-sky-800 p-2 xs:p-3 sm:p-4 rounded-full shadow-xl border-2 border-sky-200 dark:border-sky-600">
                  <GameControllerSVG />
                </div>
              </div>

              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-emerald-800 rounded-full blur-lg opacity-40" />
                <div className="relative bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900 dark:to-purple-800 p-2 xs:p-3 sm:p-4 rounded-full shadow-xl border-2 border-purple-200 dark:border-purple-600">
                  <Gamepad2 className="h-4 xs:h-5 sm:h-6 md:h-8 w-4 xs:w-5 sm:w-6 md:w-8 text-purple-500 dark:text-purple-300" />
                </div>
              </div>

              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-700 to-blue-900 rounded-full blur-lg opacity-40" />
                <div className="relative bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900 dark:to-blue-800 p-2 xs:p-3 sm:p-4 rounded-full shadow-xl border-2 border-blue-200 dark:border-blue-600">
                  <ConsoleSVG />
                </div>
              </div>
            </div>
          </div>
          <h2 className="text-responsive-2xl font-bold mb-4 xs:mb-5 sm:mb-6">
            <span className="bg-gradient-to-r from-blue-900 via-blue-700 to-blue-800 dark:from-blue-100 dark:via-blue-200 dark:to-blue-300 bg-clip-text text-transparent">
              Gaming FAQ
            </span>
            <span className="text-blue-500 dark:text-blue-400 text-3xl xs:text-4xl sm:text-5xl md:text-6xl lg:text-7xl">'</span>
            <span className="bg-gradient-to-r from-blue-800 via-blue-600 to-blue-900 dark:from-blue-300 dark:via-blue-200 dark:to-blue-100 bg-clip-text text-transparent">
              s
            </span>
          </h2>
          <p className="text-responsive-base text-gray-600 dark:text-gray-300 max-w-xs xs:max-w-sm sm:max-w-md md:max-w-lg lg:max-w-3xl mx-auto leading-relaxed">
            <span className="hidden sm:inline">Find answers to commonly asked questions about our game development services</span>
            <span className="sm:hidden">Common questions about game development</span>
          </p>
          <div className="mt-4 xs:mt-5 sm:mt-6 flex items-center justify-center space-x-1.5 xs:space-x-2">
            <Sparkles className="h-3 xs:h-4 sm:h-5 w-3 xs:w-4 sm:w-5 text-yellow-500 animate-pulse" />
            <span className="text-responsive-xs text-gray-500 dark:text-gray-400 font-medium">
              {openItems.size} of {gamingFaqData.length} questions expanded
            </span>
            <Sparkles className="h-3 xs:h-4 sm:h-5 w-3 xs:w-4 sm:w-5 text-yellow-500 animate-pulse" />
          </div>
        </div>

        {/* FAQ Items */}
        <div className="space-y-4 mb-12 xs:mb-14 sm:mb-16">
          {gamingFaqData.map((faq, index) => (
            <div
              key={faq.id}
              className="animate-fade-in-up"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <FAQItemComponent
                faq={faq}
                isOpen={openItems.has(faq.id)}
                onToggle={() => toggleItem(faq.id)}
              />
            </div>
          ))}
        </div>

        {/* Gaming Showcase */}
        <div className="text-center animate-fade-in-up" style={{ animationDelay: '1s' }}>
          <h3 className="text-xl xs:text-2xl font-bold mb-6 text-gray-900 dark:text-white">
            Explore Our <span className="text-blue-600 dark:text-blue-400">Gaming Collection</span>
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 xs:gap-6 mb-8">
            <div className="group relative bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2">
              {/* Enhanced Side Color Overlap */}
              <div className="absolute -right-2 top-2 bottom-2 w-4 bg-gradient-to-b from-blue-400 via-blue-500 to-blue-600 dark:from-blue-500 dark:via-blue-600 dark:to-blue-700 rounded-l-lg shadow-lg z-20"></div>

              <Image
                src="/gaming/ludogame.png"
                alt="Ludo Game Development"
                width={200}
                height={150}
                className="w-full h-32 xs:h-36 object-cover"
              />
              <div className="p-3">
                <h4 className="font-semibold text-gray-900 dark:text-white text-sm">Ludo Game</h4>
                <p className="text-xs text-gray-600 dark:text-gray-300">Multiplayer Board Game</p>
              </div>
              <div className="absolute inset-0 bg-blue-600/0 group-hover:bg-blue-600/10 transition-all duration-300"></div>
            </div>

            <div className="group relative bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2">
              {/* Enhanced Side Color Overlap */}
              <div className="absolute -right-2 top-2 bottom-2 w-4 bg-gradient-to-b from-purple-400 via-purple-500 to-purple-600 dark:from-purple-500 dark:via-purple-600 dark:to-purple-700 rounded-l-lg shadow-lg z-20"></div>

              <Image
                src="/gaming/teenpatti.png"
                alt="Teen Patti Game Development"
                width={200}
                height={150}
                className="w-full h-32 xs:h-36 object-cover"
              />
              <div className="p-3">
                <h4 className="font-semibold text-gray-900 dark:text-white text-sm">Teen Patti</h4>
                <p className="text-xs text-gray-600 dark:text-gray-300">Card Game</p>
              </div>
              <div className="absolute inset-0 bg-blue-600/0 group-hover:bg-blue-600/10 transition-all duration-300"></div>
            </div>

            <div className="group relative bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2">
              {/* Enhanced Side Color Overlap */}
              <div className="absolute -right-2 top-2 bottom-2 w-4 bg-gradient-to-b from-green-400 via-green-500 to-green-600 dark:from-green-500 dark:via-green-600 dark:to-green-700 rounded-l-lg shadow-lg z-20"></div>

              <Image
                src="/gaming/rumygame.png"
                alt="Rummy Game Development"
                width={200}
                height={150}
                className="w-full h-32 xs:h-36 object-cover"
              />
              <div className="p-3">
                <h4 className="font-semibold text-gray-900 dark:text-white text-sm">Rummy Game</h4>
                <p className="text-xs text-gray-600 dark:text-gray-300">Strategy Card Game</p>
              </div>
              <div className="absolute inset-0 bg-blue-600/0 group-hover:bg-blue-600/10 transition-all duration-300"></div>
            </div>

            <div className="group relative bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2">
              {/* Enhanced Side Color Overlap */}
              <div className="absolute -right-2 top-2 bottom-2 w-4 bg-gradient-to-b from-orange-400 via-orange-500 to-orange-600 dark:from-orange-500 dark:via-orange-600 dark:to-orange-700 rounded-l-lg shadow-lg z-20"></div>

              <Image
                src="/gaming/pockergame.png"
                alt="Poker Game Development"
                width={200}
                height={150}
                className="w-full h-32 xs:h-36 object-cover"
              />
              <div className="p-3">
                <h4 className="font-semibold text-gray-900 dark:text-white text-sm">Poker Game</h4>
                <p className="text-xs text-gray-600 dark:text-gray-300">Casino Game</p>
              </div>
              <div className="absolute inset-0 bg-blue-600/0 group-hover:bg-blue-600/10 transition-all duration-300"></div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 rounded-xl p-6 text-white">
            <p className="text-blue-100 mb-4">
              Ready to discuss your gaming project? Our experts are here to help!
            </p>
            <button className="bg-white text-blue-700 px-6 py-3 rounded-full font-semibold hover:bg-blue-50 transition-all duration-300 transform hover:scale-105 shadow-lg">
              Get Started Today
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
