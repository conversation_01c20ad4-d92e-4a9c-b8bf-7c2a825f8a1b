'use client';

import React, { useEffect, useRef } from 'react';
import Image from 'next/image';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
 
  type CarouselApi,
} from './ui/carousel';
import { Quote } from 'lucide-react';

const testimonials = [
  {
    name: 'PRASHANT GAUTAM',
    role: 'Manager. BRT Multi Software LLP',
    image: '/assets/review2.png',
    rating: 5,
    text: 'The service of the company is good and no one cares about the payment. The company has given good software at the normal rate.',
    color: '#0EA5E9', // sky-500 (plasma blue)
  },
  {
    name: 'GARVIT GAUTAM',
    role: 'Client',
    image: '/assets/review.png',
    rating: 4,
    text: 'BRT Multi API Soft LLP sets a benchmark in the software industry with its cutting-edge solutions and impeccable service.',
    color: '#A855F7', // purple-500 (laser purple)
  },
  {
    name: '<PERSON><PERSON>',
    role: 'Client',
    image: '/assets/review5.png',
    rating: 3.5,
    text: 'The staff was great. They all were very helpful and answered all our questions. The software service was always on time.',
    color: '#10B981', // emerald-500 (energy green)
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    role: 'Client',
    image: '/assets/review11.png',
    rating: 3.5,
    text: 'I recently had the pleasure of taking services from MAS, and it was an unforgettable experience.',
    color: '#0EA5E9', // sky-500 (plasma blue)
  },
  {
    name: 'Mona V',
    role: 'Client',
    image: '/assets/review4.png',
    rating: 3.5,
    text: 'This is the best consultancy ever. The staff is really helpful and great. GREAT TEAM WITH GREAT EXPERIENCE 👍',
    color: '#A855F7', // purple-500 (laser purple)
  },
  {
    name: 'Tanu Rastogi',
    role: 'Client',
    image: '/assets/review6.png',
    rating: 3.5,
    text: 'Thanks to the MAS company, this is much more stable. The team has integrated with the client\'s company effectively.',
    color: '#10B981', // emerald-500 (energy green)
  },
];

function renderStars(rating: number, color: string) {
  const fullStars = Math.floor(rating);
  const halfStar = rating % 1 !== 0;
  const emptyStars = 5 - fullStars - (halfStar ? 1 : 0);
  return (
    <div className="flex items-center gap-1 mt-2 mb-2">
      {Array.from({ length: fullStars }).map((_, i) => (
        <span key={i} className="text-xl drop-shadow" style={{ color }}>★</span>
      ))}
      {halfStar && <span className="text-xl drop-shadow" style={{ color }}>☆</span>}
      {Array.from({ length: emptyStars }).map((_, i) => (
        <span key={i + fullStars + 1} className="text-gray-300 dark:text-gray-600 text-xl">★</span>
      ))}
    </div>
  );
}

const TestimonialSlider = () => {
  const [api, setApi] = React.useState<CarouselApi | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Responsive slides per view
  const carouselOptions = {
    loop: true,
    slidesToScroll: 1,
    breakpoints: {
      1024: { slidesToScroll: 1, slidesToShow: 2 },
      640: { slidesToScroll: 1, slidesToShow: 2 },
      0: { slidesToScroll: 1, slidesToShow: 1 },
    },
  };

  useEffect(() => {
    if (!api) return;
    if (intervalRef.current) clearInterval(intervalRef.current);
    intervalRef.current = setInterval(() => {
      api.scrollNext();
    }, 5000);
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
    };
  }, [api]);

  return (
    <section className="relative py-6 xs:py-8 sm:py-10 md:py-12 lg:py-16 px-2 xs:px-3 sm:px-4 bg-transparent overflow-hidden">

      <div className="relative container-responsive z-10">
        <div className="text-center mb-8 xs:mb-9 sm:mb-10 md:mb-12 animate-fade-in-up">
          <h2 className="text-responsive-xl font-extrabold text-blue-600 dark:text-blue-400 mb-2 xs:mb-3 tracking-tight">
            <span className="hidden sm:inline">What Our Clients Say</span>
            <span className="sm:hidden">Client Reviews</span>
          </h2>
          <p className="text-responsive-base text-gray-600 dark:text-gray-300 max-w-xs xs:max-w-sm sm:max-w-md md:max-w-lg lg:max-w-3xl mx-auto font-medium">
            <span className="hidden sm:inline">Discover why businesses trust us with their most important projects</span>
            <span className="sm:hidden">Why businesses trust us</span>
          </p>
        </div>
        <Carousel setApi={setApi} opts={carouselOptions}>
          <CarouselContent className="gap-3 xs:gap-4 sm:gap-6">
            {testimonials.map((t, idx) => (
              <CarouselItem key={idx} className="basis-full sm:basis-1/2 px-2 xs:px-3 sm:px-4 animate-fade-in-up" style={{ animationDelay: `${idx * 0.1}s` }}>
                <div className="flex flex-col items-center justify-center h-full">
                  <div className="relative bg-white dark:bg-gray-900 rounded-responsive shadow-xl hover:shadow-2xl padding-responsive w-full border border-gray-200 dark:border-gray-700 transition-all duration-500 hover:scale-105 hover:border-blue-400 h-full flex flex-col group overflow-hidden">
                    {/* Enhanced Responsive Right corner circle shape */}
                    <svg className="absolute -top-3 xs:-top-4 sm:-top-6 -right-3 xs:-right-4 sm:-right-6 w-16 xs:w-20 sm:w-24 md:w-32 h-16 xs:h-20 sm:h-24 md:h-32 z-10 group-hover:scale-110 transition-transform duration-500" viewBox="0 0 128 128">
                      <circle cx="96" cy="32" r="20" fill={t.color} className="xs:r-24 sm:r-28" />
                    </svg>

                    {/* Enhanced responsive background with solid colors */}
                    <div className="absolute inset-0 bg-blue-50 dark:bg-gray-800 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-responsive"></div>

                    {/* Enhanced responsive quote icon */}
                    <div className="absolute -top-4 xs:-top-6 sm:-top-8 left-1/2 -translate-x-1/2 rounded-full p-1.5 xs:p-2 sm:p-3 shadow-xl z-10 group-hover:scale-110 transition-transform duration-500" style={{ backgroundColor: t.color }}>
                      <Quote className="text-white w-3 xs:w-4 sm:w-5 md:w-6 h-3 xs:h-4 sm:h-5 md:h-6" />
                    </div>

                    <div className="relative mt-4 xs:mt-5 sm:mt-6 mb-3 xs:mb-4">
                      <Image
                        src={t.image}
                        alt={t.name}
                        width={80}
                        height={80}
                        className="rounded-full border-2 xs:border-3 sm:border-4 border-gray-200 dark:border-gray-700 object-cover shadow-lg mx-auto z-10 group-hover:scale-110 transition-transform duration-500 w-12 xs:w-16 sm:w-20 h-12 xs:h-16 sm:h-20"
                      />
                    </div>

                    <h4 className="text-responsive-base font-bold mb-1 xs:mb-1.5 sm:mb-2 text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 text-center z-10 transition-all duration-500">{t.name}</h4>
                    <span className="role text-responsive-xs text-gray-600 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 mb-2 xs:mb-2.5 sm:mb-3 block text-center font-medium z-10 transition-colors duration-500">{t.role}</span>

                    <div className="mb-3 xs:mb-3.5 sm:mb-4 flex justify-center">
                      {renderStars(t.rating, t.color)}
                    </div>

                    <p className="text-center text-responsive-xs text-gray-600 dark:text-gray-400 group-hover:text-gray-800 dark:group-hover:text-gray-200 italic flex-1 z-10 leading-relaxed transition-colors duration-500">"{t.text}"</p>

                    {/* Enhanced responsive border with solid color */}
                    <div className="absolute inset-0 rounded-responsive border-2 border-transparent group-hover:border-blue-400 transition-all duration-500"></div>
                  </div>
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>
      </div>
    </section>
  );
};

export default TestimonialSlider; 