@tailwind base;
@tailwind components;
@tailwind utilities;

/* Enhanced Responsive utilities */
@layer utilities {
  .container-responsive {
    @apply w-full max-w-xs sm:max-w-lg md:max-w-4xl lg:max-w-6xl xl:max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8;
  }

  .text-responsive-xs {
    @apply text-xs sm:text-sm md:text-base;
  }

  .text-responsive-sm {
    @apply text-sm sm:text-base md:text-lg;
  }

  .text-responsive-base {
    @apply text-base sm:text-lg md:text-xl;
  }

  .text-responsive-lg {
    @apply text-lg sm:text-xl md:text-2xl lg:text-3xl;
  }

  .text-responsive-xl {
    @apply text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl;
  }

  .text-responsive-2xl {
    @apply text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl;
  }

  .padding-responsive {
    @apply p-3 sm:p-4 md:p-6 lg:p-8;
  }

  .padding-responsive-lg {
    @apply p-4 sm:p-6 md:p-8 lg:p-10 xl:p-12;
  }

  .gap-responsive {
    @apply gap-3 sm:gap-4 md:gap-6 lg:gap-8;
  }

  .rounded-responsive {
    @apply rounded-lg sm:rounded-xl md:rounded-2xl lg:rounded-3xl;
  }

  /* Additional responsive spacing utilities */
  .margin-responsive {
    @apply m-3 sm:m-4 md:m-6 lg:m-8;
  }

  .margin-responsive-lg {
    @apply m-4 sm:m-6 md:m-8 lg:m-10 xl:m-12;
  }

  /* Responsive height utilities */
  .min-h-responsive {
    @apply min-h-[40vh] xs:min-h-[45vh] sm:min-h-[50vh] md:min-h-[55vh] lg:min-h-[60vh];
  }

  /* Responsive width utilities */
  .w-responsive {
    @apply w-full max-w-xs xs:max-w-sm sm:max-w-md md:max-w-lg lg:max-w-xl xl:max-w-2xl;
  }

  /* Responsive grid utilities */
  .grid-responsive-auto {
    @apply grid-cols-1 xs:grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
  }

  .grid-responsive-cards {
    @apply grid-cols-1 xs:grid-cols-1 sm:grid-cols-2 lg:grid-cols-3;
  }

  /* Responsive flex utilities */
  .flex-responsive {
    @apply flex-col xs:flex-col sm:flex-row;
  }
}

/* Enhanced Animations */
@keyframes gradient-x {
  0%, 100% {
    background-size: 200% 200%;
    background-position: left center;
  }
  50% {
    background-size: 200% 200%;
    background-position: right center;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes stats-bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-gradient-x {
  animation: gradient-x 3s ease infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out;
}

.animate-stats-bounce {
  animation: stats-bounce 2s infinite;
}

.animate-spin-slow {
  animation: spin-slow 3s linear infinite;
}

/* CTA Section Enhancements */
.cta-shimmer {
  position: relative;
  overflow: hidden;
}

.cta-shimmer::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.cta-shimmer:hover::before {
  left: 100%;
}

:root {
  --primary: 217 91 60;
  --secondary: 217 91 70;
  --cta: 217 91 65;
  --background: 0 0 0;
  --box-background: 220 13 22;
  --light-text: 210 40 98;
  --support: 217 91 60;
  --border: 217 19 35;
  --glow: 217 91 60;
  
  --foreground: 0 0 100;
  --muted: 217 19 35;
  --muted-foreground: 215 20 65;
  --card: 220 13 22;
  --card-foreground: 210 40 98;
  --popover: 220 13 22;
  --popover-foreground: 210 40 98;
  --primary-foreground: 210 40 98;
  --secondary-foreground: 210 40 98;
  --accent: 217 19 35;
  --accent-foreground: 210 40 98;
  --destructive: 217 91 60;
  --destructive-foreground: 210 40 98;
  --input: 217 19 35;
  --ring: 217 91 60;
  --radius: 0.5rem;
}

.light {
  --primary: 217 91 60;
  --secondary: 217 91 70;
  --cta: 217 91 65;
  --background: 0 0 100;
  --box-background: 220 100 97;
  --light-text: 220 13 18;
  --support: 217 91 60;
  --border: 214 32 91;
  --glow: 217 91 60;
  --foreground: 220 13 18;
  --muted: 214 32 91;
  --muted-foreground: 215 28 45;
  --card: 0 0 100;
  --card-foreground: 220 13 18;
  --popover: 0 0 100;
  --popover-foreground: 220 13 18;
  --primary-foreground: 0 0 100;
  --secondary-foreground: 0 0 100;
  --accent: 220 100 97;
  --accent-foreground: 220 13 18;
  --destructive: 217 91 60;
  --destructive-foreground: 0 0 100;
  --input: 214 32 91;
  --ring: 217 91 60;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .video-overlay {
    background: linear-gradient(
      135deg,
      rgba(10, 10, 10, 0.8) 0%,
      rgba(15, 15, 15, 0.6) 50%,
      rgba(10, 10, 10, 0.9) 100%
    );
  }
  
  .glow-effect {
    box-shadow: 0 0 20px rgba(var(--glow), 0.3);
  }
  
  .service-card {
    background: linear-gradient(
      135deg,
      rgba(var(--box-background), 0.8),
      rgba(var(--box-background), 0.4)
    );
    backdrop-filter: blur(10px);
    border: 1px solid rgba(var(--border), 0.3);
  }
  
  .hero-title {
    background: linear-gradient(
      135deg,
      rgb(var(--primary)),
      rgb(var(--secondary))
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

.dark {
  color-scheme: dark;
}

.light {
  color-scheme: light;
}

/* Custom animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 20px rgba(34, 197, 94, 0.4); }
  50% { box-shadow: 0 0 30px rgba(34, 197, 94, 0.6); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Smooth scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

@layer utilities {
  @keyframes fade-in-up {
    0% { opacity: 0; transform: translateY(40px); }
    100% { opacity: 1; transform: translateY(0); }
  }
  .animate-fade-in-up {
    animation: fade-in-up 0.8s cubic-bezier(0.4,0,0.2,1) both;
  }
  @keyframes rocket-float {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
  }
  .animate-rocket-float {
    animation: rocket-float 2.5s ease-in-out infinite;
  }
  @keyframes spin-slow {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  .animate-spin-slow {
    animation: spin-slow 8s linear infinite;
  }
  @keyframes fade-in {
    0% { opacity: 0; }
    100% { opacity: 1; }
  }
  .animate-fade-in {
    animation: fade-in 1.2s cubic-bezier(0.4,0,0.2,1) both;
  }
  @keyframes slide-in-right {
    0% { opacity: 0; transform: translateX(50px); }
    100% { opacity: 1; transform: translateX(0); }
  }
  .animate-slide-in-right {
    animation: slide-in-right 1s cubic-bezier(0.4,0,0.2,1) both;
  }
  @keyframes glow-pulse {
    0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
    50% { box-shadow: 0 0 40px rgba(59, 130, 246, 0.6); }
  }
  .animate-glow-pulse {
    animation: glow-pulse 2s ease-in-out infinite;
  }
  @keyframes stats-bounce {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }
  .animate-stats-bounce {
    animation: stats-bounce 2s ease-in-out infinite;
  }
  @keyframes pattern-rotate {
    0% { transform: rotate(0deg) scale(1); }
    50% { transform: rotate(180deg) scale(1.1); }
    100% { transform: rotate(360deg) scale(1); }
  }
  .animate-pattern-rotate {
    animation: pattern-rotate 4s ease-in-out infinite;
  }

  @keyframes bounce-gentle {
    0%, 100% { transform: translateY(0) scale(1); }
    50% { transform: translateY(-8px) scale(1.05); }
  }
  .animate-bounce-gentle {
    animation: bounce-gentle 3s ease-in-out infinite;
  }

  @keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }
  .animate-gradient-shift {
    background-size: 200% 200%;
    animation: gradient-shift 4s ease infinite;
  }

  /* Gradient radial utility */
  .bg-gradient-radial {
    background: radial-gradient(circle, var(--tw-gradient-stops));
  }

  /* 3D perspective utilities */
  .perspective-1000 {
    perspective: 1000px;
  }

  .transform-gpu {
    transform: translateZ(0);
    backface-visibility: hidden;
  }

  /* Enhanced image animations */
  @keyframes image-break-in {
    0% {
      opacity: 0;
      transform: translateY(-120px) translateX(30px) rotate(-15deg) scale(0.7) rotateX(45deg) rotateY(-20deg);
    }
    50% {
      opacity: 0.7;
      transform: translateY(-20px) translateX(10px) rotate(-5deg) scale(0.9) rotateX(15deg) rotateY(-5deg);
    }
    100% {
      opacity: 1;
      transform: translateY(0) translateX(0) rotate(0deg) scale(1) rotateX(0deg) rotateY(0deg);
    }
  }

  .animate-image-break-in {
    animation: image-break-in 1.2s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
  }
}

/* Custom Swiper Pagination Styles */
.swiper-pagination-bullet-custom {
  width: 12px;
  height: 12px;
  background: rgba(255, 255, 255, 0.3);
  border: 2px solid rgba(59, 130, 246, 0.5);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.swiper-pagination-bullet-active-custom {
  background: linear-gradient(45deg, #3b82f6, #8b5cf6);
  border-color: rgba(255, 255, 255, 0.8);
  transform: scale(1.2);
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.6);
}

.swiper-pagination-bullet-custom:hover {
  background: rgba(59, 130, 246, 0.6);
  border-color: rgba(255, 255, 255, 0.9);
  transform: scale(1.1);
}

/* Enhanced Swiper Fade Effect */
.swiper-fade .swiper-slide {
  pointer-events: none;
  transition-property: opacity;
}

.swiper-fade .swiper-slide-active {
  pointer-events: auto;
}

.swiper-fade .swiper-slide-active,
.swiper-fade .swiper-slide-duplicate-active {
  opacity: 1;
}

.swiper-fade .swiper-slide:not(.swiper-slide-active) {
  opacity: 0 !important;
}

/* Prevent text and image overlap */
.hero-text-container {
  position: relative;
  z-index: 10;
}

.hero-image-container {
  position: relative;
  z-index: 5;
}

/* Smooth fade transitions */
.swiper-slide {
  transition: opacity 1.5s ease-in-out !important;
}

/* Modern Gaming Animations */
@layer utilities {
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  .animate-morph {
    animation: morph 8s ease-in-out infinite;
  }

  .animate-particle {
    animation: particle 3s linear infinite;
  }

  .animate-shimmer {
    animation: shimmer 2s linear infinite;
  }

  .animate-wave {
    animation: wave 4s ease-in-out infinite;
  }

  /* Advanced UI Animations */
  .animate-tilt {
    animation: tilt 10s ease-in-out infinite;
  }

  .animate-magnetic {
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .animate-magnetic:hover {
    transform: scale(1.05) rotate(2deg);
  }

  .animate-neon {
    animation: neon 2s ease-in-out infinite alternate;
  }

  .animate-hologram {
    animation: hologram 3s ease-in-out infinite;
  }

  .animate-cyber {
    animation: cyber 4s linear infinite;
  }

  .animate-glitch {
    animation: glitch 2s infinite;
  }

  .animate-matrix {
    animation: matrix 20s linear infinite;
  }

  .animate-aurora {
    animation: aurora 8s ease-in-out infinite;
  }

  .animate-liquid {
    animation: liquid 6s ease-in-out infinite;
  }

  .animate-crystallize {
    animation: crystallize 4s ease-in-out infinite;
  }

  /* Glassmorphism Effects */
  .glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .glass-dark {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }

  /* Micro-interactions */
  .micro-bounce {
    transition: transform 0.2s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  .micro-bounce:hover {
    transform: scale(1.1) translateY(-2px);
  }

  .micro-shake:hover {
    animation: shake 0.5s ease-in-out;
  }

  .micro-pulse:hover {
    animation: pulse 1s ease-in-out infinite;
  }
}

/* Modern Gaming Keyframes */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-10px) rotate(1deg);
  }
  50% {
    transform: translateY(-20px) rotate(0deg);
  }
  75% {
    transform: translateY(-10px) rotate(-1deg);
  }
}

@keyframes glow {
  from {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  to {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.6);
  }
}

@keyframes morph {
  0%, 100% {
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  }
  25% {
    border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
  }
  50% {
    border-radius: 50% 60% 30% 60% / 30% 60% 70% 40%;
  }
  75% {
    border-radius: 60% 40% 60% 30% / 70% 30% 60% 40%;
  }
}

@keyframes particle {
  0% {
    transform: translateY(0) scale(1) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) scale(0) rotate(360deg);
    opacity: 0;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes wave {
  0%, 100% {
    transform: translateX(0) translateY(0) rotate(0deg);
  }
  25% {
    transform: translateX(5px) translateY(-5px) rotate(1deg);
  }
  50% {
    transform: translateX(0) translateY(-10px) rotate(0deg);
  }
  75% {
    transform: translateX(-5px) translateY(-5px) rotate(-1deg);
  }
}

/* Advanced UI Keyframes */
@keyframes tilt {
  0%, 100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(1deg);
  }
  75% {
    transform: rotate(-1deg);
  }
}

@keyframes neon {
  from {
    text-shadow: 0 0 5px #00f, 0 0 10px #00f, 0 0 15px #00f, 0 0 20px #00f;
  }
  to {
    text-shadow: 0 0 10px #00f, 0 0 20px #00f, 0 0 30px #00f, 0 0 40px #00f;
  }
}

@keyframes hologram {
  0%, 100% {
    opacity: 1;
    filter: hue-rotate(0deg);
  }
  50% {
    opacity: 0.8;
    filter: hue-rotate(180deg);
  }
}

@keyframes cyber {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes glitch {
  0%, 100% {
    transform: translate(0);
  }
  20% {
    transform: translate(-2px, 2px);
  }
  40% {
    transform: translate(-2px, -2px);
  }
  60% {
    transform: translate(2px, 2px);
  }
  80% {
    transform: translate(2px, -2px);
  }
}

@keyframes matrix {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 100% 100%;
  }
}

@keyframes aurora {
  0%, 100% {
    background-position: 0% 50%;
  }
  25% {
    background-position: 100% 50%;
  }
  50% {
    background-position: 100% 100%;
  }
  75% {
    background-position: 0% 100%;
  }
}

@keyframes liquid {
  0%, 100% {
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    transform: translate(0, 0) rotate(0deg);
  }
  25% {
    border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
    transform: translate(5px, -5px) rotate(1deg);
  }
  50% {
    border-radius: 50% 60% 30% 60% / 30% 60% 70% 40%;
    transform: translate(0, -10px) rotate(0deg);
  }
  75% {
    border-radius: 60% 40% 60% 30% / 70% 30% 60% 40%;
    transform: translate(-5px, -5px) rotate(-1deg);
  }
}

@keyframes crystallize {
  0%, 100% {
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    transform: scale(1);
  }
  25% {
    clip-path: polygon(50% 0%, 20% 100%, 80% 100%);
    transform: scale(1.05);
  }
  50% {
    clip-path: polygon(50% 0%, 10% 100%, 90% 100%);
    transform: scale(1.1);
  }
  75% {
    clip-path: polygon(50% 0%, 30% 100%, 70% 100%);
    transform: scale(1.05);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}