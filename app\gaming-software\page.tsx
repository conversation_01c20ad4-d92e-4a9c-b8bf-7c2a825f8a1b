import { gamingStatsData } from "@/components/data/stats-data";
import type { Metadata } from 'next';
import { SharedNavigation } from "@/components/shared-navigation";
import dynamic from 'next/dynamic';
import { Suspense } from 'react';

// Lazy load components for better performance
const LazyHeroSection = dynamic(() => import('@/components/gaming/HeroSection'), {
  loading: () => <div className="w-full h-96 bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900 dark:to-blue-800 animate-pulse rounded-lg"></div>,
  ssr: true
});

const LazyGamingNextLevel = dynamic(() => import('@/components/gaming/GamingNextLevel'), {
  loading: () => <div className="w-full h-64 bg-gray-100 dark:bg-gray-800 animate-pulse rounded-lg"></div>,
  ssr: false
});

const LazyFeaturedGames = dynamic(() => import('@/components/gaming/ludogame'), {
  loading: () => <div className="w-full h-80 bg-gray-100 dark:bg-gray-800 animate-pulse rounded-lg"></div>,
  ssr: false
});

const LazyStats = dynamic(() => import('@/components/stats').then(mod => ({ default: mod.Stats })), {
  loading: () => <div className="w-full h-48 bg-gray-100 dark:bg-gray-800 animate-pulse rounded-lg"></div>,
  ssr: false
});

const LazyGamingWhyChooseUs = dynamic(() => import('@/components/gaming/GamingWhyChooseUs'), {
  loading: () => <div className="w-full h-96 bg-gray-100 dark:bg-gray-800 animate-pulse rounded-lg"></div>,
  ssr: false
});

const LazyGamingDevelopmentProcess = dynamic(() => import('@/components/gaming/GamingDevelopmentProcess'), {
  loading: () => <div className="w-full h-96 bg-gray-100 dark:bg-gray-800 animate-pulse rounded-lg"></div>,
  ssr: false
});

const LazyTestimonialSlider = dynamic(() => import('@/components/TestimonialSlider'), {
  loading: () => <div className="w-full h-64 bg-gray-100 dark:bg-gray-800 animate-pulse rounded-lg"></div>,
  ssr: false
});

const LazyGamingAboutSection = dynamic(() => import('@/components/gaming/GamingAboutSection'), {
  loading: () => <div className="w-full h-96 bg-gray-100 dark:bg-gray-800 animate-pulse rounded-lg"></div>,
  ssr: false
});

const LazyGamingFAQSection = dynamic(() => import('@/components/gaming/GamingFAQSection'), {
  loading: () => <div className="w-full h-96 bg-gray-100 dark:bg-gray-800 animate-pulse rounded-lg"></div>,
  ssr: false
});



export const metadata: Metadata = {
  metadataBase: new URL('https://brtmultisoftware.com'),
  title: 'Game Development Services | Mobile & PC Game Development | BRT Multi Software',
  description: 'Professional game development services including mobile games, PC games, VR/AR games, and multiplayer solutions. Expert Unity & Unreal Engine developers. 150+ games developed.',
  keywords: [
    'Game Development',
    'Mobile Game Development',
    'PC Game Development',
    'Unity Development',
    'Unreal Engine',
    'VR Game Development',
    'AR Game Development',
    'Multiplayer Games',
    'Game Design',
    'BRT Multi Software',
    'Gaming Solutions',
    'India',
    'Game Studio'
  ],
  authors: [{ name: 'BRT Multi Software', url: 'https://brtmultisoftware.com/' }],
  creator: 'BRT Multi Software',
  robots: {
    index: true,
    follow: true,
    nocache: false,
    googleBot: {
      index: true,
      follow: true,
      noimageindex: false,
      'max-snippet': -1,
      'max-image-preview': 'large',
      'max-video-preview': -1,
    },
  },
  openGraph: {
    title: 'Game Development Services | Mobile & PC Game Development | BRT Multi Software',
    description: 'Professional game development services including mobile games, PC games, VR/AR games, and multiplayer solutions. Expert Unity & Unreal Engine developers.',
    url: 'https://brtmultisoftware.com/gaming-software',
    siteName: 'BRT Multi Software',
    images: [
      {
        url: 'https://brtmultisoftware.com/gaming/ludogame.png',
        width: 1200,
        height: 630,
        alt: 'Game Development by BRT Multi Software',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Game Development Services | Mobile & PC Game Development | BRT Multi Software',
    description: 'Professional game development services including mobile games, PC games, VR/AR games, and multiplayer solutions.',
    site: '@brtmultisoftware',
    images: ['https://brtmultisoftware.com/gaming/ludogame.png'],
  },
  alternates: {
    canonical: 'https://brtmultisoftware.com/gaming-software',
  },
};

export default function GamingSoftwarePage() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-slate-800 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800 text-foreground overflow-x-hidden">
      <SharedNavigation
        serviceType="gaming"
        brandName="BRT Gaming"
        brandColor="sky"
      />

      {/* Hero Section - Enhanced Responsive */}
      <section id="home" className="w-full min-h-[60vh] xs:min-h-[65vh] sm:min-h-[70vh] md:min-h-[75vh] lg:min-h-[80vh] xl:min-h-[85vh]">
        <Suspense fallback={<div className="w-full h-96 bg-gradient-to-br from-sky-100 to-purple-200 dark:from-slate-900 dark:to-purple-900 animate-pulse rounded-lg"></div>}>
          <LazyHeroSection />
        </Suspense>
      </section>

      {/* Gaming Next Level Section - Enhanced Responsive */}
      <section id="gaming-next-level" className="w-full py-4 xs:py-6 sm:py-8 md:py-12 lg:py-16">
        <Suspense fallback={<div className="w-full h-64 bg-sky-100 dark:bg-slate-800 animate-pulse rounded-lg"></div>}>
          <LazyGamingNextLevel />
        </Suspense>
      </section>

      {/* Featured Games Section - Enhanced Responsive */}
      <section id="featured-games" className="w-full py-4 xs:py-6 sm:py-8 md:py-12 lg:py-16">
        <Suspense fallback={<div className="w-full h-80 bg-sky-100 dark:bg-slate-800 animate-pulse rounded-lg"></div>}>
          <LazyFeaturedGames />
        </Suspense>
      </section>

      {/* Stats Section - Enhanced Responsive */}
      <section className="w-full py-4 xs:py-6 sm:py-8 md:py-10 lg:py-12">
        <Suspense fallback={<div className="w-full h-48 bg-sky-100 dark:bg-slate-800 animate-pulse rounded-lg"></div>}>
          <LazyStats stats={gamingStatsData} />
        </Suspense>
      </section>

      {/* Why Choose Us Section - Enhanced Responsive */}
      <section id="why-choose-us" className="w-full py-4 xs:py-6 sm:py-8 md:py-12 lg:py-16">
        <Suspense fallback={<div className="w-full h-96 bg-sky-100 dark:bg-slate-800 animate-pulse rounded-lg"></div>}>
          <LazyGamingWhyChooseUs />
        </Suspense>
      </section>

    




      {/* About Section - Enhanced Responsive */}
      <section id="about" className="w-full py-4 xs:py-6 sm:py-8 md:py-12 lg:py-16">
        <Suspense fallback={<div className="w-full h-96 bg-sky-100 dark:bg-slate-800 animate-pulse rounded-lg"></div>}>
          <LazyGamingAboutSection />
        </Suspense>
      </section>
      {/* Testimonials Section - Enhanced Responsive */}
      <section id="testimonials" className="w-full py-4 xs:py-6 sm:py-8 md:py-12 lg:py-16 bg-white dark:bg-slate-950">
        <Suspense fallback={<div className="w-full h-64 bg-white dark:bg-slate-900 animate-pulse rounded-lg border border-gray-200 dark:border-purple-700"></div>}>
          <LazyTestimonialSlider />
        </Suspense>
      </section>

      {/* FAQ Section - Enhanced Responsive */}
      <section id="faq" className="w-full py-4 xs:py-6 sm:py-8 md:py-12 lg:py-16">
        <Suspense fallback={<div className="w-full h-96 bg-sky-100 dark:bg-slate-800 animate-pulse rounded-lg"></div>}>
          <LazyGamingFAQSection />
        </Suspense>
      </section>


    </main>
  );
}