"use client";

import { MarketingNavigation } from "@/components/marketing-navigation";
import { ServiceHero, ServiceFeatures, ServicePricing, ServiceCTA } from "@/components/digital-marketing/shared";
import {
  MessageSquare, Heart, Users, Camera, Video, TrendingUp,
  Share2, Award, Globe, Smartphone, Star, Zap
} from "lucide-react";

export default function SocialMediaServicesPage() {
  const heroData = {
    title: "Social Media Marketing",
    subtitle: "That Builds Communities",
    description: "Build engaged communities, increase brand awareness, and drive conversions with our comprehensive social media marketing strategies across all major platforms.",
    features: [
      "Content creation and curation for all platforms",
      "Community management and engagement",
      "Influencer partnerships and collaborations",
      "Social media advertising campaigns",
      "Analytics and performance tracking"
    ],
    primaryCTA: "Boost Social Presence",
    secondaryCTA: "View Portfolio",
    badge: "📱 Social Media Excellence",
    stats: [
      { value: "500%", label: "Engagement Increase" },
      { value: "10M+", label: "Impressions Generated" },
      { value: "95%", label: "Client Satisfaction" },
      { value: "24/7", label: "Community Management" }
    ],
    backgroundGradient: "from-pink-900/10 via-purple-900/5 to-pink-900/10"
  };

  const features = [
    {
      icon: Camera,
      title: "Content Creation",
      description: "Engaging visual content that captures attention and drives engagement across all social platforms.",
      benefits: [
        "Professional photography",
        "Graphic design and templates",
        "Video content creation",
        "Story and reel production"
      ],
      popular: true
    },
    {
      icon: Users,
      title: "Community Management",
      description: "Build and nurture engaged communities with responsive, authentic social media management.",
      benefits: [
        "Daily posting and scheduling",
        "Real-time engagement",
        "Comment and message management",
        "Crisis management"
      ]
    },
    {
      icon: Star,
      title: "Influencer Marketing",
      description: "Partner with relevant influencers to expand your reach and build authentic brand connections.",
      benefits: [
        "Influencer identification",
        "Campaign strategy",
        "Partnership management",
        "Performance tracking"
      ]
    },
    {
      icon: TrendingUp,
      title: "Social Media Advertising",
      description: "Targeted social media ads that drive traffic, leads, and conversions across all platforms.",
      benefits: [
        "Facebook & Instagram ads",
        "LinkedIn B2B campaigns",
        "Twitter promoted content",
        "TikTok advertising"
      ]
    },
    {
      icon: Video,
      title: "Video Marketing",
      description: "Compelling video content that tells your brand story and drives engagement.",
      benefits: [
        "Short-form video content",
        "Live streaming setup",
        "Video editing and production",
        "YouTube optimization"
      ]
    },
    {
      icon: Share2,
      title: "Social Media Strategy",
      description: "Comprehensive social media strategies aligned with your business goals and target audience.",
      benefits: [
        "Platform-specific strategies",
        "Content calendar planning",
        "Hashtag research",
        "Competitor analysis"
      ]
    }
  ];

  const pricingTiers = [
    {
      name: "Social Starter",
      price: "$1,200",
      period: "month",
      description: "Perfect for small businesses starting their social media journey",
      features: [
        "2 social media platforms",
        "12 posts per month",
        "Basic graphic design",
        "Community management",
        "Monthly analytics report"
      ],
      cta: "Start Social Journey",
      badge: "Best for Startups"
    },
    {
      name: "Social Professional",
      price: "$2,800",
      period: "month",
      description: "Comprehensive social media management for growing businesses",
      features: [
        "4 social media platforms",
        "20 posts per month",
        "Custom graphic design",
        "Video content creation",
        "Daily community management",
        "Influencer outreach",
        "Bi-weekly reporting"
      ],
      popular: true,
      cta: "Scale Social Presence",
      badge: "Most Popular"
    },
    {
      name: "Social Enterprise",
      price: "$5,500",
      period: "month",
      description: "Advanced social media marketing for large businesses",
      features: [
        "All major platforms",
        "Daily posting (30+ posts)",
        "Professional video production",
        "Influencer partnerships",
        "Social media advertising",
        "24/7 community management",
        "Dedicated account manager",
        "Weekly reporting",
        "Crisis management"
      ],
      cta: "Dominate Social Media",
      badge: "Maximum Impact"
    }
  ];

  const ctaData = {
    title: "Ready to Build Your Social Community?",
    subtitle: "Transform your social media presence and build engaged communities that drive real business results.",
    primaryCTA: "Get Social Media Audit",
    secondaryCTA: "Schedule Consultation",
    features: [
      "Free social media audit",
      "Custom content strategy",
      "Results within 30 days"
    ],
    backgroundGradient: "from-pink-600 via-purple-700 to-pink-700"
  };

  return (
    <main className="min-h-screen">
      <MarketingNavigation />
      <ServiceHero {...heroData} />
      <ServiceFeatures
        title="Complete Social Media Solutions"
        subtitle="Build engaged communities and drive conversions with our comprehensive social media services"
        features={features}
      />
      <ServicePricing
        title="Social Media Packages"
        subtitle="Choose the perfect social media package to grow your online community"
        tiers={pricingTiers}
      />
      <ServiceCTA {...ctaData} />
    </main>
  );
}
