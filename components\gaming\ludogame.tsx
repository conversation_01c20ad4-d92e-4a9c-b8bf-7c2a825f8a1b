import Image from 'next/image';
import Link from 'next/link';
import { Users, Trophy, ArrowRight } from 'lucide-react';
import { gamesData } from '@/components/data/games-data';

export default function FeaturedGames() {
  return (
    <section className="relative w-full py-20 bg-white dark:bg-slate-950 transition-colors duration-300 overflow-hidden">
      {/* Enhanced Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Light Mode - Subtle Blue Elements */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-sky-50 dark:bg-sky-900/30 rounded-full opacity-60 animate-float border border-sky-100 dark:border-sky-800"></div>
        <div className="absolute bottom-20 right-10 w-24 h-24 bg-purple-100 dark:bg-purple-800/30 rounded-full opacity-60 animate-float border border-purple-200 dark:border-purple-700" style={{ animationDelay: '2s' }}></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-emerald-200 dark:bg-emerald-700/30 rounded-full opacity-60 animate-float border border-emerald-300 dark:border-emerald-600" style={{ animationDelay: '4s' }}></div>

        {/* Additional Gaming Theme Elements */}
        <div className="absolute top-1/3 right-1/3 w-20 h-20 bg-sky-50 dark:bg-sky-800/20 rounded-full opacity-40 animate-float" style={{ animationDelay: '6s' }}></div>
        <div className="absolute bottom-1/3 left-1/3 w-12 h-12 bg-purple-100 dark:bg-purple-700/20 rounded-full opacity-40 animate-float" style={{ animationDelay: '8s' }}></div>
      </div>
      <div className="container mx-auto px-4 relative z-10">
        {/* Enhanced Header Section */}
        <div className="text-center mb-16 animate-fade-in-up">
          <div className="inline-flex items-center gap-3 px-6 py-3 bg-white dark:bg-slate-800 rounded-full mb-8 shadow-lg hover:shadow-xl transition-all duration-300 animate-bounce border-2 border-sky-200 dark:border-sky-600" style={{ animationDuration: '3s' }}>
            <Trophy className="w-6 h-6 text-sky-500 dark:text-sky-300 animate-pulse" />
            <span className="text-lg font-black text-sky-600 dark:text-white">Featured Games</span>
          </div>

          <h2 className="text-4xl md:text-5xl lg:text-6xl font-black text-gray-900 dark:text-white mb-8 leading-tight animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
            Our Premium Game
            <br />
            <span className="text-sky-500 dark:text-sky-400 animate-shimmer">Collection</span>
          </h2>

          <div className="w-32 h-2 bg-sky-500 dark:bg-sky-400 rounded-full mx-auto mb-8 animate-fade-in-up" style={{ animationDelay: '0.4s' }}></div>

          <p className="text-xl font-semibold text-gray-800 dark:text-white max-w-4xl mx-auto mb-10 leading-relaxed animate-fade-in-up" style={{ animationDelay: '0.6s' }}>
            Discover our handpicked selection of premium games designed to captivate players and drive engagement.
            Each game is crafted with cutting-edge technology and innovative gameplay mechanics.
          </p>

          <Link
            href="/product"
            className="inline-flex items-center gap-3 bg-sky-500 hover:bg-sky-600 dark:bg-sky-400 dark:hover:bg-sky-500 text-white font-bold px-10 py-5 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 hover:scale-105 group animate-fade-in-up"
            style={{ animationDelay: '0.8s' }}
          >
            <span className="text-lg">Explore All Games</span>
            <ArrowRight className="w-6 h-6 group-hover:translate-x-2 transition-transform duration-300" />
          </Link>
        </div>

        {/* Balanced Game Cards Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {gamesData.map((game, idx) => (
            <Link
              key={game.id}
              href={`/games/${game.id}`}
              className="group relative bg-white dark:bg-slate-900 rounded-xl shadow-lg hover:shadow-xl border border-gray-200 dark:border-slate-700 transition-all duration-500 hover:-translate-y-3 hover:scale-105 overflow-hidden animate-fade-in-up cursor-pointer block"
              style={{ animationDelay: `${0.1 + idx * 0.1}s` }}
            >
              {/* Highlight Badge */}
              <div className="absolute top-3 right-3 z-10">
                <div className={`px-3 py-1 text-white text-xs font-black rounded-lg shadow-lg border-2 border-white ${
                  game.color === 'sky' ? 'bg-sky-500 dark:bg-sky-400 dark:border-sky-300' :
                  game.color === 'purple' ? 'bg-purple-500 dark:bg-purple-400 dark:border-purple-300' :
                  'bg-emerald-500 dark:bg-emerald-400 dark:border-emerald-300'
                }`}>
                  {game.highlight}
                </div>
              </div>

              {/* Card Content */}
              <div className="relative p-4 h-full flex flex-col">
                {/* Game Image */}
                <div className="relative w-20 h-20 mx-auto mb-4 group-hover:scale-125 group-hover:rotate-6 transition-all duration-700 transform-gpu">
                  <div className="absolute inset-0 bg-gray-50 dark:bg-slate-800 rounded-xl shadow-md group-hover:shadow-2xl transition-shadow duration-700 animate-pulse group-hover:animate-none border border-gray-200 dark:border-slate-600"></div>
                  <div className={`absolute inset-0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-700 animate-pulse ${
                    game.color === 'sky' ? 'bg-sky-500/10 dark:bg-sky-400/20' :
                    game.color === 'purple' ? 'bg-purple-500/10 dark:bg-purple-400/20' :
                    'bg-emerald-500/10 dark:bg-emerald-400/20'
                  }`}></div>
                  <div className="relative p-3 w-full h-full">
                    <Image
                      src={game.img}
                      alt={game.name}
                      fill
                      className="object-contain drop-shadow-lg group-hover:drop-shadow-2xl transition-all duration-500"
                    />
                  </div>

                  {/* Floating Particles */}
                  <div className={`absolute -top-1 -right-1 w-3 h-3 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-500 animate-bounce ${
                    game.color === 'sky' ? 'bg-sky-500 dark:bg-sky-400' :
                    game.color === 'purple' ? 'bg-purple-500 dark:bg-purple-400' :
                    'bg-emerald-500 dark:bg-emerald-400'
                  }`}></div>
                  <div className={`absolute -bottom-1 -left-1 w-2 h-2 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-500 animate-bounce ${
                    game.color === 'sky' ? 'bg-sky-600 dark:bg-sky-300' :
                    game.color === 'purple' ? 'bg-purple-600 dark:bg-purple-300' :
                    'bg-emerald-600 dark:bg-emerald-300'
                  }`} style={{ animationDelay: '0.3s' }}></div>
                </div>

                {/* Game Title */}
                <h3 className={`text-lg font-black text-gray-900 dark:text-white text-center mb-3 transition-all duration-500 group-hover:scale-105 transform ${
                  game.color === 'sky' ? 'group-hover:text-sky-500 dark:group-hover:text-sky-400' :
                  game.color === 'purple' ? 'group-hover:text-purple-500 dark:group-hover:text-purple-400' :
                  'group-hover:text-emerald-500 dark:group-hover:text-emerald-400'
                }`}>
                  {game.name}
                </h3>

                {/* Category */}
                <div className="inline-flex items-center gap-2 px-3 py-1 bg-blue-100 dark:bg-blue-800 rounded-lg mx-auto mb-3 group-hover:scale-105 transition-transform duration-300 shadow-md hover:shadow-lg border border-blue-200 dark:border-blue-600">
                  <div className="w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-pulse"></div>
                  <span className="text-sm font-black text-blue-900 dark:text-white">{game.category}</span>
                </div>

                {/* Players */}
                <div className="flex items-center justify-center gap-2 text-gray-800 dark:text-white mb-4 group-hover:scale-105 transition-transform duration-300">
                  <Users className="w-4 h-4 group-hover:animate-pulse text-blue-600 dark:text-blue-400" />
                  <span className="text-sm font-bold">{game.players}</span>
                </div>

                {/* Features */}
                <div className="mb-4">
                  <div className="flex flex-wrap gap-2 justify-center">
                    {game.features.slice(0, 2).map((feature, featureIdx) => (
                      <span
                        key={featureIdx}
                        className="px-3 py-1 bg-gray-200 dark:bg-blue-700 text-gray-900 dark:text-white text-xs rounded-lg font-black hover:scale-105 transition-transform duration-300 cursor-pointer shadow-md border border-gray-300 dark:border-blue-500"
                        style={{ animationDelay: `${featureIdx * 0.1}s` }}
                      >
                        {feature}
                      </span>
                    ))}
                  </div>
                </div>

                {/* CTA */}
                <div className="mt-auto">
                  <button className="w-full py-3 bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white font-black rounded-lg transition-all duration-500 hover:scale-110 hover:-translate-y-1 text-sm shadow-lg hover:shadow-2xl group-hover:animate-pulse border-2 border-white/20">
                    Get Quote
                  </button>
                </div>
              </div>

              {/* Enhanced Hover Effects */}
              <div className={`absolute inset-0 bg-${game.color}-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-700 rounded-xl`}></div>

              {/* Animated Border */}
              <div className={`absolute inset-0 rounded-xl border-2 border-transparent group-hover:border-${game.color}-400 transition-all duration-700 animate-pulse`}></div>

              {/* Glow Effect */}
              <div className={`absolute inset-0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-700 shadow-2xl shadow-${game.color}-500/30`}></div>

              {/* Corner Accents */}
              <div className={`absolute top-0 right-0 w-8 h-8 bg-${game.color}-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-tr-xl rounded-bl-xl`}></div>
              <div className={`absolute bottom-0 left-0 w-6 h-6 bg-${game.color}-500/25 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-bl-xl rounded-tr-xl`}></div>

              {/* Ripple Effect */}
              <div className="absolute inset-0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className={`absolute inset-0 bg-${game.color}-500/20 rounded-xl animate-ping`} style={{ animationDuration: '2s' }}></div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}
