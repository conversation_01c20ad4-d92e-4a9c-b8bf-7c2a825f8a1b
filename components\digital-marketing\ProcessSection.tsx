"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Target, Rocket, Activity, BarChart3 } from "lucide-react";

interface ProcessStep {
  step: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
}

const process: ProcessStep[] = [
  {
    step: "01",
    title: "Strategy & Planning",
    description: "We analyze your business, competitors, and target audience to create a comprehensive digital marketing strategy.",
    icon: Target
  },
  {
    step: "02", 
    title: "Campaign Setup",
    description: "Our experts set up and optimize your campaigns across all relevant platforms and channels.",
    icon: Rocket
  },
  {
    step: "03",
    title: "Execution & Monitoring",
    description: "We launch your campaigns and continuously monitor performance to ensure optimal results.",
    icon: Activity
  },
  {
    step: "04",
    title: "Analysis & Optimization",
    description: "Regular analysis and optimization ensure your campaigns continue to improve and deliver better ROI.",
    icon: BarChart3
  }
];

export function ProcessSection() {
  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8 bg-muted/20">
      <div className="container mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 hero-title animate-fade-in-up">
            Our Proven Process
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto animate-fade-in-up">
            A systematic approach to digital marketing that ensures consistent results and continuous growth for your business.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {process.map((step, index) => (
            <div key={index} className="relative">
              {/* Connection Line */}
              {index < process.length - 1 && (
                <div className="hidden lg:block absolute top-16 left-full w-full h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 z-0"></div>
              )}
              
              <Card className="service-card hover:scale-105 transition-all duration-300 group relative z-10 animate-fade-in-up"
                    style={{ animationDelay: `${index * 0.2}s` }}>
                <CardContent className="p-8 text-center">
                  <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 mb-6 group-hover:shadow-xl transition-all duration-300">
                    <step.icon className="h-8 w-8 text-white" />
                  </div>
                  
                  <div className="text-3xl font-bold text-blue-500 mb-2">{step.step}</div>
                  <h3 className="text-xl font-bold mb-4 group-hover:text-blue-500 transition-colors">
                    {step.title}
                  </h3>
                  <p className="text-muted-foreground leading-relaxed">
                    {step.description}
                  </p>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
