'use client';

import React from 'react';
import { Gamepad2, Trophy, Users, Zap, Target, Rocket, Star, Award, Code, Palette } from 'lucide-react';
import Image from 'next/image';



const aboutFeatures = [
  {
    icon: Gamepad2,
    title: 'Innovate Games',
    description: 'Creating cutting-edge gaming experiences with innovative mechanics and engaging gameplay.',
    color: '#0EA5E9', // sky-500 (plasma blue)
    delay: 0.1
  },
  {
    icon: Trophy,
    title: 'Award Winning',
    description: 'Our games have won multiple industry awards and recognition for excellence.',
    color: '#A855F7', // purple-500 (laser purple)
    delay: 0.2
  },
  {
    icon: Users,
    title: 'Player Focused',
    description: 'Designing games with player experience and engagement at the forefront.',
    color: '#10B981', // emerald-500 (energy green)
    delay: 0.3
  },
  {
    icon: Zap,
    title: 'High Performance',
    description: 'Optimized games that deliver smooth 60+ FPS across all platforms.',
    color: '#0EA5E9', // sky-500 (plasma blue)
    delay: 0.4
  },
  {
    icon: Target,
    title: 'Precision Development',
    description: 'Meticulous attention to detail in every aspect of game development.',
    color: '#A855F7', // purple-500 (laser purple)
    delay: 0.5
  },
  {
    icon: Rocket,
    title: 'Level Up',
    description: 'Continuously pushing boundaries to take gaming to the next level.',
    color: '#10B981', // emerald-500 (energy green)
    delay: 0.6
  }
];

const roadmapItems = [
  {
    icon: Code,
    title: 'Game Innovation',
    description: 'Developing innovative game mechanics and unique gameplay experiences',
    features: ['AI-powered NPCs', 'Procedural generation', 'Dynamic storytelling', 'Cross-platform play']
  },
  {
    icon: Palette,
    title: 'Advanced Development',
    description: 'Using cutting-edge technology and development practices',
    features: ['Unity & Unreal Engine', 'Cloud gaming', 'VR/AR integration', 'Blockchain gaming']
  },
  {
    icon: Users,
    title: 'Player Engagement',
    description: 'Creating immersive experiences that keep players coming back',
    features: ['Social features', 'Live events', 'Community building', 'Player analytics']
  }
];

const GamingAboutSection: React.FC = () => {
  return (
    <section className="relative py-12 xs:py-16 sm:py-20 md:py-24 lg:py-28 px-2 xs:px-3 sm:px-4 overflow-hidden bg-white dark:bg-slate-950">

      {/* Enhanced Colorful Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Colorful Floating Circles */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-sky-100 dark:bg-sky-800/40 rounded-full opacity-70 animate-float border-2 border-sky-300 dark:border-sky-600 shadow-lg"></div>
        <div className="absolute bottom-20 right-10 w-24 h-24 bg-purple-100 dark:bg-purple-800/40 rounded-full opacity-70 animate-float border-2 border-purple-300 dark:border-purple-600 shadow-lg" style={{ animationDelay: '2s' }}></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-emerald-100 dark:bg-emerald-800/40 rounded-full opacity-70 animate-float border-2 border-emerald-300 dark:border-emerald-600 shadow-lg" style={{ animationDelay: '4s' }}></div>
        <div className="absolute top-1/3 right-1/4 w-20 h-20 bg-orange-100 dark:bg-orange-800/40 rounded-full opacity-70 animate-float border-2 border-orange-300 dark:border-orange-600 shadow-lg" style={{ animationDelay: '6s' }}></div>

        {/* Colorful Gaming Icons */}
        <div className="absolute top-20 right-20 w-10 h-10 text-sky-500 dark:text-sky-400 animate-bounce drop-shadow-lg" style={{ animationDuration: '3s' }}>
          <Gamepad2 className="w-full h-full" />
        </div>
        <div className="absolute bottom-1/3 left-20 w-8 h-8 text-purple-500 dark:text-purple-400 animate-bounce drop-shadow-lg" style={{ animationDuration: '4s', animationDelay: '1s' }}>
          <Trophy className="w-full h-full" />
        </div>
        <div className="absolute top-1/3 right-1/3 w-9 h-9 text-emerald-500 dark:text-emerald-400 animate-bounce drop-shadow-lg" style={{ animationDuration: '5s', animationDelay: '2s' }}>
          <Target className="w-full h-full" />
        </div>
        <div className="absolute bottom-1/4 right-1/5 w-7 h-7 text-orange-500 dark:text-orange-400 animate-bounce drop-shadow-lg" style={{ animationDuration: '6s', animationDelay: '3s' }}>
          <Zap className="w-full h-full" />
        </div>

        {/* Particle System */}
        <div className="absolute top-10 right-10 w-2 h-2 bg-sky-400/60 dark:bg-sky-300/50 rounded-full animate-particle" style={{ animationDelay: '0s' }}></div>
        <div className="absolute top-20 right-32 w-1.5 h-1.5 bg-purple-400/60 dark:bg-purple-300/50 rounded-full animate-particle" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-32 right-16 w-1 h-1 bg-emerald-400/60 dark:bg-emerald-300/50 rounded-full animate-particle" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="relative container-responsive">
        {/* Enhanced Header Design */}
        <div className="relative text-center mb-12 xs:mb-16 sm:mb-20 md:mb-24 animate-fade-in-up">
          {/* Professional Hero Badge */}
          <div className="inline-flex items-center gap-3 px-6 py-3 mb-8 bg-white dark:bg-slate-900 rounded-full border-2 border-sky-200 dark:border-sky-700 shadow-lg hover:shadow-xl transition-all duration-500 group">
            <Star className="w-6 h-6 text-sky-500 dark:text-sky-400 animate-pulse" />
            <span className="text-base font-bold text-gray-800 dark:text-white group-hover:text-sky-500 dark:group-hover:text-sky-400 transition-colors duration-300">
              Premium Gaming Solutions
            </span>
            <div className="w-3 h-3 bg-sky-500 dark:bg-sky-400 rounded-full animate-bounce"></div>
          </div>

          {/* Advanced Typography with Effects */}
          <div className="relative mb-10">
            <h1 className="text-4xl xs:text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-black mb-6 leading-tight">
              <span className="block text-gray-900 dark:text-white mb-2">
                About
              </span>
              <span className="block text-blue-600 dark:text-blue-400">
                BRT Gaming
              </span>
            </h1>

            {/* Simple Underline */}
            <div className="flex justify-center">
              <div className="w-32 h-1 bg-blue-600 dark:bg-blue-400 rounded-full"></div>
            </div>
          </div>

          {/* Advanced Glassmorphism Description */}
          <div className="relative max-w-5xl mx-auto mb-12">
            <div className="glass-effect dark:glass-dark rounded-3xl p-8 xs:p-10 sm:p-12 border border-white/30 dark:border-gray-600/30 shadow-2xl hover:shadow-3xl transition-all duration-500 group animate-magnetic">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-purple-500/5 to-cyan-500/10 dark:from-blue-400/8 dark:via-purple-400/4 dark:to-cyan-400/8 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 animate-aurora"></div>
              <p className="relative text-lg xs:text-xl sm:text-2xl text-gray-800 dark:text-gray-100 leading-relaxed font-medium group-hover:text-gray-900 dark:group-hover:text-gray-50 transition-colors duration-300">
                <span className="hidden sm:inline">
                  BRT Multi Software is a <span className="text-blue-600 dark:text-blue-400 font-bold animate-shimmer">game development company</span> specializing in creating innovative and engaging experiences. Our team is dedicated to delivering high-quality solutions that are tailored to your needs. From strategy-based games to interactive multiplayer creations, we focus on every detail to ensure exceptional results.
                </span>
                <span className="sm:hidden">
                  Creating <span className="text-blue-600 dark:text-blue-400 font-bold animate-shimmer">innovative gaming experiences</span> with cutting-edge technology
                </span>
              </p>

              {/* Floating Particles in Description */}
              <div className="absolute top-4 right-4 w-2 h-2 bg-blue-400/60 dark:bg-blue-300/50 rounded-full animate-particle opacity-0 group-hover:opacity-100"></div>
              <div className="absolute bottom-4 left-4 w-1.5 h-1.5 bg-purple-400/60 dark:bg-purple-300/50 rounded-full animate-particle opacity-0 group-hover:opacity-100" style={{ animationDelay: '1s' }}></div>
              <div className="absolute top-1/2 right-8 w-1 h-1 bg-cyan-400/60 dark:bg-cyan-300/50 rounded-full animate-particle opacity-0 group-hover:opacity-100" style={{ animationDelay: '2s' }}></div>
            </div>
          </div>

          {/* Enhanced Colorful Stats Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 xs:gap-8 max-w-4xl mx-auto">
            {[
              { number: '150+', label: 'Games Developed', icon: Gamepad2, color: 'blue' },
              { number: '5M+', label: 'Active Players', icon: Users, color: 'purple' },
              { number: '50+', label: 'Industry Awards', icon: Trophy, color: 'green' }
            ].map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <div key={index} className="group relative bg-white dark:bg-blue-900 rounded-2xl p-6 border border-gray-200 dark:border-blue-700 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 overflow-hidden" style={{ animationDelay: `${index * 0.2}s` }}>
                  {/* Enhanced Side Color Box */}
                  <div className={`absolute -right-2 top-0 bottom-0 w-8 bg-gradient-to-b from-${stat.color}-400 via-${stat.color}-500 to-${stat.color}-600 dark:from-${stat.color}-500 dark:via-${stat.color}-600 dark:to-${stat.color}-700 rounded-l-2xl opacity-80 group-hover:opacity-100 transition-opacity duration-300 shadow-lg`}></div>

                  {/* Simple Icon Container */}
                  <div className="flex items-center justify-center mb-4 relative z-10">
                    <div className={`w-16 h-16 rounded-xl bg-${stat.color}-100 dark:bg-${stat.color}-800 flex items-center justify-center`}>
                      <IconComponent className={`w-8 h-8 text-${stat.color}-600 dark:text-${stat.color}-400`} />
                    </div>
                  </div>

                  {/* Number Display */}
                  <div className={`text-3xl font-bold text-${stat.color}-600 dark:text-${stat.color}-400 mb-2 text-center relative z-10`}>
                    {stat.number}
                  </div>

                  {/* Label */}
                  <div className="text-sm font-medium text-gray-700 dark:text-white text-center relative z-10">
                    {stat.label}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Enhanced Colorful Features Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 xs:gap-8 sm:gap-10 mb-16 xs:mb-20 sm:mb-24 md:mb-28">
          {aboutFeatures.map((feature, index) => {
            const IconComponent = feature.icon;
            const colors = ['blue', 'purple', 'green', 'orange', 'red', 'indigo'];
            const cardColor = colors[index % colors.length];

            return (
              <div
                key={index}
                className="group relative bg-white dark:bg-blue-900 rounded-2xl p-6 shadow-lg hover:shadow-xl border border-gray-200 dark:border-blue-700 transition-all duration-300 hover:-translate-y-2 animate-fade-in-up overflow-hidden"
                style={{ animationDelay: `${feature.delay}s` }}
              >
                {/* Enhanced Left Side Color Box */}
                <div className={`absolute -left-2 top-0 bottom-0 w-6 bg-gradient-to-b from-${cardColor}-400 via-${cardColor}-500 to-${cardColor}-600 dark:from-${cardColor}-500 dark:via-${cardColor}-600 dark:to-${cardColor}-700 rounded-r-2xl opacity-80 group-hover:opacity-100 transition-opacity duration-300 shadow-lg`}></div>

                {/* Simple Icon Container */}
                <div className={`mb-4 w-16 h-16 mx-auto flex items-center justify-center rounded-xl bg-${cardColor}-100 dark:bg-${cardColor}-800 relative z-10`}>
                  <IconComponent className={`w-8 h-8 text-${cardColor}-600 dark:text-${cardColor}-400`} />
                </div>

                {/* Content */}
                <div className="text-center relative z-10">
                  <h3 className={`text-lg font-bold mb-3 text-gray-900 dark:text-white group-hover:text-${cardColor}-600 dark:group-hover:text-${cardColor}-400 transition-colors duration-300`}>
                    {feature.title}
                  </h3>

                  <p className="text-gray-600 dark:text-gray-200 leading-relaxed text-sm">
                    {feature.description}
                  </p>
                </div>
              </div>
            );
          })}
        </div>

        {/* Main About Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 xs:gap-10 sm:gap-12 items-center mb-12 xs:mb-14 sm:mb-16 md:mb-20">
          {/* Enhanced Image Section */}
          <div className="relative animate-fade-in-up" style={{ animationDelay: '0.7s' }}>
            <div className="relative bg-white dark:bg-blue-900 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-blue-700 overflow-hidden">
              {/* Enhanced Side Color Strip */}
              <div className="absolute top-0 left-0 bottom-0 w-2 bg-gradient-to-b from-blue-400 via-blue-500 to-blue-600 dark:from-blue-500 dark:via-blue-600 dark:to-blue-700 shadow-lg"></div>

              {/* Main Gaming Image */}
              <div className="relative mb-6 group">
                <Image
                  src="/gaming/about.png"
                  alt="Gaming Development"
                  width={400}
                  height={300}
                  className="w-full h-48 object-cover rounded-xl shadow-md group-hover:scale-105 transition-all duration-300"
                  priority
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent rounded-xl"></div>
                <div className="absolute bottom-3 left-3 right-3 text-white">
                  <h3 className="text-lg font-bold mb-1">Game Innovation</h3>
                  <p className="text-sm text-gray-200">Advanced Development & Player Engagement</p>
                </div>
              </div>

              {/* Gaming Icons Grid */}
              <div className="grid grid-cols-3 gap-3">
                {[
                  { src: "/gaming/ludogame.png", alt: "Ludo Game", name: "Ludo", color: "blue" },
                  { src: "/gaming/teenpatti.png", alt: "Teen Patti Game", name: "Teen Patti", color: "purple" },
                  { src: "/gaming/rumygame.png", alt: "Rummy Game", name: "Rummy", color: "green" }
                ].map((game, index) => (
                  <div key={index} className="text-center group">
                    <div className="relative">
                      <div className={`w-12 h-12 mx-auto rounded-lg bg-${game.color}-100 dark:bg-${game.color}-800 flex items-center justify-center group-hover:scale-110 transition-all duration-300 border border-${game.color}-200 dark:border-${game.color}-600`}>
                        <Image
                          src={game.src}
                          alt={game.alt}
                          width={32}
                          height={32}
                          className="w-8 h-8 rounded-md"
                        />
                      </div>
                    </div>
                    <p className={`text-xs text-${game.color}-700 dark:text-${game.color}-300 mt-2 font-medium`}>{game.name}</p>
                  </div>
                ))}
              </div>

              {/* Stats Row */}
              <div className="grid grid-cols-3 gap-4 mt-6 pt-4 border-t border-gray-200 dark:border-blue-700">
                <div className="text-center">
                  <div className="text-lg font-bold text-blue-600 dark:text-blue-400">150+</div>
                  <div className="text-xs text-gray-600 dark:text-gray-300">Games</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-purple-600 dark:text-purple-400">5M+</div>
                  <div className="text-xs text-gray-600 dark:text-gray-300">Players</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-green-600 dark:text-green-400">50+</div>
                  <div className="text-xs text-gray-600 dark:text-gray-300">Awards</div>
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Roadmap Section */}
          <div className="animate-fade-in-up" style={{ animationDelay: '0.8s' }}>
            {/* Enhanced Roadmap Header */}
            <div className="text-center mb-12">
              <div className="inline-flex items-center gap-2 px-4 py-2 mb-4 bg-blue-50 dark:bg-blue-900/30 rounded-full border border-blue-200 dark:border-blue-700">
                <Star className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                <span className="text-sm font-semibold text-blue-700 dark:text-blue-300">Our Roadmap</span>
              </div>

              <h3 className="text-3xl xs:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Game <span className="text-blue-600 dark:text-blue-400">Development</span> Journey
              </h3>

              <p className="text-gray-600 dark:text-gray-300 leading-relaxed max-w-2xl mx-auto mb-6">
                Partner with BRT to turn your game ideas into reality and unlock rewarding opportunities for growth.
              </p>

              <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-100 dark:bg-blue-800 rounded-full">
                <Award className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                <span className="text-blue-700 dark:text-blue-300 font-medium text-sm">Earn Great Rewards with BRT</span>
              </div>
            </div>

            {/* Square Roadmap Cards Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-5xl mx-auto">
              {roadmapItems.map((item, index) => {
                const IconComponent = item.icon;
                const colors = ['blue', 'purple', 'green'];
                const cardColor = colors[index % colors.length];

                return (
                  <div key={index} className="group relative bg-white dark:bg-blue-900 rounded-2xl p-6 shadow-lg hover:shadow-xl border border-gray-200 dark:border-blue-700 transition-all duration-300 hover:-translate-y-2 hover:scale-105 overflow-hidden aspect-square flex flex-col">
                    {/* Top Color Strip */}
                    <div className={`absolute top-0 left-0 right-0 h-2 bg-${cardColor}-500 dark:bg-${cardColor}-600 rounded-t-2xl`}></div>

                    {/* Enhanced Icon Container */}
                    <div className="flex items-center justify-center mb-4 mt-2 relative z-10">
                      <div className={`relative w-16 h-16 rounded-xl bg-${cardColor}-100 dark:bg-${cardColor}-800 flex items-center justify-center group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 border border-${cardColor}-200 dark:border-${cardColor}-600 shadow-md`}>
                        <IconComponent className={`w-8 h-8 text-${cardColor}-600 dark:text-${cardColor}-400`} />
                      </div>
                    </div>

                    {/* Enhanced Content */}
                    <div className="text-center relative z-10 flex-1 flex flex-col justify-between">
                      <div className="flex-1">
                        <h4 className={`text-lg font-bold mb-3 text-gray-900 dark:text-white group-hover:text-${cardColor}-600 dark:group-hover:text-${cardColor}-400 transition-colors duration-300 leading-tight`}>
                          {item.title}
                        </h4>

                        <p className="text-gray-600 dark:text-gray-200 leading-relaxed mb-4 text-xs font-medium line-clamp-3">
                          {item.description}
                        </p>
                      </div>

                      {/* Compact Features List */}
                      <div className="space-y-2 mb-4">
                        {item.features.slice(0, 3).map((feature, featureIndex) => (
                          <div key={featureIndex} className={`flex items-center gap-2 p-2 bg-${cardColor}-50 dark:bg-${cardColor}-900/20 rounded-lg transition-all duration-300`}>
                            <div className={`w-1.5 h-1.5 bg-${cardColor}-500 rounded-full flex-shrink-0`}></div>
                            <span className={`text-${cardColor}-700 dark:text-${cardColor}-300 font-medium text-xs leading-tight truncate`}>
                              {feature}
                            </span>
                          </div>
                        ))}
                        {item.features.length > 3 && (
                          <div className={`text-${cardColor}-600 dark:text-${cardColor}-400 text-xs font-medium text-center`}>
                            +{item.features.length - 3} more
                          </div>
                        )}
                      </div>

                      {/* Compact Progress Bar */}
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden">
                        <div className={`h-full bg-${cardColor}-600 dark:bg-${cardColor}-400 transition-all duration-1000 group-hover:w-full w-0 rounded-full`}></div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

       
      </div>
    </section>
  );
};

export default GamingAboutSection;
