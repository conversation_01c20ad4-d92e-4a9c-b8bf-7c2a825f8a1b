"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Code, Gamepad2, TrendingUp, Palette } from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { useState } from "react";

const services = [
  {
    id: "software-development",
    title: "Software Development",
    icon: Code,
    href: "/software-development",
    color: "from-blue-500 to-blue-700",
    hoverImage: "https://brtmultisoftware.com/img/Software-animation.gif"
  },
  {
    id: "gaming-software",
    title: "Gaming Software",
    icon: Gamepad2,
    href: "/gaming-software",
    color: "from-blue-500 to-blue-700",
    hoverImage: "https://brtmultisoftware.com/img/game-animated.gif"
  },
  {
    id: "digital-marketing",
    title: "Digital Marketing", 
    icon: TrendingUp,
    href: "/digital-marketing",
    color: "from-blue-500 to-blue-700",
    hoverImage: "https://brtmultisoftware.com/img/digital-animation.gif"
  },
  {
    id: "graphics-design",
    title: "Graphics Design",
    icon: Palette,
    href: "/graphics-design",
    color: "from-blue-500 to-blue-700",
    hoverImage: "https://brtmultisoftware.com/img/graphics-animation.gif"
  }
];

export default function Home() {
  const [hoveredCard, setHoveredCard] = useState<string | null>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  const handleMouseMove = (e: React.MouseEvent) => {
    setMousePosition({ x: e.clientX, y: e.clientY });
  };

  return (
    <main
      className="relative h-screen flex items-center justify-center overflow-hidden"
      onMouseMove={handleMouseMove}
    >
      {/* Video Background */}
      <video
        autoPlay
        muted
        loop
        playsInline
        className="absolute inset-0 w-full h-full object-cover z-0"
      >
        <source src="https://brtmultisoftware.com/video/tech-2.mp4" type="video/mp4" />
      </video>
      
      {/* Overlay with mouse follow effect */}
      <div className="absolute inset-0 bg-black/60 z-10" />

      {/* Floating cursor glow effect */}
      <div
        className="absolute w-96 h-96 rounded-full bg-gradient-to-r from-blue-500/20 to-purple-500/20 blur-3xl pointer-events-none z-15 transition-all duration-300 ease-out"
        style={{
          left: mousePosition.x - 192,
          top: mousePosition.y - 192,
        }}
      />
      
      {/* Logo */}
      <div className="absolute top-8 left-8 z-30 flex items-center space-x-3">
        <Image
          src="https://brtmultisoftware.com/img/BRTLOGO.png"
          alt="BRT Logo"
          width={50}
          height={50}
          className="h-12 w-auto"
        />
        <span className="text-2xl font-bold text-white">BRT</span>
      </div>
      
      {/* Services Cards - 4 in one row */}
      <div className="relative z-20 flex gap-8 px-8 max-w-7xl mx-auto">
        {services.map((service) => (
          <Link key={service.id} href={service.href} className="flex-1 min-w-[260px] max-w-[340px]">
            <Card
              className="bg-black/30 backdrop-blur-md border border-white/20 hover:scale-110 hover:bg-black/40 hover:shadow-2xl hover:shadow-blue-500/25 hover:border-blue-400/50 transition-all duration-500 cursor-pointer group relative overflow-hidden min-h-[340px] hover:rotate-1 hover:-translate-y-2"
              onMouseEnter={() => setHoveredCard(service.id)}
              onMouseLeave={() => setHoveredCard(null)}
            >
              <CardContent className="p-12 text-center relative">
                {/* Animated background particles */}
                <div className="absolute inset-0 opacity-20">
                  <div className="absolute top-4 left-4 w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                  <div className="absolute top-8 right-6 w-1 h-1 bg-purple-400 rounded-full animate-ping"></div>
                  <div className="absolute bottom-6 left-8 w-1.5 h-1.5 bg-cyan-400 rounded-full animate-bounce"></div>
                  <div className="absolute bottom-4 right-4 w-1 h-1 bg-pink-400 rounded-full animate-pulse"></div>
                </div>
                <div className="flex justify-center mb-8">
                  {/* Show image by default, replace with icon on hover */}
                  {hoveredCard === service.id ? (
                    <div className={`p-8 rounded-2xl bg-gradient-to-br ${service.color} shadow-2xl group-hover:shadow-3xl transition-all duration-500 animate-pulse`}>
                      <service.icon className="h-16 w-16 text-white animate-bounce" />
                    </div>
                  ) : (
                    <div className="w-40 h-40 rounded-2xl overflow-hidden shadow-2xl transform transition-all duration-500 hover:scale-105">
                      <Image
                        src={service.hoverImage}
                        alt={`${service.title} Animation`}
                        width={160}
                        height={160}
                        className="w-full h-full object-cover transition-all duration-500 group-hover:brightness-110"
                        unoptimized
                        priority={false}
                      />
                    </div>
                  )}
                </div>
                <h3 className="text-white font-bold text-xl md:text-2xl group-hover:text-blue-300 group-hover:scale-110 transition-all duration-500 whitespace-normal break-words relative">
                  <span className="relative z-10">{service.title}</span>
                  {hoveredCard === service.id && (
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-lg blur-sm -z-10 animate-pulse"></div>
                  )}
                </h3>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </main>
  );
}