"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { socialLinks } from "./data/links";
import { ChevronRight, ChevronLeft } from "lucide-react";

const iconColors: { [key: string]: string } = {
  Facebook: "#4267B2",
  Twitter: "#1DA1F2",
  Instagram: "#E4405F",
  LinkedIn: "#0A66C2",
  YouTube: "#FF0000",
  GitHub: "#E8E8E8",
};

const hoverTexts: { [key: string]: string[] } = {
  Facebook: ["Connect", "Share", "Like"],
  Twitter: ["Tweet", "Follow", "Trend"],
  Instagram: ["Post", "Story", "Reel"],
  LinkedIn: ["Network", "Career", "Pro"],
  YouTube: ["Watch", "Subscribe", "Create"],
  GitHub: ["Code", "Fork", "Star"],
};

const sidebarVariants = {
  hidden: { x: -200, opacity: 0, scale: 0.8 },
  visible: {
    x: 0,
    opacity: 1,
    scale: 1,
    transition: {
      type: "spring",
      stiffness: 100,
      damping: 15,
      staggerChildren: 0.1,
      delayChildren: 0.3
    },
  },
  exit: { x: -200, opacity: 0, scale: 0.8, transition: { duration: 0.3 } }
};

const itemVariants = {
  hidden: { opacity: 0, x: -50, scale: 0.5 },
  visible: {
    opacity: 1,
    x: 0,
    scale: 1,
    transition: {
      type: "spring",
      stiffness: 150,
      damping: 12,
    },
  },
};

const toggleButtonVariants = {
  hidden: { opacity: 0, scale: 0 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      type: "spring",
      stiffness: 200,
      delay: 1
    }
  }
};

export function SocialSidebar() {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const [mounted, setMounted] = useState(false);
  const [currentTextIndex, setCurrentTextIndex] = useState<{ [key: number]: number }>({});
  const [isShaking, setIsShaking] = useState<number | null>(null);

  useEffect(() => {
    setMounted(true);

    // Auto shake animation every 10 seconds
    const shakeInterval = setInterval(() => {
      const randomIndex = Math.floor(Math.random() * socialLinks.length);
      setIsShaking(randomIndex);
      setTimeout(() => setIsShaking(null), 1000);
    }, 10000);

    return () => clearInterval(shakeInterval);
  }, []);

  // Text cycling effect
  useEffect(() => {
    if (hoveredIndex !== null) {
      const interval = setInterval(() => {
        setCurrentTextIndex(prev => ({
          ...prev,
          [hoveredIndex]: ((prev[hoveredIndex] || 0) + 1) % hoverTexts[socialLinks[hoveredIndex].name].length
        }));
      }, 800);

      return () => clearInterval(interval);
    }
  }, [hoveredIndex]);

  if (!mounted) return null;

  return (

    <></>
  );
}