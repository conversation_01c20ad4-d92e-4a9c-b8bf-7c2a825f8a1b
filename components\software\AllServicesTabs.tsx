'use client';

import React from 'react';
import Link from 'next/link';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent } from '@/components/ui/tabs';
import {
  Server, Globe, FileCode, Smartphone, MonitorSmartphone, Layers, Database,
  Cloud, Shield, Sparkles, Gamepad2, PenTool, Figma, Brush, Search,
  BookOpen, BarChart2, Facebook, FileText
} from 'lucide-react';

const techColors = {
  'Node.js': '#3b82f6',
  'React': '#8b5cf6',
  'Python': '#06b6d4',
  'Java': '#10b981',
  'Flutter': '#f59e0b',
  'React Native': '#ef4444',
  'PHP': '#ec4899',
  'Laravel': '#6366f1',
  'MongoDB': '#84cc16',
  'Postgres': '#3b82f6',
  'MySQL': '#8b5cf6',
  'Bootstrap': '#06b6d4',
  'HTML5': '#10b981',
  'CSS3': '#f59e0b',
  'Cloud': '#ef4444',
  'Blockchain': '#ec4899',
  'Smart Contract': '#6366f1',
  'Unity': '#84cc16',
  'Unreal Engine': '#3b82f6',
  'C#': '#8b5cf6',
  'SEO': '#06b6d4',
  'WordPress': '#10b981',
  'Google Ads': '#f59e0b',
  'Facebook Ads': '#ef4444',
  'Content Marketing': '#ec4899',
  'Photoshop': '#6366f1',
  'Illustrator': '#84cc16',
  'Figma': '#3b82f6',
  'Canva': '#8b5cf6',
  'CorelDRAW': '#06b6d4',
};

const services = [
  {
    key: 'software-development',
    label: 'Software Development',
    description: 'Custom web, mobile, and enterprise software solutions using the latest technologies and best practices.',
    href: '/software-development',
    tech: [
      { name: 'Node.js', icon: Server },
      { name: 'React', icon: Globe },
      { name: 'Python', icon: FileCode },
      { name: 'Java', icon: FileCode },
      { name: 'Flutter', icon: Smartphone },
      { name: 'React Native', icon: MonitorSmartphone },
      { name: 'PHP', icon: FileCode },
      { name: 'Laravel', icon: Layers },
      { name: 'MongoDB', icon: Database },
      { name: 'Postgres', icon: Database },
      { name: 'MySQL', icon: Database },
      { name: 'Bootstrap', icon: Layers },
      { name: 'HTML5', icon: FileCode },
      { name: 'CSS3', icon: FileCode },
      { name: 'Cloud', icon: Cloud },
      { name: 'Blockchain', icon: Shield },
      { name: 'Smart Contract', icon: Sparkles },
    ],
  },
  {
    key: 'gaming-software',
    label: 'Gaming Software',
    description: 'End-to-end game development for web, mobile, and desktop platforms, including blockchain and NFT games.',
    href: '/gaming-software',
    tech: [
      { name: 'Unity', icon: Gamepad2 },
      { name: 'Unreal Engine', icon: Gamepad2 },
      { name: 'Node.js', icon: Server },
      { name: 'React', icon: Globe },
      { name: 'C#', icon: FileCode },
      { name: 'Java', icon: FileCode },
      { name: 'Flutter', icon: Smartphone },
      { name: 'React Native', icon: MonitorSmartphone },
      { name: 'Blockchain', icon: Shield },
      { name: 'Smart Contract', icon: Sparkles },
      { name: 'Cloud', icon: Cloud },
    ],
  },
  {
    key: 'digital-marketing',
    label: 'Digital Marketing',
    description: 'Grow your business with SEO, paid ads, content marketing, and social media strategies that deliver results.',
    href: '/digital-marketing',
    tech: [
      { name: 'SEO', icon: Search },
      { name: 'WordPress', icon: BookOpen },
      { name: 'Google Ads', icon: BarChart2 },
      { name: 'Facebook Ads', icon: Facebook },
      { name: 'Content Marketing', icon: FileText },
      { name: 'Cloud', icon: Cloud },
    ],
  },
  {
    key: 'graphics-design',
    label: 'Graphics Design',
    description: 'Creative graphic design for branding, marketing, and digital experiences using industry-leading tools.',
    href: '/graphics-design',
    tech: [
      { name: 'Photoshop', icon: Brush },
      { name: 'Illustrator', icon: PenTool },
      { name: 'Figma', icon: Figma },
      { name: 'Canva', icon: Brush },
      { name: 'CorelDRAW', icon: Brush },
      { name: 'Cloud', icon: Cloud },
    ],
  },
];

const AllServicesTabs: React.FC = () => {
  return (
    <section className="relative w-full mx-auto py-8 xs:py-10 sm:py-12 md:py-16 px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 overflow-hidden bg-transparent">
      <div className="text-center mb-8 xs:mb-9 sm:mb-10 md:mb-12 animate-fade-in-up">
        <h2 className="text-responsive-xl font-extrabold mb-3 xs:mb-4 text-blue-600 dark:text-blue-400">
          <span className="hidden sm:inline">Our Services & Tech Stacks</span>
          <span className="sm:hidden">Services & Tech</span>
        </h2>
        <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
          Explore our core services and the modern technologies we use to deliver top-notch solutions for your business.
        </p>
      </div>

      <Tabs defaultValue={services[0].key} className="w-full">
        <TabsList className="flex flex-wrap justify-center gap-4 mb-12 border-0 bg-transparent">
          {services.map((service, index) => (
            <TabsTrigger
              key={service.key}
              value={service.key}
              className="capitalize text-base px-8 py-3 rounded-full font-semibold transition-all duration-500 focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 focus-visible:z-10 animate-fade-in-up
                data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-lg data-[state=active]:scale-105
                data-[state=inactive]:bg-white data-[state=inactive]:dark:bg-gray-800 data-[state=inactive]:text-gray-700 data-[state=inactive]:dark:text-gray-300 data-[state=inactive]:hover:bg-gray-100 data-[state=inactive]:dark:hover:bg-gray-700 data-[state=inactive]:hover:scale-105 data-[state=inactive]:shadow-md data-[state=inactive]:border data-[state=inactive]:border-gray-200 data-[state=inactive]:dark:border-gray-700"
              style={{
                animationDelay: `${index * 0.1}s`
              }}
            >
              {service.label}
            </TabsTrigger>
          ))}
        </TabsList>

        {services.map((service) => (
          <TabsContent key={service.key} value={service.key} className="flex flex-col items-center animate-fade-in-up">
            <div className="mb-10 text-center max-w-3xl">
              <p className="text-lg text-gray-600 dark:text-gray-300 font-medium mb-6 leading-relaxed">
                {service.description}
              </p>
              <Link href={service.href} className="group inline-block px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-full font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300">
                Learn More
                <span className="ml-2 group-hover:translate-x-1 transition-transform duration-300">→</span>
              </Link>
            </div>

            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-6 w-full max-w-6xl">
              {service.tech.map(({ name, icon: Icon }, index) => (
                <div
                  key={name}
                  className="group relative flex flex-col items-center p-6 bg-white dark:bg-gray-900 rounded-2xl shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-500 border border-gray-200 dark:border-gray-700 hover:border-blue-400 animate-fade-in-up overflow-hidden"
                  style={{ animationDelay: `${index * 0.05}s` }}
                >
                  {/* Right corner circle shape - bigger */}
                  <svg className="absolute -top-3 -right-3 w-20 h-20 z-10 group-hover:scale-110 transition-transform duration-500" viewBox="0 0 80 80">
                    <circle cx="60" cy="20" r="16" fill={techColors[name as keyof typeof techColors] || '#3b82f6'} />
                  </svg>

                  {/* Enhanced background with solid colors */}
                  <div className="absolute inset-0 bg-gray-50 dark:bg-gray-800 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl"></div>

                  <div className="relative w-16 h-16 mb-4 flex items-center justify-center">
                    <span className="inline-flex items-center justify-center w-14 h-14 rounded-xl shadow-md transition-all bg-gray-100 dark:bg-gray-800 group-hover:scale-110 group-hover:rotate-6 duration-500 border border-gray-200 dark:border-gray-700">
                      <Icon className="w-7 h-7 relative z-10 group-hover:scale-110 transition-transform duration-500" style={{ color: techColors[name as keyof typeof techColors] || '#3b82f6' }} />
                    </span>
                  </div>

                  <span className="text-sm font-bold text-center text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-all duration-500 relative z-10">
                    {name}
                  </span>

                  {/* Enhanced border with solid color */}
                  <div className="absolute inset-0 rounded-2xl border-2 border-transparent group-hover:border-blue-400 transition-all duration-500"></div>
                </div>
              ))}
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </section>
  );
};

export default AllServicesTabs;
