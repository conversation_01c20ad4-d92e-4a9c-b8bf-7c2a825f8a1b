'use client';

import React, { useState } from 'react';
import { ChevronDown, HelpCircle, Sparkles, MessageCircle } from 'lucide-react';
import { faqData, FAQItem } from '@/components/data/faq-data';

interface FAQItemProps {
  faq: FAQItem;
  isOpen: boolean;
  onToggle: () => void;
}

const FAQItemComponent: React.FC<FAQItemProps> = ({ faq, isOpen, onToggle }) => {
  const renderAnswer = (answer: string | string[]) => {
    if (Array.isArray(answer)) {
      return (
        <div className="space-y-3">
          {answer.map((line, index) => (
            <p key={index} className="text-gray-600 dark:text-gray-300 leading-relaxed">
              {index === 0 && line.includes('available 24/7') ? (
                <span className="font-medium text-blue-600 dark:text-blue-400">{line}</span>
              ) : (
                line
              )}
            </p>
          ))}
        </div>
      );
    }
    return <p className="text-gray-600 dark:text-gray-300 leading-relaxed">{answer}</p>;
  };

  return (
    <div className={`group relative border-2 rounded-xl mb-4 overflow-hidden transition-all duration-500 hover:shadow-xl ${
      isOpen
        ? 'border-blue-500 dark:border-blue-400 shadow-lg shadow-blue-500/20'
        : 'border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600'
    }`}>
      {/* Gradient background overlay */}
      <div className={`absolute inset-0 bg-gradient-to-r from-blue-50 via-purple-50 to-cyan-50 dark:from-blue-900/10 dark:via-purple-900/10 dark:to-cyan-900/10 transition-opacity duration-500 ${
        isOpen ? 'opacity-100' : 'opacity-0 group-hover:opacity-50'
      }`} />

      <button
        className="relative w-full px-6 py-5 text-left bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm hover:bg-white dark:hover:bg-gray-800 transition-all duration-300 flex items-center justify-between group"
        onClick={onToggle}
        aria-expanded={isOpen}
      >
        <div className="flex items-center space-x-4">
          <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold transition-all duration-300 ${
            isOpen
              ? 'bg-blue-600 text-white shadow-lg'
              : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 group-hover:bg-blue-100 dark:group-hover:bg-blue-900/30'
          }`}>
            {faq.id}
          </div>
          <span className="font-semibold text-gray-900 dark:text-white pr-4 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
            {faq.question}
          </span>
        </div>
        <div className="flex-shrink-0">
          <div className={`p-2 rounded-full transition-all duration-300 ${
            isOpen
              ? 'bg-blue-600 text-white rotate-180'
              : 'bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 group-hover:bg-blue-100 dark:group-hover:bg-blue-900/30 group-hover:text-blue-600'
          }`}>
            <ChevronDown className="h-4 w-4 transition-transform duration-300" />
          </div>
        </div>
      </button>

      <div
        className={`overflow-hidden transition-all duration-500 ease-out ${
          isOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
        }`}
      >
        <div className="relative px-6 py-6 bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 dark:from-gray-900 dark:via-blue-900/10 dark:to-purple-900/10 border-t border-gray-200 dark:border-gray-700">
          <div className="relative z-10">
            {renderAnswer(faq.answer)}
          </div>
          {/* Decorative corner element */}
          <div className="absolute top-4 right-4 w-2 h-2 bg-blue-400 rounded-full opacity-60" />
        </div>
      </div>
    </div>
  );
};

export default function FAQSection() {
  const [openItems, setOpenItems] = useState<Set<number>>(new Set());

  const toggleItem = (id: number) => {
    const newOpenItems = new Set(openItems);
    if (newOpenItems.has(id)) {
      newOpenItems.delete(id);
    } else {
      newOpenItems.add(id);
    }
    setOpenItems(newOpenItems);
  };

  return (
    <section className="relative py-8 xs:py-10 sm:py-12 md:py-16 lg:py-20 px-2 xs:px-3 sm:px-4 overflow-hidden">
      {/* Enhanced Responsive Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-20 xs:-top-30 sm:-top-40 -right-20 xs:-right-30 sm:-right-40 w-40 xs:w-60 sm:w-80 h-40 xs:h-60 sm:h-80 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-2xl xs:blur-3xl" />
        <div className="absolute -bottom-20 xs:-bottom-30 sm:-bottom-40 -left-20 xs:-left-30 sm:-left-40 w-40 xs:w-60 sm:w-80 h-40 xs:h-60 sm:h-80 bg-gradient-to-tr from-cyan-400/20 to-blue-400/20 rounded-full blur-2xl xs:blur-3xl" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-48 xs:w-72 sm:w-96 h-48 xs:h-72 sm:h-96 bg-gradient-to-r from-purple-400/10 to-pink-400/10 rounded-full blur-2xl xs:blur-3xl" />
      </div>

      <div className="relative container-responsive">
        {/* Enhanced Responsive Header */}
        <div className="text-center mb-8 xs:mb-10 sm:mb-12 md:mb-16">
          <div className="flex items-center justify-center mb-4 xs:mb-5 sm:mb-6">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full blur-lg opacity-30" />
              <div className="relative bg-white dark:bg-gray-800 p-2 xs:p-3 sm:p-4 rounded-full shadow-lg">
                <HelpCircle className="h-4 xs:h-5 sm:h-6 md:h-8 w-4 xs:w-5 sm:w-6 md:w-8 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
          </div>
          <h2 className="text-responsive-2xl font-bold mb-4 xs:mb-5 sm:mb-6">
            <span className="bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 dark:from-white dark:via-blue-200 dark:to-purple-200 bg-clip-text text-transparent">
              FAQ
            </span>
            <span className="text-blue-600 dark:text-blue-400 text-3xl xs:text-4xl sm:text-5xl md:text-6xl lg:text-7xl">'</span>
            <span className="bg-gradient-to-r from-purple-800 via-blue-800 to-gray-900 dark:from-purple-200 dark:via-blue-200 dark:to-white bg-clip-text text-transparent">
              s
            </span>
          </h2>
          <p className="text-responsive-base text-gray-600 dark:text-gray-300 max-w-xs xs:max-w-sm sm:max-w-md md:max-w-lg lg:max-w-3xl mx-auto leading-relaxed">
            <span className="hidden sm:inline">Find answers to commonly asked questions about our services and company</span>
            <span className="sm:hidden">Common questions about our services</span>
          </p>
          <div className="mt-4 xs:mt-5 sm:mt-6 flex items-center justify-center space-x-1.5 xs:space-x-2">
            <Sparkles className="h-3 xs:h-4 sm:h-5 w-3 xs:w-4 sm:w-5 text-yellow-500 animate-pulse" />
            <span className="text-responsive-xs text-gray-500 dark:text-gray-400 font-medium">
              {openItems.size} of {faqData.length} questions expanded
            </span>
            <Sparkles className="h-3 xs:h-4 sm:h-5 w-3 xs:w-4 sm:w-5 text-yellow-500 animate-pulse" />
          </div>
        </div>

        {/* FAQ Items */}
        <div className="space-y-4">
          {faqData.map((faq, index) => (
            <div
              key={faq.id}
              className="animate-fade-in-up"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <FAQItemComponent
                faq={faq}
                isOpen={openItems.has(faq.id)}
                onToggle={() => toggleItem(faq.id)}
              />
            </div>
          ))}
        </div>

       

      </div>
    </section>
  );
}
