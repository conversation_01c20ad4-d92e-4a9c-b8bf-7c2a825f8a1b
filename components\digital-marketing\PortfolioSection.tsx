"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  TrendingUp, Users, DollarSign, ArrowRight, ExternalLink,
  ShoppingCart, Briefcase, Heart, Smartphone
} from "lucide-react";

interface CaseStudy {
  title: string;
  client: string;
  industry: string;
  challenge: string;
  solution: string;
  results: {
    metric: string;
    value: string;
    icon: React.ComponentType<any>;
  }[];
  image: string;
  tags: string[];
}

const caseStudies: CaseStudy[] = [
  {
    title: "E-commerce Revenue Transformation",
    client: "Fashion Forward",
    industry: "E-commerce",
    challenge: "Low conversion rates and poor organic visibility were limiting growth potential.",
    solution: "Implemented comprehensive SEO strategy, optimized product pages, and launched targeted PPC campaigns.",
    results: [
      { metric: "Revenue Increase", value: "340%", icon: DollarSign },
      { metric: "Organic Traffic", value: "250%", icon: TrendingUp },
      { metric: "Conversion Rate", value: "180%", icon: ShoppingCart }
    ],
    image: "/api/placeholder/400/300",
    tags: ["SEO", "PPC", "E-commerce"]
  },
  {
    title: "SaaS Lead Generation Success",
    client: "TechFlow Solutions",
    industry: "Technology",
    challenge: "Struggling to generate qualified leads and establish thought leadership in competitive market.",
    solution: "Developed content marketing strategy, LinkedIn advertising, and marketing automation workflows.",
    results: [
      { metric: "Qualified Leads", value: "420%", icon: Users },
      { metric: "Cost per Lead", value: "-65%", icon: DollarSign },
      { metric: "Demo Requests", value: "300%", icon: TrendingUp }
    ],
    image: "/api/placeholder/400/300",
    tags: ["Content Marketing", "LinkedIn Ads", "Lead Generation"]
  },
  {
    title: "Local Business Digital Domination",
    client: "Wellness Center Plus",
    industry: "Healthcare",
    challenge: "Limited online presence and difficulty competing with larger healthcare providers.",
    solution: "Local SEO optimization, Google My Business management, and targeted social media campaigns.",
    results: [
      { metric: "Local Rankings", value: "#1", icon: TrendingUp },
      { metric: "Appointment Bookings", value: "280%", icon: Heart },
      { metric: "Online Reviews", value: "95%", icon: Users }
    ],
    image: "/api/placeholder/400/300",
    tags: ["Local SEO", "Social Media", "Reputation Management"]
  },
  {
    title: "Mobile App User Acquisition",
    client: "FitTrack Pro",
    industry: "Mobile Apps",
    challenge: "High user acquisition costs and low retention rates for fitness tracking app.",
    solution: "App store optimization, influencer partnerships, and retargeting campaigns.",
    results: [
      { metric: "App Downloads", value: "500%", icon: Smartphone },
      { metric: "User Retention", value: "220%", icon: Users },
      { metric: "Acquisition Cost", value: "-45%", icon: DollarSign }
    ],
    image: "/api/placeholder/400/300",
    tags: ["ASO", "Influencer Marketing", "Mobile Marketing"]
  }
];

export function PortfolioSection() {
  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8">
      <div className="container mx-auto">
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-blue-500/10 border border-blue-500/20 mb-6 animate-fade-in-up">
            <Briefcase className="h-4 w-4 text-blue-500 mr-2" />
            <span className="text-sm font-medium text-blue-500">Success Stories</span>
          </div>
          
          <h2 className="text-4xl md:text-5xl font-bold mb-6 hero-title animate-fade-in-up">
            Real Results for Real Businesses
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto animate-fade-in-up">
            Discover how we've helped businesses across various industries achieve remarkable growth through strategic digital marketing.
          </p>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {caseStudies.map((study, index) => (
            <Card 
              key={index} 
              className="service-card hover:scale-105 transition-all duration-300 group overflow-hidden animate-fade-in-up"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="aspect-video bg-gradient-to-br from-blue-500/20 to-purple-500/20 relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                <div className="absolute bottom-4 left-4 right-4">
                  <Badge className="bg-blue-500 text-white mb-2">{study.industry}</Badge>
                  <h3 className="text-white font-bold text-xl">{study.title}</h3>
                </div>
              </div>
              
              <CardContent className="p-8">
                <div className="mb-6">
                  <h4 className="font-semibold text-lg mb-2">{study.client}</h4>
                  <div className="flex flex-wrap gap-2 mb-4">
                    {study.tags.map((tag, tagIndex) => (
                      <Badge key={tagIndex} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
                
                <div className="space-y-4 mb-6">
                  <div>
                    <h5 className="font-semibold text-sm text-muted-foreground mb-2">Challenge:</h5>
                    <p className="text-sm">{study.challenge}</p>
                  </div>
                  
                  <div>
                    <h5 className="font-semibold text-sm text-muted-foreground mb-2">Solution:</h5>
                    <p className="text-sm">{study.solution}</p>
                  </div>
                </div>
                
                <div className="border-t pt-6">
                  <h5 className="font-semibold text-sm text-muted-foreground mb-4">Key Results:</h5>
                  <div className="grid grid-cols-3 gap-4 mb-6">
                    {study.results.map((result, resultIndex) => (
                      <div key={resultIndex} className="text-center">
                        <div className="inline-flex items-center justify-center w-10 h-10 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 mb-2">
                          <result.icon className="h-5 w-5 text-white" />
                        </div>
                        <div className="text-lg font-bold text-blue-500">{result.value}</div>
                        <div className="text-xs text-muted-foreground">{result.metric}</div>
                      </div>
                    ))}
                  </div>
                  
                  <Button variant="outline" className="w-full group">
                    View Full Case Study
                    <ExternalLink className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        
        <div className="text-center mt-12">
          <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white group">
            View All Case Studies
            <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
          </Button>
        </div>
      </div>
    </section>
  );
}
