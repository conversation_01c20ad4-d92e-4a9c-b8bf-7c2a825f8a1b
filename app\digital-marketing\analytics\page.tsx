"use client";

import { MarketingNavigation } from "@/components/marketing-navigation";
import { ServiceHero, ServiceFeatures, ServicePricing, ServiceCTA } from "@/components/digital-marketing/shared";
import {
  BarChart3, TrendingUp, Target, Eye, Users, DollarSign,
  Zap, Award, Globe, Monitor, Activity, PieChart
} from "lucide-react";

export default function AnalyticsServicesPage() {
  const heroData = {
    title: "Analytics & Reporting",
    subtitle: "That Drive Decisions",
    description: "Make data-driven decisions with comprehensive analytics and reporting that provide actionable insights to optimize your marketing performance and ROI.",
    features: [
      "Custom dashboard creation and setup",
      "Advanced tracking and measurement",
      "Comprehensive performance reporting",
      "Data visualization and insights",
      "ROI and attribution analysis"
    ],
    primaryCTA: "Get Analytics Setup",
    secondaryCTA: "View Sample Reports",
    badge: "📊 Analytics Excellence",
    stats: [
      { value: "95%", label: "Data Accuracy" },
      { value: "50+", label: "KPIs Tracked" },
      { value: "24/7", label: "Real-time Monitoring" },
      { value: "100%", label: "Custom Reporting" }
    ],
    backgroundGradient: "from-violet-900/10 via-purple-900/5 to-violet-900/10"
  };

  const features = [
    {
      icon: Monitor,
      title: "Custom Dashboards",
      description: "Beautiful, interactive dashboards that provide real-time insights into your marketing performance.",
      benefits: [
        "Real-time data visualization",
        "Custom KPI tracking",
        "Interactive charts and graphs",
        "Mobile-responsive design"
      ],
      popular: true
    },
    {
      icon: Target,
      title: "Conversion Tracking",
      description: "Advanced conversion tracking to measure the effectiveness of your marketing campaigns.",
      benefits: [
        "Goal and event tracking",
        "E-commerce tracking",
        "Multi-channel attribution",
        "Customer journey mapping"
      ]
    },
    {
      icon: TrendingUp,
      title: "Performance Analysis",
      description: "Deep dive into your marketing performance with comprehensive analysis and recommendations.",
      benefits: [
        "Traffic source analysis",
        "Campaign performance review",
        "Competitor benchmarking",
        "Trend identification"
      ]
    },
    {
      icon: DollarSign,
      title: "ROI Measurement",
      description: "Track and measure the return on investment for all your marketing activities.",
      benefits: [
        "Revenue attribution",
        "Cost per acquisition tracking",
        "Lifetime value analysis",
        "Budget optimization"
      ]
    },
    {
      icon: Users,
      title: "Audience Insights",
      description: "Understand your audience better with detailed demographic and behavioral analysis.",
      benefits: [
        "Demographic analysis",
        "Behavioral segmentation",
        "User flow analysis",
        "Audience overlap studies"
      ]
    },
    {
      icon: Activity,
      title: "Automated Reporting",
      description: "Automated reports delivered to your inbox with key insights and recommendations.",
      benefits: [
        "Scheduled report delivery",
        "Custom report templates",
        "Executive summaries",
        "Actionable recommendations"
      ]
    }
  ];

  const pricingTiers = [
    {
      name: "Analytics Starter",
      price: "$600",
      period: "month",
      description: "Essential analytics setup for small businesses",
      features: [
        "Google Analytics setup",
        "Basic dashboard creation",
        "Monthly reporting",
        "Goal tracking setup",
        "Email support"
      ],
      cta: "Start Analytics Tracking",
      badge: "Best for Small Business"
    },
    {
      name: "Analytics Professional",
      price: "$1,500",
      period: "month",
      description: "Comprehensive analytics for growing businesses",
      features: [
        "Advanced tracking setup",
        "Custom dashboard creation",
        "Multi-platform integration",
        "Conversion tracking",
        "Bi-weekly reporting",
        "Performance analysis",
        "Phone support"
      ],
      popular: true,
      cta: "Scale Analytics Insights",
      badge: "Most Popular"
    },
    {
      name: "Analytics Enterprise",
      price: "$3,500",
      period: "month",
      description: "Advanced analytics for large businesses",
      features: [
        "Enterprise-level tracking",
        "Custom data warehouse",
        "Advanced attribution modeling",
        "Predictive analytics",
        "Real-time monitoring",
        "Dedicated analyst",
        "Weekly reporting",
        "Priority support"
      ],
      cta: "Maximize Data Insights",
      badge: "Maximum Intelligence"
    }
  ];

  const ctaData = {
    title: "Ready to Unlock Your Data's Potential?",
    subtitle: "Transform your marketing performance with comprehensive analytics and reporting that drive real business results.",
    primaryCTA: "Get Analytics Audit",
    secondaryCTA: "Schedule Demo",
    features: [
      "Free analytics audit",
      "Custom dashboard setup",
      "Insights within 7 days"
    ],
    backgroundGradient: "from-violet-600 via-purple-700 to-violet-700"
  };

  return (
    <main className="min-h-screen">
      <MarketingNavigation />
      <ServiceHero {...heroData} />
      <ServiceFeatures
        title="Complete Analytics Solutions"
        subtitle="Make data-driven decisions with our comprehensive analytics and reporting services"
        features={features}
      />
      <ServicePricing
        title="Analytics Packages"
        subtitle="Choose the perfect analytics package to optimize your marketing performance"
        tiers={pricingTiers}
      />
      <ServiceCTA {...ctaData} />
    </main>
  );
}
