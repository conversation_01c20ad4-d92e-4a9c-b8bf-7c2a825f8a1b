import { SharedNavi<PERSON> } from "@/components/shared-navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Users, Award, Target, Zap, Code, Trophy } from "lucide-react";
import Link from "next/link";

export default function GamingAboutPage() {
  const team = [
    {
      name: "<PERSON>",
      role: "Lead Game Developer",
      experience: "8+ years",
      specialization: "Unity, Unreal Engine, C#"
    },
    {
      name: "<PERSON>",
      role: "Game Designer",
      experience: "6+ years", 
      specialization: "Game Mechanics, UX Design"
    },
    {
      name: "<PERSON>",
      role: "Technical Director",
      experience: "10+ years",
      specialization: "Engine Development, Performance"
    },
    {
      name: "<PERSON>",
      role: "QA Lead",
      experience: "5+ years",
      specialization: "Testing, Quality Assurance"
    }
  ];

  const achievements = [
    { icon: Trophy, title: "50+ Games Developed", desc: "Successfully launched games across multiple platforms" },
    { icon: Users, title: "10M+ Players", desc: "Our games have reached millions of players worldwide" },
    { icon: Award, title: "Industry Awards", desc: "Recognition for innovation and excellence in game development" },
    { icon: Target, title: "95% Success Rate", desc: "Consistently delivering projects on time and within budget" }
  ];

  return (
    <main className="min-h-screen">
      <SharedNavigation 
        serviceType="gaming" 
        brandName="BRT Gaming" 
        brandColor="blue"
      />
      
      {/* Hero Section */}
      <section className="pt-24 pb-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-blue-900/20 to-blue-900/20">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-5xl font-bold mb-6 hero-title">
                About BRT Gaming
              </h1>
              <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
                We are passionate game developers dedicated to creating extraordinary gaming experiences. With years of expertise and a commitment to innovation, we transform ideas into engaging, high-quality games.
              </p>
              <div className="grid grid-cols-2 gap-4 mb-8">
                <div className="text-center p-4 bg-blue-500/10 rounded-lg border border-blue-500/20">
                  <div className="text-2xl font-bold text-blue-500">8+</div>
                  <div className="text-sm text-muted-foreground">Years Experience</div>
                </div>
                <div className="text-center p-4 bg-blue-500/10 rounded-lg border border-blue-500/20">
                  <div className="text-2xl font-bold text-blue-500">50+</div>
                  <div className="text-sm text-muted-foreground">Games Developed</div>
                </div>
              </div>
            </div>
            <div className="relative">
              <div className="aspect-square rounded-2xl bg-gradient-to-br from-blue-500/20 to-blue-500/20 p-8 backdrop-blur-sm border border-blue-500/20">
                <div className="w-full h-full rounded-xl bg-gradient-to-br from-blue-400 to-blue-400 opacity-80 flex items-center justify-center">
                  <Code className="h-24 w-24 text-white animate-pulse" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            <Card className="service-card p-8">
              <CardContent className="p-0">
                <h2 className="text-2xl font-bold mb-4 text-blue-500">Our Mission</h2>
                <p className="text-muted-foreground leading-relaxed">
                  To create innovative gaming experiences that captivate players and push the boundaries of interactive entertainment. We believe in the power of games to bring people together and create lasting memories.
                </p>
              </CardContent>
            </Card>
            <Card className="service-card p-8">
              <CardContent className="p-0">
                <h2 className="text-2xl font-bold mb-4 text-blue-500">Our Vision</h2>
                <p className="text-muted-foreground leading-relaxed">
                  To be the leading game development studio known for exceptional quality, innovation, and player satisfaction. We strive to set new standards in the gaming industry through cutting-edge technology and creative excellence.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Achievements */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-muted/20">
        <div className="container mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-16 hero-title">
            Our Achievements
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {achievements.map((achievement, index) => (
              <Card key={index} className="service-card text-center hover:scale-105 transition-all duration-300 group">
                <CardContent className="p-8">
                  <div className="flex justify-center mb-4">
                    <achievement.icon className="h-12 w-12 text-blue-500 group-hover:text-blue-400 transition-colors" />
                  </div>
                  <h3 className="text-lg font-bold mb-2">{achievement.title}</h3>
                  <p className="text-muted-foreground text-sm">{achievement.desc}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-16 hero-title">
            Meet Our Team
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <Card key={index} className="service-card hover:scale-105 transition-all duration-300 group">
                <CardContent className="p-6 text-center">
                  <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-700 rounded-full mx-auto mb-4 flex items-center justify-center group-hover:shadow-lg transition-all duration-300">
                    <Users className="h-10 w-10 text-white" />
                  </div>
                  <h3 className="text-lg font-bold mb-1">{member.name}</h3>
                  <p className="text-blue-500 font-semibold mb-2">{member.role}</p>
                  <p className="text-sm text-muted-foreground mb-2">{member.experience}</p>
                  <p className="text-xs text-muted-foreground">{member.specialization}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-blue-600 to-blue-600 text-white">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Work With Us?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Join the many satisfied clients who have trusted us with their gaming projects. Let's create something amazing together.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg" 
              variant="secondary"
              className="px-8 py-3"
              asChild
            >
              <Link href="/gaming-software/contact">
                Start Your Project
              </Link>
            </Button>
            <Button 
              size="lg" 
              variant="outline"
              className="px-8 py-3 border-white text-white hover:bg-white hover:text-blue-600"
              asChild
            >
              <Link href="/gaming-software/services">
                View Our Services
              </Link>
            </Button>
          </div>
        </div>
      </section>
    </main>
  );
}