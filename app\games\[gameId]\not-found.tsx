import Link from 'next/link';
import { <PERSON>pad2, <PERSON><PERSON><PERSON><PERSON>, Home } from 'lucide-react';
import { Button } from '@/components/ui/button';

export default function GameNotFound() {
  return (
    <main className="min-h-screen bg-white dark:bg-slate-950 text-foreground flex items-center justify-center">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto text-center">
          {/* 404 Animation */}
          <div className="relative mb-8">
            <div className="text-8xl sm:text-9xl font-bold text-gray-200 dark:text-gray-800 select-none">
              404
            </div>
            <div className="absolute inset-0 flex items-center justify-center">
              <Gamepad2 className="w-16 h-16 sm:w-20 sm:h-20 text-sky-500 animate-bounce" />
            </div>
          </div>

          {/* Error Message */}
          <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Game Not Found
          </h1>
          
          <p className="text-lg text-gray-600 dark:text-gray-300 mb-8 leading-relaxed">
            Oops! The game you're looking for doesn't exist or has been moved. 
            Let's get you back to our amazing games collection.
          </p>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/games">
              <Button 
                size="lg"
                className="bg-gradient-to-r from-sky-500 to-sky-600 hover:opacity-90 text-white font-semibold px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
              >
                <Gamepad2 className="w-5 h-5 mr-2" />
                Browse All Games
              </Button>
            </Link>
            
            <Link href="/gaming-software">
              <Button 
                variant="outline"
                size="lg"
                className="border-2 border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 font-semibold px-8 py-3 rounded-xl transition-all duration-300"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Back to Gaming
              </Button>
            </Link>
          </div>

          {/* Decorative Elements */}
          <div className="mt-16 relative">
            <div className="absolute top-0 left-1/4 w-4 h-4 bg-sky-500 rounded-full opacity-60 animate-float"></div>
            <div className="absolute top-8 right-1/4 w-3 h-3 bg-purple-500 rounded-full opacity-60 animate-float" style={{ animationDelay: '1s' }}></div>
            <div className="absolute top-4 left-1/2 w-2 h-2 bg-emerald-500 rounded-full opacity-60 animate-float" style={{ animationDelay: '2s' }}></div>
          </div>
        </div>
      </div>
    </main>
  );
}
