"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, Play, CheckCircle } from "lucide-react";

interface ServiceHeroProps {
  title: string;
  subtitle: string;
  description: string;
  features: string[];
  primaryCTA: string;
  secondaryCTA: string;
  badge: string;
  stats: {
    value: string;
    label: string;
  }[];
  backgroundGradient?: string;
}

export function ServiceHero({
  title,
  subtitle,
  description,
  features,
  primaryCTA,
  secondaryCTA,
  badge,
  stats,
  backgroundGradient = "from-blue-900/10 via-purple-900/5 to-blue-900/10"
}: ServiceHeroProps) {
  return (
    <section className="relative pt-24 pb-20 px-4 sm:px-6 lg:px-8 overflow-hidden">
      {/* 3D Background Elements */}
      <div className={`absolute inset-0 bg-gradient-to-br ${backgroundGradient}`}></div>
      
      {/* Floating 3D Elements */}
      <div className="absolute top-20 left-10 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl animate-float"></div>
      <div className="absolute bottom-20 right-10 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-float delay-1000"></div>
      <div className="absolute top-1/2 left-1/4 w-4 h-4 bg-blue-400/30 rounded-full animate-bounce"></div>
      <div className="absolute top-1/3 right-1/4 w-6 h-6 bg-purple-400/30 rounded-full animate-bounce delay-500"></div>
      <div className="absolute bottom-1/3 left-1/3 w-3 h-3 bg-pink-400/30 rounded-full animate-bounce delay-1000"></div>
      
      {/* 3D Grid Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `
            linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px',
          transform: 'perspective(1000px) rotateX(60deg)'
        }}></div>
      </div>
      
      <div className="container mx-auto relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Left Content */}
          <div>
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-blue-500/10 border border-blue-500/20 mb-6 animate-fade-in-up">
              <span className="text-sm font-medium text-blue-500">{badge}</span>
            </div>
            
            <h1 className="text-5xl md:text-6xl font-bold mb-6 hero-title leading-tight animate-fade-in-up">
              {title}
              <span className="block bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent">
                {subtitle}
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed animate-fade-in-up">
              {description}
            </p>
            
            {/* Features List */}
            <div className="space-y-3 mb-8">
              {features.map((feature, index) => (
                <div 
                  key={index} 
                  className="flex items-center space-x-3 animate-fade-in-up"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                  <span className="text-muted-foreground">{feature}</span>
                </div>
              ))}
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4 mb-8 animate-fade-in-up">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg group">
                {primaryCTA}
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button size="lg" variant="outline" className="px-8 py-4 text-lg group">
                <Play className="mr-2 h-5 w-5" />
                {secondaryCTA}
              </Button>
            </div>
          </div>
          
          {/* Right Content - Stats */}
          <div className="grid grid-cols-2 gap-6">
            {stats.map((stat, index) => (
              <div 
                key={index} 
                className="text-center p-8 service-card rounded-xl hover:scale-105 transition-all duration-300 animate-fade-in-up"
                style={{ 
                  animationDelay: `${index * 0.1}s`,
                  transform: `perspective(1000px) rotateY(${index % 2 === 0 ? '-5deg' : '5deg'}) rotateX(5deg)`
                }}
              >
                <div className="text-3xl md:text-4xl font-bold text-blue-500 mb-2 animate-stats-bounce">
                  {stat.value}
                </div>
                <div className="text-sm text-muted-foreground">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
