<svg xmlns="http://www.w3.org/2000/svg" width="120" height="130" viewBox="0 0 120 130" fill="none">
<style>

@-webkit-keyframes animate-svg-stroke-1 {
  0% {
    stroke-dashoffset: 272.6286926269531px;
    stroke-dasharray: 272.6286926269531px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 272.6286926269531px;
  }
}

@keyframes animate-svg-stroke-1 {
  0% {
    stroke-dashoffset: 272.6286926269531px;
    stroke-dasharray: 272.6286926269531px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 272.6286926269531px;
  }
}

.svg-elem-1 {
  -webkit-animation: animate-svg-stroke-1 1s cubic-bezier(0.47, 0, 0.745, 0.715) 0s alternate-reverse infinite;
          animation: animate-svg-stroke-1 1s cubic-bezier(0.47, 0, 0.745, 0.715) 0s alternate-reverse infinite;
}

@-webkit-keyframes animate-svg-stroke-2 {
  0% {
    stroke-dashoffset: 20.593505859375px;
    stroke-dasharray: 20.593505859375px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 20.593505859375px;
  }
}

@keyframes animate-svg-stroke-2 {
  0% {
    stroke-dashoffset: 20.593505859375px;
    stroke-dasharray: 20.593505859375px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 20.593505859375px;
  }
}

.svg-elem-2 {
  -webkit-animation: animate-svg-stroke-2 1s cubic-bezier(0.47, 0, 0.745, 0.715) 0.12s alternate-reverse infinite;
          animation: animate-svg-stroke-2 1s cubic-bezier(0.47, 0, 0.745, 0.715) 0.12s alternate-reverse infinite;
}

@-webkit-keyframes animate-svg-stroke-3 {
  0% {
    stroke-dashoffset: 23.679275512695312px;
    stroke-dasharray: 23.679275512695312px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 23.679275512695312px;
  }
}

@keyframes animate-svg-stroke-3 {
  0% {
    stroke-dashoffset: 23.679275512695312px;
    stroke-dasharray: 23.679275512695312px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 23.679275512695312px;
  }
}

.svg-elem-3 {
  -webkit-animation: animate-svg-stroke-3 1s cubic-bezier(0.47, 0, 0.745, 0.715) 0.24s alternate-reverse infinite;
          animation: animate-svg-stroke-3 1s cubic-bezier(0.47, 0, 0.745, 0.715) 0.24s alternate-reverse infinite;
}


</style>
  <path d="M22.1156 110.051C18.2222 102.245 23.6536 86.5215 30.9282 82.1394C43.7165 74.4361 62.2295 77.7779 75.2647 84.9947C82.6371 89.0764 74.3756 93.7311 69.545 89.505C60.0016 81.1557 62.3325 64.485 64.093 54.7454C65.6111 46.3471 68.018 39.8044 78.9297 39.6044C86.1099 39.4727 112.573 48.2251 109.851 57.6682C108.842 61.1709 108.334 65.7693 105.666 68.5228C100.542 73.8117 89.7052 62.6086 88.1809 58.1804C83.4668 44.4852 81.8623 28.6279 80.7672 14.5584" stroke="#021639" stroke-width="3" stroke-linecap="round" class="svg-elem-1"></path>
  <path d="M21.9646 110.011C28.2069 109.605 30.4873 100.223 36.972 99.9558" stroke="#FF6433" stroke-width="3" stroke-linecap="round" class="svg-elem-2"></path>
  <path d="M20.1709 108.512C14.4446 103.514 9.24867 99.3197 2.61826 95.8848" stroke="#FF6433" stroke-width="3" stroke-linecap="round" class="svg-elem-3"></path>
</svg>