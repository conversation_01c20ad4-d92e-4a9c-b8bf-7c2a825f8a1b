'use client';

import React from 'react';
import { Search, Brush, Code2, Upload } from 'lucide-react';
import type { FC } from 'react';

type StepProps = {
  step: string;
  title: string;
  description: string;
  Icon: FC<{ className?: string }>;
  colorIndex: number;
};

const process = [
  {
    step: '01',
    title: 'Discovery & Planning',
    description: 'We analyze your requirements, define project scope, and create a detailed roadmap.',
    icon: Search,
  },
  {
    step: '02',
    title: 'Design & Architecture',
    description: 'UI/UX design and technical architecture planning for optimal user experience.',
    icon: Brush,
  },
  {
    step: '03',
    title: 'Development & Testing',
    description: 'Agile development with continuous testing and quality assurance throughout.',
    icon: Code2,
  },
  {
    step: '04',
    title: 'Deployment & Support',
    description: 'Smooth deployment and ongoing maintenance with 24/7 technical support.',
    icon: Upload,
  },
];

const cornerColors = [
  '#3b82f6', // blue-500
  '#8b5cf6', // violet-500
  '#06b6d4', // cyan-500
  '#10b981', // emerald-500
];

const ProcessStep: FC<StepProps> = ({ step, title, description, Icon, colorIndex }) => (
  <div className="group relative p-4 sm:p-6 md:p-8 lg:p-10 bg-white dark:bg-gray-900 rounded-2xl sm:rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-200 dark:border-gray-700 hover:border-blue-400 hover:scale-105 animate-fade-in-up overflow-hidden">
    {/* Right corner shape only */}
    <svg className="absolute -top-1 -right-1 sm:-top-2 sm:-right-2 w-12 h-12 sm:w-16 sm:h-16 z-10 group-hover:scale-110 transition-transform duration-500" viewBox="0 0 64 64">
      <path d="M0,0 Q64,0 64,64 L0,0 Z" fill={cornerColors[colorIndex % cornerColors.length]} />
    </svg>

    {/* Enhanced background with solid colors */}
    <div className="absolute inset-0 bg-blue-50 dark:bg-gray-800 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

    <div className="absolute -top-4 sm:-top-6 left-1/2 -translate-x-1/2 z-30">
      <span className="inline-flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 rounded-full bg-blue-600 text-white text-base sm:text-lg md:text-xl font-bold shadow-xl border-2 sm:border-4 border-white dark:border-gray-900 group-hover:scale-110 transition-transform duration-500">
        {step}
      </span>
    </div>

    <div className="mt-6 sm:mt-8 md:mt-10 mb-4 sm:mb-6 flex items-center justify-center relative z-10">
      <div className="relative w-14 h-14 sm:w-16 sm:h-16 md:w-18 md:h-18 lg:w-20 lg:h-20 flex items-center justify-center rounded-xl sm:rounded-2xl bg-blue-100 dark:bg-blue-900 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 shadow-lg border-2 border-gray-200 dark:border-gray-700">
        {/* Solid ring effect */}
        <div className="absolute inset-0 rounded-xl sm:rounded-2xl bg-blue-500 opacity-0 group-hover:opacity-20 transition-opacity duration-500"></div>
        <Icon className="w-6 h-6 sm:w-8 sm:h-8 md:w-9 md:h-9 lg:w-10 lg:h-10 text-gray-700 dark:text-gray-300 group-hover:text-blue-600 dark:group-hover:text-blue-400 relative z-10 group-hover:scale-110 transition-transform duration-500" />
      </div>
    </div>

    <h3 className="text-lg sm:text-xl md:text-2xl font-bold text-gray-900 dark:text-white mb-3 sm:mb-4 text-center group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-all duration-500 relative z-10">{title}</h3>
    <p className="text-gray-600 dark:text-gray-400 group-hover:text-gray-800 dark:group-hover:text-gray-200 text-center leading-relaxed text-sm sm:text-base md:text-lg transition-colors duration-500 relative z-10">{description}</p>

    {/* Enhanced border with solid color */}
    <div className="absolute inset-0 rounded-2xl sm:rounded-3xl border-2 border-transparent group-hover:border-blue-400 transition-all duration-500"></div>
  </div>
);

const DevelopmentProcess = () => {
  return (
    <section className="relative py-8 sm:py-12 md:py-16 lg:py-20 px-3 sm:px-4 md:px-6 lg:px-8 bg-transparent overflow-hidden">
      <div className="relative z-10 container mx-auto">
        <div className="text-center mb-8 sm:mb-12 md:mb-16 animate-fade-in-up">
          <h2 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-extrabold text-blue-600 dark:text-blue-400 mb-3 sm:mb-4 tracking-tight px-2 sm:px-0">
            Our Development Process
          </h2>
          <p className="text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl text-gray-600 dark:text-gray-300 max-w-xs sm:max-w-lg md:max-w-2xl lg:max-w-4xl mx-auto font-medium leading-relaxed px-2 sm:px-0">
            A proven methodology that ensures successful project delivery on time and within budget.
          </p>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 md:gap-8 lg:gap-10 relative z-10">
          {process.map((phase, index) => (
            <div key={index} style={{ animationDelay: `${index * 0.2}s` }}>
              <ProcessStep
                step={phase.step}
                title={phase.title}
                description={phase.description}
                Icon={phase.icon}
                colorIndex={index}
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default DevelopmentProcess;