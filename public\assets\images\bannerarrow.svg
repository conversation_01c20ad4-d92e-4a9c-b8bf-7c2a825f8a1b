<svg width="88" height="88" viewBox="0 0 88 88" fill="none" xmlns="http://www.w3.org/2000/svg">
<style>
/***************************************************
 * Generated by SVG Artista on 10/7/2023, 1:39:55 PM
 * MIT license (https://opensource.org/licenses/MIT)
 * W. https://svgartista.net
 **************************************************/

@-webkit-keyframes animate-svg-stroke-1 {
  0% {
    stroke-dashoffset: 275.318560862312px;
    stroke-dasharray: 275.318560862312px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 275.318560862312px;
  }
}

@keyframes animate-svg-stroke-1 {
  0% {
    stroke-dashoffset: 275.318560862312px;
    stroke-dasharray: 275.318560862312px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 275.318560862312px;
  }
}

.svg-elem-1 {
  -webkit-animation: animate-svg-stroke-1 3s cubic-bezier(0.47, 0, 0.745, 0.715) 0s alternate-reverse infinite;
          animation: animate-svg-stroke-1 3s cubic-bezier(0.47, 0, 0.745, 0.715) 0s alternate-reverse infinite;
}

@-webkit-keyframes animate-svg-stroke-2 {
  0% {
    stroke-dashoffset: 19.16855239868164px;
    stroke-dasharray: 19.16855239868164px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 19.16855239868164px;
  }
}

@keyframes animate-svg-stroke-2 {
  0% {
    stroke-dashoffset: 19.16855239868164px;
    stroke-dasharray: 19.16855239868164px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 19.16855239868164px;
  }
}

.svg-elem-2 {
  -webkit-animation: animate-svg-stroke-2 3s cubic-bezier(0.47, 0, 0.745, 0.715) 0.12s alternate-reverse infinite;
          animation: animate-svg-stroke-2 3s cubic-bezier(0.47, 0, 0.745, 0.715) 0.12s alternate-reverse infinite;
}

@-webkit-keyframes animate-svg-stroke-3 {
  0% {
    stroke-dashoffset: 18.830001831054688px;
    stroke-dasharray: 18.830001831054688px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 18.830001831054688px;
  }
}

@keyframes animate-svg-stroke-3 {
  0% {
    stroke-dashoffset: 18.830001831054688px;
    stroke-dasharray: 18.830001831054688px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 18.830001831054688px;
  }
}

.svg-elem-3 {
  -webkit-animation: animate-svg-stroke-3 3s cubic-bezier(0.47, 0, 0.745, 0.715) 0.24s alternate-reverse infinite;
          animation: animate-svg-stroke-3 3s cubic-bezier(0.47, 0, 0.745, 0.715) 0.24s alternate-reverse infinite;
}

</style>
<circle cx="44" cy="44" r="43.5" transform="rotate(90 44 44)" stroke="#FF450A" class="svg-elem-1"></circle>
<path d="M50.07 46.4302L44 52.5002L37.93 46.4302" stroke="#FF450A" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" class="svg-elem-2"></path>
<path opacity="0.4" d="M44 35.5V52.33" stroke="#FF450A" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" class="svg-elem-3"></path>
</svg>
