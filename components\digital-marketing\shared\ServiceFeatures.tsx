"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface Feature {
  icon: React.ComponentType<any>;
  title: string;
  description: string;
  benefits: string[];
  popular?: boolean;
}

interface ServiceFeaturesProps {
  title: string;
  subtitle: string;
  features: Feature[];
}

export function ServiceFeatures({ title, subtitle, features }: ServiceFeaturesProps) {
  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8">
      <div className="container mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 hero-title animate-fade-in-up">
            {title}
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto animate-fade-in-up">
            {subtitle}
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Card 
              key={index} 
              className={`service-card hover:scale-105 transition-all duration-500 group relative animate-fade-in-up ${
                feature.popular ? 'ring-2 ring-blue-500' : ''
              }`}
              style={{ 
                animationDelay: `${index * 0.1}s`,
                transform: `perspective(1000px) rotateY(${(index % 3 - 1) * 5}deg) rotateX(2deg)`
              }}
            >
              {feature.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
                  <Badge className="bg-blue-500 text-white px-4 py-1 animate-pulse">Most Popular</Badge>
                </div>
              )}
              
              <CardContent className="p-8 relative overflow-hidden">
                {/* 3D Background Effect */}
                <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-full blur-2xl transform translate-x-8 -translate-y-8 group-hover:scale-150 transition-transform duration-500"></div>
                
                <div className="relative z-10">
                  <div className="inline-flex items-center justify-center w-16 h-16 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 mb-6 group-hover:shadow-2xl group-hover:scale-110 transition-all duration-300">
                    <feature.icon className="h-8 w-8 text-white" />
                  </div>
                  
                  <h3 className="text-xl font-bold mb-4 group-hover:text-blue-500 transition-colors">
                    {feature.title}
                  </h3>
                  
                  <p className="text-muted-foreground mb-6 leading-relaxed">
                    {feature.description}
                  </p>
                  
                  <ul className="space-y-2">
                    {feature.benefits.map((benefit, benefitIndex) => (
                      <li 
                        key={benefitIndex} 
                        className="flex items-center text-sm opacity-0 animate-fade-in-up"
                        style={{ animationDelay: `${(index * 0.1) + (benefitIndex * 0.05)}s` }}
                      >
                        <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-3 animate-pulse" />
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
