"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  TrendingUp, Zap, ArrowRight, Play, Rocket, Star, Headphones
} from "lucide-react";

interface StatItem {
  number: string;
  label: string;
  icon: React.ComponentType<any>;
}

const stats: StatItem[] = [
  { number: "500+", label: "Successful Campaigns", icon: Rocket },
  { number: "250%", label: "Average ROI Increase", icon: TrendingUp },
  { number: "98%", label: "Client Satisfaction", icon: Star },
  { number: "24/7", label: "Support Available", icon: Headphones }
];

export function HeroSection() {
  return (
    <section className="relative pt-24 pb-20 px-4 sm:px-6 lg:px-8 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-900/10 via-purple-900/5 to-blue-900/10"></div>
      <div className="absolute top-20 left-10 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute bottom-20 right-10 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
      
      <div className="container mx-auto relative z-10">
        <div className="text-center max-w-4xl mx-auto mb-16">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-blue-500/10 border border-blue-500/20 mb-6 animate-fade-in-up">
            <Rocket className="h-4 w-4 text-blue-500 mr-2" />
            <span className="text-sm font-medium text-blue-500">Digital Marketing Excellence</span>
          </div>
          
          <h1 className="text-5xl md:text-7xl font-bold mb-6 hero-title leading-tight animate-fade-in-up">
            Accelerate Your
            <span className="block bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent">
              Digital Growth
            </span>
          </h1>
          
          <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed max-w-3xl mx-auto animate-fade-in-up">
            Transform your business with data-driven marketing strategies that deliver measurable results and maximize ROI across all digital channels.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12 animate-fade-in-up">
            <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg group">
              <Zap className="mr-2 h-5 w-5 group-hover:animate-pulse" />
              Start Your Growth Journey
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Button>
            <Button size="lg" variant="outline" className="px-8 py-4 text-lg group">
              <Play className="mr-2 h-5 w-5" />
              Watch Success Stories
            </Button>
          </div>
          
          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <div 
                key={index} 
                className="text-center p-6 service-card rounded-xl hover:scale-105 transition-all duration-300 animate-fade-in-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="inline-flex items-center justify-center w-12 h-12 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 mb-3">
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
                <div className="text-2xl md:text-3xl font-bold text-blue-500 mb-1">{stat.number}</div>
                <div className="text-sm text-muted-foreground">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
