"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  TrendingUp, ArrowRight, Play, Rocket, Star, Headphones,
  Sparkles, Globe, Target, Award, Users, BarChart3
} from "lucide-react";

interface StatItem {
  number: string;
  label: string;
  icon: React.ComponentType<any>;
  color: string;
}

const stats: StatItem[] = [
  { number: "500+", label: "Successful Campaigns", icon: Rocket, color: "from-blue-500 to-cyan-500" },
  { number: "250%", label: "Average ROI Increase", icon: TrendingUp, color: "from-green-500 to-emerald-500" },
  { number: "98%", label: "Client Satisfaction", icon: Star, color: "from-yellow-500 to-orange-500" },
  { number: "24/7", label: "Support Available", icon: Headphones, color: "from-purple-500 to-pink-500" }
];

const floatingElements = [
  { icon: Globe, delay: 0, duration: 6 },
  { icon: Target, delay: 1, duration: 8 },
  { icon: Award, delay: 2, duration: 7 },
  { icon: Users, delay: 3, duration: 9 },
  { icon: BarChart3, delay: 4, duration: 6 },
  { icon: Sparkles, delay: 5, duration: 10 }
];

export function HeroSection() {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <section className="relative min-h-screen w-full overflow-hidden bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      {/* Animated Background Grid */}
      <div className="absolute inset-0 opacity-20">
        <div
          className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20"
          style={{
            backgroundImage: `
              linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px',
            transform: `translate(${mousePosition.x * 0.01}px, ${mousePosition.y * 0.01}px)`
          }}
        />
      </div>

      {/* Dynamic Floating Elements */}
      {floatingElements.map((element, index) => (
        <div
          key={index}
          className="absolute opacity-30"
          style={{
            left: `${10 + (index * 15)}%`,
            top: `${20 + (index * 10)}%`,
            animation: `float ${element.duration}s ease-in-out infinite`,
            animationDelay: `${element.delay}s`
          }}
        >
          <div className="p-4 rounded-full bg-gradient-to-br from-blue-500/20 to-purple-500/20 backdrop-blur-sm">
            <element.icon className="h-8 w-8 text-blue-400" />
          </div>
        </div>
      ))}

      {/* Large Animated Orbs */}
      <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-500/30 to-cyan-500/30 rounded-full blur-3xl animate-pulse"
           style={{ animation: 'float 8s ease-in-out infinite' }} />
      <div className="absolute top-1/2 right-0 w-80 h-80 bg-gradient-to-br from-purple-500/30 to-pink-500/30 rounded-full blur-3xl animate-pulse"
           style={{ animation: 'float 10s ease-in-out infinite reverse' }} />
      <div className="absolute bottom-0 left-1/3 w-72 h-72 bg-gradient-to-br from-emerald-500/30 to-teal-500/30 rounded-full blur-3xl animate-pulse"
           style={{ animation: 'float 12s ease-in-out infinite' }} />

      {/* Particle Effects */}
      <div className="absolute inset-0">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-blue-400/60 rounded-full animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 3}s`
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="relative z-10 flex items-center justify-center min-h-screen px-4 sm:px-6 lg:px-8">
        <div className="text-center max-w-6xl mx-auto">
          {/* Badge */}
          <div className="inline-flex items-center px-6 py-3 rounded-full bg-white/10 border border-white/20 backdrop-blur-md mb-8 animate-fade-in-up hover:scale-105 transition-all duration-300">
            <Sparkles className="h-5 w-5 text-yellow-400 mr-3 animate-pulse" />
            <span className="text-white font-medium">🚀 #1 Digital Marketing Agency</span>
          </div>

          {/* Main Heading */}
          <h1 className="text-6xl md:text-8xl lg:text-9xl font-bold mb-8 leading-tight animate-fade-in-up">
            <span className="block text-white mb-4">
              Transform Your
            </span>
            <span className="block bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent animate-pulse">
              Digital Empire
            </span>
          </h1>

          {/* Subtitle */}
          <p className="text-xl md:text-3xl text-blue-100 mb-12 leading-relaxed max-w-4xl mx-auto animate-fade-in-up font-light">
            Dominate your market with cutting-edge strategies that deliver
            <span className="text-yellow-400 font-semibold"> explosive growth </span>
            and measurable results across all digital channels.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16 animate-fade-in-up">
            <Button
              size="lg"
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-12 py-6 text-xl font-semibold rounded-full shadow-2xl hover:shadow-blue-500/25 transition-all duration-300 group hover:scale-105"
            >
              <Rocket className="mr-3 h-6 w-6 group-hover:animate-bounce" />
              Launch Your Success
              <ArrowRight className="ml-3 h-6 w-6 group-hover:translate-x-2 transition-transform" />
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-2 border-white/30 text-white hover:bg-white/10 px-12 py-6 text-xl font-semibold rounded-full backdrop-blur-md transition-all duration-300 group hover:scale-105"
            >
              <Play className="mr-3 h-6 w-6 group-hover:animate-pulse" />
              Watch Our Magic
            </Button>
          </div>

          {/* Enhanced Stats */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 max-w-5xl mx-auto">
            {stats.map((stat, index) => (
              <div
                key={index}
                className="group relative p-8 rounded-2xl bg-white/10 backdrop-blur-md border border-white/20 hover:bg-white/20 transition-all duration-500 hover:scale-110 animate-fade-in-up cursor-pointer"
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                {/* Glow Effect */}
                <div className={`absolute inset-0 rounded-2xl bg-gradient-to-r ${stat.color} opacity-0 group-hover:opacity-20 transition-opacity duration-500 blur-xl`} />

                <div className="relative z-10 text-center">
                  <div className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-r ${stat.color} mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                    <stat.icon className="h-8 w-8 text-white" />
                  </div>
                  <div className="text-4xl md:text-5xl font-bold text-white mb-2 group-hover:scale-110 transition-transform duration-300">
                    {stat.number}
                  </div>
                  <div className="text-blue-200 font-medium group-hover:text-white transition-colors duration-300">
                    {stat.label}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Bottom Gradient Fade */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-slate-900 to-transparent" />
    </section>
  );
}
