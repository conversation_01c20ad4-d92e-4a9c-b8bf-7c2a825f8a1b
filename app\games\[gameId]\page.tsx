import { notFound } from 'next/navigation';
import { getGameById, getAllGameIds } from '@/components/data/games-data';
import GameDetailPage from '@/components/games/GameDetailPage';
import type { Metadata } from 'next';

interface GamePageProps {
  params: {
    gameId: string;
  };
}

export async function generateStaticParams() {
  const gameIds = getAllGameIds();
  return gameIds.map((gameId) => ({
    gameId: gameId,
  }));
}

export async function generateMetadata({ params }: GamePageProps): Promise<Metadata> {
  const game = getGameById(params.gameId);
  
  if (!game) {
    return {
      title: 'Game Not Found | BRT Multi Software',
      description: 'The requested game could not be found.',
    };
  }

  return {
    metadataBase: new URL('https://brtmultisoftware.com'),
    title: `${game.title} | BRT Multi Software`,
    description: game.description,
    keywords: [
      game.name,
      game.category,
      'Game Development',
      'Mobile Games',
      'BRT Multi Software',
      'Gaming Solutions',
      ...game.features
    ],
    authors: [{ name: 'BRT Multi Software', url: 'https://brtmultisoftware.com/' }],
    creator: 'BRT Multi Software',
    openGraph: {
      title: `${game.title} | BRT Multi Software`,
      description: game.description,
      url: `https://brtmultisoftware.com/games/${game.id}`,
      siteName: 'BRT Multi Software',
      images: [
        {
          url: game.img,
          width: 1200,
          height: 630,
          alt: game.title,
        },
      ],
      locale: 'en_US',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: `${game.title} | BRT Multi Software`,
      description: game.description,
      images: [game.img],
    },
    alternates: {
      canonical: `https://brtmultisoftware.com/games/${game.id}`,
    },
  };
}

export default function GamePage({ params }: GamePageProps) {
  const game = getGameById(params.gameId);

  if (!game) {
    notFound();
  }

  return <GameDetailPage game={game} />;
}
