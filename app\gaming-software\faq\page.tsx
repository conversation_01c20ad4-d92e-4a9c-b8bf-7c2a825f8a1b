import { SharedNavigation } from "@/components/shared-navigation";
import { Card, CardContent } from "@/components/ui/card";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { HelpCircle, MessageCircle } from "lucide-react";
import Link from "next/link";

export default function GamingFAQPage() {
  const faqs = [
    {
      question: "What types of games do you develop?",
      answer: "We develop a wide range of games including mobile games (iOS/Android), PC games, console games, VR/AR games, and multiplayer online games. Our expertise covers various genres from casual puzzle games to complex RPGs and action games."
    },
    {
      question: "How long does it take to develop a game?",
      answer: "Development time varies greatly depending on the complexity and scope of the project. Simple mobile games can take 2-4 months, while complex PC or console games may take 12-24 months or more. We provide detailed timelines during the planning phase."
    },
    {
      question: "What is your development process?",
      answer: "Our process includes: 1) Concept & Planning, 2) Game Design Document creation, 3) Prototyping, 4) Full Development with regular milestones, 5) Testing & QA, 6) Launch preparation, and 7) Post-launch support and updates."
    },
    {
      question: "Do you provide post-launch support?",
      answer: "Yes, we offer comprehensive post-launch support including bug fixes, updates, new content development, server maintenance (for online games), and ongoing optimization based on player feedback."
    },
    {
      question: "What platforms can you develop for?",
      answer: "We develop for all major platforms including iOS, Android, Windows, macOS, PlayStation, Xbox, Nintendo Switch, Steam, and various VR platforms like Oculus and HTC Vive."
    },
    {
      question: "How much does game development cost?",
      answer: "Costs vary significantly based on game complexity, platform, features, and timeline. Simple mobile games start from $15,000, while complex games can cost $100,000+. We provide detailed quotes after understanding your requirements."
    },
    {
      question: "Do you help with game publishing and marketing?",
      answer: "Yes, we can assist with publishing on various app stores and platforms, as well as provide marketing consultation to help promote your game effectively to your target audience."
    },
    {
      question: "Can you work with existing game projects?",
      answer: "Absolutely! We can take over existing projects, provide additional development resources, fix bugs, add new features, or help complete unfinished games."
    },
    {
      question: "What game engines do you use?",
      answer: "We primarily use Unity and Unreal Engine, but also work with custom engines when needed. We choose the best engine based on your project requirements, target platforms, and performance needs."
    },
    {
      question: "Do you provide game design services?",
      answer: "Yes, we offer complete game design services including concept development, game mechanics design, level design, user experience optimization, and monetization strategy planning."
    },
    {
      question: "How do you handle intellectual property?",
      answer: "All intellectual property rights belong to you as the client. We sign comprehensive NDAs and IP agreements to protect your ideas and ensure you maintain full ownership of your game."
    },
    {
      question: "Can you develop multiplayer games?",
      answer: "Yes, we specialize in multiplayer game development including real-time multiplayer, turn-based games, MMOs, and social games with features like matchmaking, leaderboards, and anti-cheat systems."
    }
  ];

  return (
    <main className="min-h-screen">
      <SharedNavigation 
        serviceType="gaming" 
        brandName="BRT Gaming" 
        brandColor="blue"
      />
      
      {/* Hero Section */}
      <section className="pt-24 pb-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-blue-900/20 to-blue-900/20">
        <div className="container mx-auto text-center">
          <div className="flex justify-center mb-6">
            <div className="p-4 rounded-2xl bg-gradient-to-br from-blue-500 to-blue-700 shadow-lg">
              <HelpCircle className="h-12 w-12 text-white" />
            </div>
          </div>
          <h1 className="text-4xl md:text-6xl font-bold mb-6 hero-title">
            Frequently Asked Questions
          </h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
            Find answers to common questions about our gaming development services, process, and pricing.
          </p>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto max-w-4xl">
          <Card className="service-card">
            <CardContent className="p-8">
              <Accordion type="single" collapsible className="space-y-4">
                {faqs.map((faq, index) => (
                  <AccordionItem key={index} value={`item-${index}`} className="border border-blue-500/20 rounded-lg px-4">
                    <AccordionTrigger className="text-left hover:text-blue-500 transition-colors">
                      {faq.question}
                    </AccordionTrigger>
                    <AccordionContent className="text-muted-foreground leading-relaxed pt-2">
                      {faq.answer}
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Still Have Questions */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-muted/20">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6 hero-title">
            Still Have Questions?
          </h2>
          <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
            Can't find the answer you're looking for? Our team is here to help you with any questions about your gaming project.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg" 
              className="bg-blue-600 hover:bg-blue-700 px-8 py-3"
              asChild
            >
              <Link href="/gaming-software/contact">
                <MessageCircle className="mr-2 h-5 w-5" />
                Contact Us
              </Link>
            </Button>
            <Button 
              size="lg" 
              variant="outline"
              className="px-8 py-3"
              asChild
            >
              <Link href="/gaming-software/services">
                View Our Services
              </Link>
            </Button>
          </div>
        </div>
      </section>
    </main>
  );
}