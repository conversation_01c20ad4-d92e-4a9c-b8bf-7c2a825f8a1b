import {
  Facebook,
  Instagram,
  Twitter,
  Youtube,
  LucideIcon,
  Linkedin,
  Github,
  Code,
  Share2,
  Bot,
  Gamepad2,
  ArrowRightLeft,
  Network,
  List,
  FileSignature,
  Globe,
  Wrench,
  Users,
  Mic,
  FlaskConical,
  Replace,
  TrendingUp,
  Megaphone,
  Target,
  BarChart,
} from "lucide-react";

export const serviceGroups = [
  {
    title: "Software & Web3",
    icon: Code,
    services: [
      { name: "NFT Development", href: "/software-development", icon: Share2 },
      { name: "Blockchain", href: "/software-development", icon: Network },
      { name: "AI Services", href: "/software-development", icon: Bo<PERSON> },
      { name: "Game Dev", href: "/software-development", icon: Gamepad2 },
      { name: "Crypto Exchange", href: "/software-development", icon: ArrowRightLeft },
      { name: "Centralized/DeFi", href: "/software-development", icon: Globe },
      { name: "Coin Listing", href: "/software-development", icon: List },
      { name: "Smart Contracts", href: "/software-development", icon: FileSignature },
      { name: "Web3", href: "/software-development", icon: Share2 },
    ],
  },
  {
    title: "Gaming",
    icon: Gamepad2,
    services: [
      { name: "Custom Game Dev", href: "/gaming-software/services", icon: Wrench },
      { name: "Game Engine", href: "/gaming-software/services", icon: Replace },
      { name: "Multiplayer", href: "/gaming-software/services", icon: Users },
      { name: "Design Consult", href: "/gaming-software/services", icon: Mic },
      { name: "Testing & QA", href: "/gaming-software/services", icon: FlaskConical },
      { name: "Porting", href: "/gaming-software/services", icon: Replace },
    ],
  },
  {
    title: "Marketing",
    icon: TrendingUp,
    services: [
      { name: "SEO", href: "/digital-marketing", icon: Megaphone },
      { name: "Social Media", href: "/digital-marketing", icon: Users },
      { name: "PPC", href: "/digital-marketing", icon: Target },
      { name: "Analytics", href: "/digital-marketing", icon: BarChart },
    ],
  },
];

export const companyLinks = [
    { label: "Home", href: "/" },
    { label: "About", href: "/gaming-software/about" },
    { label: "Contact", href: "/gaming-software/contact" },
];

interface SocialLink {
  href: string;
  icon: LucideIcon;
  name: string;
}

export const socialLinks: SocialLink[] = [
  { href: "https://www.facebook.com/brtsoft", icon: Facebook, name: "Facebook" },
  { href: "https://www.instagram.com/brtsoftware/", icon: Instagram, name: "Instagram" },
  { href: "https://twitter.com/brtsoft", icon: Twitter, name: "Twitter" },
  { href: "https://www.linkedin.com/company/brtsoft/", icon: Linkedin, name: "LinkedIn" },
  { href: "https://www.youtube.com/@brtsoft", icon: Youtube, name: "YouTube" },
  { href: "#", icon: Github, name: "GitHub" },
]; 