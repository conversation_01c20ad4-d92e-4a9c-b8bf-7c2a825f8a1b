import { MarketingNavigation } from "@/components/marketing-navigation";
import { ServiceHero, ServiceFeatures, ServicePricing, ServiceCTA } from "@/components/digital-marketing/shared";
import {
  FileText, Video, Mic, Camera, Globe, Users,
  TrendingUp, Award, Zap, Target, BarChart3, Heart
} from "lucide-react";

export default function ContentMarketingPage() {
  const heroData = {
    title: "Content Marketing",
    subtitle: "That Drives Growth",
    description: "Attract, engage, and convert your audience with high-quality content that builds trust, establishes authority, and drives sustainable business growth.",
    features: [
      "Strategic content planning and creation",
      "Blog writing and SEO optimization",
      "Video production and marketing",
      "Podcast creation and distribution",
      "Content performance analytics"
    ],
    primaryCTA: "Start Content Strategy",
    secondaryCTA: "View Content Portfolio",
    badge: "📝 Content Excellence",
    stats: [
      { value: "300%", label: "Lead Generation Increase" },
      { value: "6x", label: "More Qualified Leads" },
      { value: "80%", label: "Cost Reduction vs Ads" },
      { value: "13x", label: "Higher ROI" }
    ],
    backgroundGradient: "from-emerald-900/10 via-teal-900/5 to-emerald-900/10"
  };

  const features = [
    {
      icon: FileText,
      title: "Blog Content Creation",
      description: "High-quality, SEO-optimized blog posts that attract organic traffic and establish thought leadership.",
      benefits: [
        "SEO-optimized articles",
        "Industry expertise content",
        "Thought leadership pieces",
        "How-to guides and tutorials"
      ],
      popular: true
    },
    {
      icon: Video,
      title: "Video Content Production",
      description: "Engaging video content that captures attention and drives engagement across all platforms.",
      benefits: [
        "Explainer videos",
        "Product demonstrations",
        "Customer testimonials",
        "Educational content"
      ]
    },
    {
      icon: Mic,
      title: "Podcast Marketing",
      description: "Build authority and reach new audiences through strategic podcast creation and marketing.",
      benefits: [
        "Podcast strategy development",
        "Episode production",
        "Guest booking",
        "Distribution optimization"
      ]
    },
    {
      icon: Globe,
      title: "Content Distribution",
      description: "Maximize your content's reach with strategic distribution across multiple channels.",
      benefits: [
        "Multi-channel publishing",
        "Social media distribution",
        "Email newsletter content",
        "Guest posting opportunities"
      ]
    },
    {
      icon: Target,
      title: "Content Strategy",
      description: "Comprehensive content strategies aligned with your business goals and audience needs.",
      benefits: [
        "Content calendar planning",
        "Audience research",
        "Competitor analysis",
        "Content gap identification"
      ]
    },
    {
      icon: BarChart3,
      title: "Performance Analytics",
      description: "Track content performance and optimize for better results with detailed analytics.",
      benefits: [
        "Traffic and engagement metrics",
        "Lead generation tracking",
        "Content ROI measurement",
        "Performance optimization"
      ]
    }
  ];

  const pricingTiers = [
    {
      name: "Content Starter",
      price: "$1,000",
      period: "month",
      description: "Perfect for businesses starting their content marketing journey",
      features: [
        "4 blog posts per month",
        "Content strategy development",
        "SEO optimization",
        "Social media promotion",
        "Monthly performance report"
      ],
      cta: "Start Content Marketing",
      badge: "Best for Startups"
    },
    {
      name: "Content Professional",
      price: "$2,500",
      period: "month",
      description: "Comprehensive content marketing for growing businesses",
      features: [
        "8 blog posts per month",
        "2 video content pieces",
        "Email newsletter content",
        "Social media content",
        "Content distribution",
        "Lead magnet creation",
        "Bi-weekly reporting"
      ],
      popular: true,
      cta: "Scale Content Marketing",
      badge: "Most Popular"
    },
    {
      name: "Content Enterprise",
      price: "$5,000",
      period: "month",
      description: "Advanced content marketing for large businesses",
      features: [
        "16 blog posts per month",
        "4 video content pieces",
        "Podcast production",
        "Whitepapers and eBooks",
        "Multi-channel distribution",
        "Dedicated content manager",
        "Weekly reporting",
        "Content performance optimization"
      ],
      cta: "Dominate Content Marketing",
      badge: "Maximum Impact"
    }
  ];

  const ctaData = {
    title: "Ready to Build Your Content Empire?",
    subtitle: "Transform your business with strategic content marketing that attracts, engages, and converts your ideal customers.",
    primaryCTA: "Get Content Audit",
    secondaryCTA: "Schedule Strategy Call",
    features: [
      "Free content marketing audit",
      "Custom content strategy",
      "Results within 60 days"
    ],
    backgroundGradient: "from-emerald-600 via-teal-700 to-emerald-700"
  };

  return (
    <main className="min-h-screen">
      <MarketingNavigation />
      <ServiceHero {...heroData} />
      <ServiceFeatures
        title="Complete Content Marketing Solutions"
        subtitle="Build authority and drive growth with our comprehensive content marketing services"
        features={features}
      />
      <ServicePricing
        title="Content Marketing Packages"
        subtitle="Choose the perfect content marketing package to grow your business"
        tiers={pricingTiers}
      />
      <ServiceCTA {...ctaData} />
    </main>
  );
}
