import { GraphicsNavigation } from "@/components/graphics-navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Palette, Brush, Eye, Sparkles, Award, Layers } from "lucide-react";
import Link from "next/link";

export default function GraphicsDesignPage() {
  const services = [
    {
      icon: Brush,
      title: "Brand Identity Design",
      description: "Complete brand packages including logos, color schemes, and style guides that make lasting impressions."
    },
    {
      icon: Eye,
      title: "UI/UX Design",
      description: "User-centered designs that combine beautiful aesthetics with intuitive functionality."
    },
    {
      icon: Layers,
      title: "3D Modeling & Animation",
      description: "Stunning 3D visuals and animations that bring your ideas to life with photorealistic detail."
    },
    {
      icon: Sparkles,
      title: "Motion Graphics",
      description: "Dynamic animations and video graphics that captivate audiences and enhance storytelling."
    }
  ];

  const portfolio = [
    {
      category: "Branding",
      title: "Tech Startup Rebrand",
      description: "Complete visual identity overhaul resulting in 300% increase in brand recognition.",
      image: "https://images.pexels.com/photos/196644/pexels-photo-196644.jpeg?auto=compress&cs=tinysrgb&w=600"
    },
    {
      category: "Web Design",
      title: "E-commerce Platform",
      description: "Modern, conversion-focused design that boosted sales by 150% within 3 months.",
      image: "https://images.pexels.com/photos/196655/pexels-photo-196655.jpeg?auto=compress&cs=tinysrgb&w=600"
    },
    {
      category: "3D Design",
      title: "Product Visualization",
      description: "Photorealistic 3D renders for marketing campaigns across multiple industries.",
      image: "https://images.pexels.com/photos/442150/pexels-photo-442150.jpeg?auto=compress&cs=tinysrgb&w=600"
    }
  ];

  return (
    <main className="min-h-screen">
      <GraphicsNavigation />
      
      {/* Hero Section */}
      <section className="pt-24 pb-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-blue-900/20 to-blue-900/20">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="flex items-center mb-6">
                <div className="p-4 rounded-2xl bg-gradient-to-br from-blue-500 to-blue-700 shadow-lg mr-4">
                  <Palette className="h-12 w-12 text-white" />
                </div>
                <h1 className="text-4xl md:text-5xl font-bold hero-title">
                  Graphics Design
                </h1>
              </div>
              <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
                Transform your vision into stunning visual experiences. Our creative team delivers exceptional design solutions that elevate your brand and engage your audience.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3">
                  <Award className="mr-2 h-5 w-5" />
                  View Portfolio
                </Button>
                <Button size="lg" variant="outline" className="px-8 py-3">
                  Get Quote
                </Button>
              </div>
            </div>
            <div className="relative">
              <div className="aspect-square rounded-2xl bg-gradient-to-br from-blue-500/20 to-blue-500/20 p-8 backdrop-blur-sm border border-blue-500/20">
                <div className="w-full h-full rounded-xl bg-gradient-to-br from-blue-400 to-blue-400 opacity-80 flex items-center justify-center">
                  <Sparkles className="h-24 w-24 text-white animate-pulse" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-16 hero-title">
            Design Services
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {services.map((service, index) => (
              <Card key={index} className="service-card hover:scale-105 transition-all duration-300 group p-8">
                <CardContent className="p-0">
                  <div className="flex items-start space-x-4">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-blue-500 to-blue-700 shadow-lg group-hover:shadow-xl transition-all duration-300">
                      <service.icon className="h-8 w-8 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-bold mb-3 group-hover:text-blue-500 transition-colors">
                        {service.title}
                      </h3>
                      <p className="text-muted-foreground leading-relaxed">
                        {service.description}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Portfolio Showcase */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-muted/20">
        <div className="container mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-16 hero-title">
            Featured Work
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {portfolio.map((item, index) => (
              <Card key={index} className="service-card overflow-hidden hover:scale-105 transition-all duration-300 group">
                <div className="aspect-video overflow-hidden">
                  <img 
                    src={item.image} 
                    alt={item.title}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                </div>
                <CardContent className="p-6">
                  <div className="text-sm text-blue-500 font-semibold mb-2">{item.category}</div>
                  <h3 className="text-xl font-bold mb-3">{item.title}</h3>
                  <p className="text-muted-foreground">{item.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-16 hero-title">
            Our Design Process
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              { step: "01", title: "Discovery", desc: "Understanding your brand, goals, and target audience" },
              { step: "02", title: "Concept", desc: "Creating initial concepts and design directions" },
              { step: "03", title: "Design", desc: "Developing polished designs with attention to detail" },
              { step: "04", title: "Delivery", desc: "Final assets and ongoing support for your success" }
            ].map((phase, index) => (
              <div key={index} className="text-center group">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-700 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-white font-bold text-lg">{phase.step}</span>
                </div>
                <h3 className="text-xl font-bold mb-2">{phase.title}</h3>
                <p className="text-muted-foreground text-sm">{phase.desc}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-blue-600 to-blue-600 text-white">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Elevate Your Brand?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Let's create stunning visuals that make your brand unforgettable and drive meaningful engagement with your audience.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg" 
              variant="secondary"
              className="px-8 py-3"
            >
              Start Your Project
            </Button>
            <Button 
              size="lg" 
              variant="outline"
              className="px-8 py-3 border-white text-white hover:bg-white hover:text-blue-600"
              asChild
            >
              <Link href="/">
                Explore All Services
              </Link>
            </Button>
          </div>
        </div>
      </section>
    </main>
  );
}