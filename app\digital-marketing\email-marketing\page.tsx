import { MarketingNavigation } from "@/components/marketing-navigation";
import { ServiceHero, ServiceFeatures, ServicePricing, ServiceCTA } from "@/components/digital-marketing/shared";
import {
  Mail, Users, Target, BarChart3, Zap, Heart,
  Send, Award, Globe, Smartphone, TrendingUp, CheckCircle
} from "lucide-react";

export default function EmailMarketingPage() {
  const heroData = {
    title: "Email Marketing",
    subtitle: "That Converts",
    description: "Build lasting relationships with your customers through personalized email campaigns that drive engagement, nurture leads, and boost conversions.",
    features: [
      "Automated email sequences and drip campaigns",
      "Personalized email design and copywriting",
      "Advanced segmentation and targeting",
      "A/B testing for optimal performance",
      "Comprehensive analytics and reporting"
    ],
    primaryCTA: "Start Email Campaigns",
    secondaryCTA: "View Templates",
    badge: "📧 Email Excellence",
    stats: [
      { value: "4,200%", label: "Average ROI" },
      { value: "45%", label: "Open Rate Increase" },
      { value: "300%", label: "Click-Through Rate" },
      { value: "99%", label: "Deliverability Rate" }
    ],
    backgroundGradient: "from-indigo-900/10 via-blue-900/5 to-indigo-900/10"
  };

  const features = [
    {
      icon: Send,
      title: "Email Campaign Design",
      description: "Beautiful, responsive email templates that look great on any device and drive action.",
      benefits: [
        "Custom email templates",
        "Mobile-responsive design",
        "Brand-consistent styling",
        "Interactive elements"
      ],
      popular: true
    },
    {
      icon: Target,
      title: "List Segmentation",
      description: "Advanced segmentation strategies to deliver the right message to the right audience.",
      benefits: [
        "Behavioral segmentation",
        "Demographic targeting",
        "Purchase history analysis",
        "Engagement-based groups"
      ]
    },
    {
      icon: Zap,
      title: "Marketing Automation",
      description: "Automated email sequences that nurture leads and drive conversions 24/7.",
      benefits: [
        "Welcome email series",
        "Abandoned cart recovery",
        "Lead nurturing sequences",
        "Re-engagement campaigns"
      ]
    },
    {
      icon: BarChart3,
      title: "Performance Analytics",
      description: "Detailed insights into your email performance with actionable recommendations.",
      benefits: [
        "Open and click tracking",
        "Conversion attribution",
        "A/B testing results",
        "ROI measurement"
      ]
    },
    {
      icon: Users,
      title: "List Building",
      description: "Grow your email list with proven strategies and lead magnets.",
      benefits: [
        "Lead magnet creation",
        "Opt-in form optimization",
        "Landing page design",
        "List growth strategies"
      ]
    },
    {
      icon: Heart,
      title: "Customer Retention",
      description: "Keep customers engaged and coming back with strategic retention campaigns.",
      benefits: [
        "Loyalty program emails",
        "Customer surveys",
        "Product recommendations",
        "Win-back campaigns"
      ]
    }
  ];

  const pricingTiers = [
    {
      name: "Email Starter",
      price: "$800",
      period: "month",
      description: "Perfect for small businesses starting with email marketing",
      features: [
        "Up to 5,000 subscribers",
        "2 email campaigns per month",
        "Basic templates",
        "List segmentation",
        "Monthly reporting"
      ],
      cta: "Start Email Marketing",
      badge: "Best for Small Business"
    },
    {
      name: "Email Professional",
      price: "$2,200",
      period: "month",
      description: "Comprehensive email marketing for growing businesses",
      features: [
        "Up to 25,000 subscribers",
        "8 email campaigns per month",
        "Custom template design",
        "Advanced automation",
        "A/B testing",
        "Detailed analytics",
        "Bi-weekly reporting"
      ],
      popular: true,
      cta: "Scale Email Marketing",
      badge: "Most Popular"
    },
    {
      name: "Email Enterprise",
      price: "$4,500",
      period: "month",
      description: "Advanced email marketing for large businesses",
      features: [
        "Unlimited subscribers",
        "Unlimited campaigns",
        "Custom automation flows",
        "Advanced segmentation",
        "Dedicated IP",
        "Priority deliverability",
        "Dedicated account manager",
        "Weekly reporting"
      ],
      cta: "Maximize Email ROI",
      badge: "Maximum Performance"
    }
  ];

  const ctaData = {
    title: "Ready to Transform Your Email Marketing?",
    subtitle: "Start building stronger customer relationships and driving more revenue with our proven email marketing strategies.",
    primaryCTA: "Get Email Audit",
    secondaryCTA: "Schedule Demo",
    features: [
      "Free email marketing audit",
      "Custom strategy session",
      "Results within 30 days"
    ],
    backgroundGradient: "from-indigo-600 via-blue-700 to-indigo-700"
  };

  return (
    <main className="min-h-screen">
      <MarketingNavigation />
      <ServiceHero {...heroData} />
      <ServiceFeatures
        title="Complete Email Marketing Solutions"
        subtitle="Drive engagement and conversions with our comprehensive email marketing services"
        features={features}
      />
      <ServicePricing
        title="Email Marketing Packages"
        subtitle="Choose the perfect email marketing package for your business needs"
        tiers={pricingTiers}
      />
      <ServiceCTA {...ctaData} />
    </main>
  );
}
