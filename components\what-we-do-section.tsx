'use client';

import React, { useState, useEffect } from "react";
import Image from 'next/image';
import { motion } from 'framer-motion';
import { Code, Gamepad2, TrendingUp, Palette } from 'lucide-react';

const WhatWeDoSection = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [hoveredCard, setHoveredCard] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const services = [
    {
      icon: Code,
      title: "Software Development",
      desc: "Custom software solutions",
      color: "from-blue-500 to-blue-700",
      bgColor: "#3B82F6"
    },
    {
      icon: Gamepad2,
      title: "Gaming Software",
      desc: "Interactive gaming experiences",
      color: "from-green-500 to-green-700",
      bgColor: "#10B981"
    },
    {
      icon: TrendingUp,
      title: "Digital Marketing",
      desc: "Growth-driven marketing strategies",
      color: "from-purple-500 to-purple-700",
      bgColor: "#8B5CF6"
    },
    {
      icon: Palette,
      title: "Graphics Design",
      desc: "Creative visual solutions",
      color: "from-pink-500 to-pink-700",
      bgColor: "#EC4899"
    }
  ];

  return (
    <>
      <section className="relative w-full py-3 xs:py-4 sm:py-6 md:py-8 lg:py-12 xl:py-16 px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 xl:px-12 bg-background overflow-hidden">
        {/* Enhanced Responsive Decorative Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-purple-50/20 dark:from-blue-950/10 dark:via-transparent dark:to-purple-950/5"></div>

        {/* Responsive animated mesh background */}
        <div className="absolute inset-0 opacity-20 xs:opacity-25 sm:opacity-30">
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-blue-500/5 to-purple-500/5 animate-pulse"></div>
        </div>

        {/* Enhanced floating elements with staggered animations */}
        <motion.div
          className="absolute top-20 left-20 w-80 h-80 bg-blue-400/8 rounded-full blur-3xl -z-10"
          animate={{
            y: [0, -20, 0],
            scale: [1, 1.1, 1],
            opacity: [0.3, 0.6, 0.3]
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute bottom-20 right-20 w-96 h-96 bg-purple-400/8 rounded-full blur-3xl -z-10"
          animate={{
            y: [0, 15, 0],
            scale: [1, 0.9, 1],
            opacity: [0.4, 0.7, 0.4]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
        />

        {/* Interactive floating particles */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className={`absolute w-2 h-2 rounded-full ${i % 2 === 0 ? 'bg-blue-400/40' : 'bg-purple-400/40'}`}
            style={{
              top: `${20 + (i * 15)}%`,
              left: `${10 + (i * 12)}%`,
            }}
            animate={{
              y: [0, -30, 0],
              x: [0, 20, 0],
              opacity: [0.3, 0.8, 0.3],
              scale: [1, 1.5, 1]
            }}
            transition={{
              duration: 4 + i,
              repeat: Infinity,
              ease: "easeInOut",
              delay: i * 0.5
            }}
          />
        ))}

        <div className="container-responsive relative z-10">
          {/* Enhanced Responsive Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 xs:gap-5 sm:gap-6 md:gap-8 items-center mb-6 xs:mb-7 sm:mb-8 md:mb-10 lg:mb-12">
            {/* Enhanced Responsive Content Box */}
            <motion.div
              className="bg-card/80 backdrop-blur-sm text-card-foreground rounded-xl xs:rounded-2xl sm:rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-500 p-3 xs:p-4 sm:p-6 md:p-8 lg:p-10 col-span-1 lg:col-span-2 border border-border/20 group relative overflow-hidden"
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 50 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
              onHoverStart={() => setHoveredCard(true)}
              onHoverEnd={() => setHoveredCard(false)}
              whileHover={{ scale: 1.02, y: -5 }}
            >
              {/* Animated background overlay */}
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-3xl"
                animate={{
                  opacity: hoveredCard ? 1 : 0,
                  scale: hoveredCard ? 1.05 : 1
                }}
                transition={{ duration: 0.3 }}
              />

              {/* Shimmer effect */}
              <div className="absolute inset-0 -top-4 -left-4 bg-gradient-to-r from-transparent via-white/10 to-transparent skew-x-12 group-hover:animate-shimmer"></div>

              <div className="relative z-10">
                <motion.h2
                  className="text-responsive-xl font-extrabold mb-4 xs:mb-5 sm:mb-6 leading-tight bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"
                  animate={{
                    backgroundPosition: hoveredCard ? ['0%', '100%'] : '0%'
                  }}
                  transition={{ duration: 2, repeat: hoveredCard ? Infinity : 0 }}
                >
                  What We Do
                </motion.h2>

                <motion.p
                  className="mt-3 xs:mt-4 text-responsive-base text-muted-foreground leading-relaxed"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.3, duration: 0.6 }}
                >
                  <span className="hidden sm:inline">We offer a wide range of software development services to help you achieve your business goals.</span>
                  <span className="sm:hidden">Wide range of software development services for your business goals.</span>
                </motion.p>

                <motion.div
                  className="mt-8 p-8 bg-gradient-to-r from-blue-50/50 to-purple-50/30 dark:from-blue-950/20 dark:to-purple-950/10 border border-blue-200/30 dark:border-blue-800/30 rounded-2xl group-hover:shadow-lg transition-all duration-500 relative overflow-hidden"
                  whileHover={{ scale: 1.02 }}
                >
                  {/* Animated border */}
                  <motion.div
                    className="absolute inset-0 rounded-2xl border-2 border-gradient-to-r from-blue-400/50 to-purple-400/50"
                    animate={{
                      opacity: hoveredCard ? [0.5, 1, 0.5] : 0
                    }}
                    transition={{ duration: 2, repeat: Infinity }}
                  />

                  <p className="text-foreground text-center text-lg leading-relaxed relative z-10">
                    At BRT, we specialize in turning your innovative ideas into robust software solutions. Our team of expert developers is dedicated to delivering high-quality, scalable, and secure applications tailored to your specific needs.
                  </p>
                </motion.div>
              </div>
            </motion.div>

            {/* Enhanced Image Box */}
            <motion.div
              className="flex justify-center items-center"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: isVisible ? 1 : 0, x: isVisible ? 0 : 50 }}
              transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
            >
              <div className="relative group">
                {/* Enhanced glowing background effect */}
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-3xl blur-xl"
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.3, 0.6, 0.3]
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />

                {/* Rotating ring with service icons */}
                <motion.div
                  className="absolute inset-0 rounded-full"
                  animate={{ rotate: 360 }}
                  transition={{
                    duration: 15,
                    repeat: Infinity,
                    ease: "linear"
                  }}
                >
                  {/* Service icons moving around the circle */}
                  {services.map((service, index) => {
                    const angle = (index * 90) * (Math.PI / 180); // 90 degrees apart
                    const radius = 140; // Perfect distance for circle alignment
                    return (
                      <motion.div
                        key={index}
                        className="absolute w-14 h-14 rounded-2xl flex items-center justify-center backdrop-blur-sm border border-white/20"
                        style={{
                          top: `calc(50% + ${radius * Math.sin(angle)}px)`,
                          left: `calc(50% + ${radius * Math.cos(angle)}px)`,
                          transform: 'translate(-50%, -50%)',
                          background: `linear-gradient(135deg, ${service.bgColor}20, ${service.bgColor}40)`,
                          boxShadow: `0 8px 25px ${service.bgColor}30`,
                        }}
                        animate={{
                          rotate: -360, // Counter-rotate to keep icons upright
                          y: [0, -8, 0], // Floating effect
                        }}
                        transition={{
                          rotate: {
                            duration: 15,
                            repeat: Infinity,
                            ease: "linear"
                          },
                          y: {
                            duration: 2,
                            repeat: Infinity,
                            delay: index * 0.3
                          }
                        }}
                        whileHover={{
                          scale: 1.3,
                          y: -15,
                          boxShadow: `0 15px 35px ${service.bgColor}50`,
                          rotateY: 15,
                        }}
                      >
                        <motion.div
                          className={`p-2 rounded-xl bg-gradient-to-br ${service.color} shadow-lg`}
                          animate={{
                            rotateY: [0, 10, -10, 0],
                          }}
                          transition={{
                            duration: 4,
                            repeat: Infinity,
                            delay: index * 0.5
                          }}
                        >
                          <service.icon className="h-5 w-5 text-white" />
                        </motion.div>
                      </motion.div>
                    );
                  })}
                </motion.div>

                {/* Additional outer rotating ring for more visual appeal */}
                <motion.div
                  className="absolute inset-0 rounded-full border border-purple-400/20"
                  style={{ transform: 'scale(1.2)' }}
                  animate={{ rotate: -360 }}
                  transition={{
                    duration: 30,
                    repeat: Infinity,
                    ease: "linear"
                  }}
                />

                <motion.div
                  whileHover={{
                    scale: 1.05,
                    rotateY: 5,
                    rotateX: 5
                  }}
                  transition={{ duration: 0.3 }}
                >
                  <Image
                    src="/assets/images/boy.png"
                    alt="What We Do Illustration"
                    width={450}
                    height={450}
                    className="relative w-full max-w-sm h-auto object-contain rounded-2xl drop-shadow-2xl z-10"
                    priority
                  />
                </motion.div>

                {/* Enhanced pulsing dots around image */}
                {[...Array(6)].map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute w-2 h-2 bg-gradient-to-r from-blue-400/60 to-purple-400/60 rounded-full"
                    style={{
                      top: `${50 + 50 * Math.sin((i * Math.PI) / 3)}%`,
                      left: `${50 + 50 * Math.cos((i * Math.PI) / 3)}%`,
                    }}
                    animate={{
                      scale: [1, 2, 1],
                      opacity: [0.3, 0.8, 0.3],
                      rotate: 360
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      delay: i * 0.3
                    }}
                  />
                ))}
              </div>
            </motion.div>
          </div>

          
        </div>
      </section>
    </>
  );
};

export default WhatWeDoSection;
