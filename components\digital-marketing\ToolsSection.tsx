"use client";

import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Search, MessageSquare, Heart, Linkedin, Play, BarChart3, 
  Mail, TrendingUp, Users, Zap, Smartphone
} from "lucide-react";

interface Platform {
  name: string;
  icon: React.ComponentType<any>;
  category: string;
}

const platforms: Platform[] = [
  { name: "Google Ads", icon: Search, category: "PPC" },
  { name: "Facebook", icon: MessageSquare, category: "Social" },
  { name: "Instagram", icon: Heart, category: "Social" },
  { name: "LinkedIn", icon: Linkedin, category: "B2B" },
  { name: "Twitter", icon: MessageSquare, category: "Social" },
  { name: "YouTube", icon: Play, category: "Video" },
  { name: "Analytics", icon: BarChart3, category: "Data" },
  { name: "Email", icon: Mail, category: "Email" },
  { name: "SEO Tools", icon: TrendingUp, category: "SEO" },
  { name: "<PERSON><PERSON>", icon: Users, category: "Management" },
  { name: "Automation", icon: Zap, category: "Tools" },
  { name: "Mobile", icon: Smartphone, category: "Mobile" }
];

export function ToolsSection() {
  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8 bg-muted/20">
      <div className="container mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 hero-title animate-fade-in-up">
            Platforms & Tools We Master
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto animate-fade-in-up">
            We leverage the latest tools and platforms to deliver exceptional results across all digital marketing channels.
          </p>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
          {platforms.map((platform, index) => (
            <Card 
              key={index} 
              className="service-card hover:scale-105 transition-all duration-300 group text-center p-6 animate-fade-in-up"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 mb-4 group-hover:shadow-xl transition-all duration-300">
                <platform.icon className="h-8 w-8 text-white" />
              </div>
              <h3 className="font-semibold mb-2 group-hover:text-blue-500 transition-colors">
                {platform.name}
              </h3>
              <Badge variant="secondary" className="text-xs">
                {platform.category}
              </Badge>
            </Card>
          ))}
        </div>
        
        {/* Additional Tools Section */}
        <div className="mt-16 text-center">
          <h3 className="text-2xl font-bold mb-8 text-muted-foreground">
            And Many More Professional Tools
          </h3>
          <div className="flex flex-wrap justify-center gap-4 max-w-4xl mx-auto">
            {[
              "Google Analytics", "SEMrush", "Ahrefs", "HubSpot", "Mailchimp", 
              "Hootsuite", "Buffer", "Canva", "Adobe Creative Suite", "Shopify",
              "WordPress", "Salesforce", "Zapier", "Slack", "Asana"
            ].map((tool, index) => (
              <Badge 
                key={index} 
                variant="outline" 
                className="px-4 py-2 text-sm hover:bg-blue-500 hover:text-white transition-colors cursor-pointer"
              >
                {tool}
              </Badge>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
