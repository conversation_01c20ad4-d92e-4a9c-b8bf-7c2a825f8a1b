"use client";

import { useState } from "react";
import { Card } from "@/components/ui/card";
import { ChevronDown, ChevronUp } from "lucide-react";

interface FAQ {
  question: string;
  answer: string;
}

const faqs: FAQ[] = [
  {
    question: "How long does it take to see results from digital marketing?",
    answer: "Results vary by service and industry, but you can typically expect to see initial improvements within 30-60 days. SEO results may take 3-6 months for significant ranking improvements, while PPC campaigns can show immediate traffic increases. Social media engagement usually improves within the first month."
  },
  {
    question: "Do you work with small businesses and startups?",
    answer: "Absolutely! We work with businesses of all sizes, from startups to enterprise companies. We tailor our strategies and pricing to fit your budget and goals. Our scalable approach means we can grow with your business as it expands."
  },
  {
    question: "What's included in your monthly reporting?",
    answer: "Our comprehensive reports include traffic analytics, conversion tracking, ROI analysis, keyword rankings, social media metrics, and actionable insights for continuous improvement. You'll receive detailed monthly reports plus access to real-time dashboards."
  },
  {
    question: "Can you help optimize our existing marketing campaigns?",
    answer: "Yes, we can audit and optimize your existing campaigns to improve performance and maximize your current marketing investments. We'll analyze what's working, identify areas for improvement, and implement optimizations to boost your ROI."
  },
  {
    question: "What makes your agency different from others?",
    answer: "Our data-driven approach, transparent reporting, and focus on ROI set us apart. We don't just drive traffic – we focus on qualified leads and conversions. Plus, our team stays updated with the latest industry trends and algorithm changes to keep your campaigns ahead of the competition."
  },
  {
    question: "Do you offer custom marketing strategies?",
    answer: "Every strategy we create is custom-tailored to your business goals, target audience, and industry. We don't believe in one-size-fits-all solutions. After a thorough analysis of your business and competitors, we develop a unique strategy that aligns with your objectives."
  },
  {
    question: "What is your pricing structure?",
    answer: "Our pricing is based on the services you need and the scope of work. We offer flexible packages starting from $600/month for basic analytics up to comprehensive marketing solutions. We'll provide a detailed quote after understanding your specific requirements during our free consultation."
  },
  {
    question: "How do you measure campaign success?",
    answer: "We measure success through key performance indicators (KPIs) that align with your business goals, such as conversion rates, cost per acquisition, return on ad spend (ROAS), organic traffic growth, lead quality, and overall ROI. We set clear benchmarks and track progress monthly."
  }
];

export function FAQSection() {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8">
      <div className="container mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 hero-title animate-fade-in-up">
            Frequently Asked Questions
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto animate-fade-in-up">
            Get answers to common questions about our digital marketing services and how we can help grow your business.
          </p>
        </div>
        
        <div className="max-w-4xl mx-auto space-y-4">
          {faqs.map((faq, index) => (
            <Card 
              key={index} 
              className="service-card transition-all duration-300 animate-fade-in-up"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <button
                onClick={() => toggleFAQ(index)}
                className="w-full p-6 text-left flex items-center justify-between hover:bg-muted/20 transition-colors"
              >
                <h3 className="text-lg font-semibold text-blue-500 pr-4">
                  {faq.question}
                </h3>
                {openIndex === index ? (
                  <ChevronUp className="h-5 w-5 text-blue-500 flex-shrink-0" />
                ) : (
                  <ChevronDown className="h-5 w-5 text-blue-500 flex-shrink-0" />
                )}
              </button>
              
              {openIndex === index && (
                <div className="px-6 pb-6">
                  <div className="border-t pt-4">
                    <p className="text-muted-foreground leading-relaxed">
                      {faq.answer}
                    </p>
                  </div>
                </div>
              )}
            </Card>
          ))}
        </div>
        
        <div className="text-center mt-12">
          <p className="text-muted-foreground mb-4">
            Still have questions? We're here to help!
          </p>
          <a 
            href="/contact" 
            className="text-blue-500 hover:text-blue-600 font-semibold transition-colors"
          >
            Contact us for a free consultation →
          </a>
        </div>
      </div>
    </section>
  );
}
