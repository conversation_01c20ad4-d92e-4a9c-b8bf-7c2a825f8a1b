'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { 
  ArrowLeft, 
  Download, 
  Star, 
  Users, 
  Smartphone, 
  Monitor, 
  Gamepad2, 
  Trophy,
  Play,
  Share2,
  Heart,
  MessageCircle,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { Game } from '@/components/data/games-data';
import { Button } from '@/components/ui/button';

interface GameDetailPageProps {
  game: Game;
}

export default function GameDetailPage({ game }: GameDetailPageProps) {
  const [currentScreenshot, setCurrentScreenshot] = useState(0);
  const [isLiked, setIsLiked] = useState(false);

  const nextScreenshot = () => {
    setCurrentScreenshot((prev) => (prev + 1) % game.screenshots.length);
  };

  const prevScreenshot = () => {
    setCurrentScreenshot((prev) => (prev - 1 + game.screenshots.length) % game.screenshots.length);
  };

  const getColorClasses = (color: string) => {
    switch (color) {
      case 'sky':
        return {
          bg: 'bg-sky-500',
          text: 'text-sky-500',
          border: 'border-sky-500',
          gradient: 'from-sky-500 to-sky-600',
          light: 'bg-sky-50 dark:bg-sky-900/20',
          ring: 'ring-sky-500/20'
        };
      case 'purple':
        return {
          bg: 'bg-purple-500',
          text: 'text-purple-500',
          border: 'border-purple-500',
          gradient: 'from-purple-500 to-purple-600',
          light: 'bg-purple-50 dark:bg-purple-900/20',
          ring: 'ring-purple-500/20'
        };
      case 'emerald':
        return {
          bg: 'bg-emerald-500',
          text: 'text-emerald-500',
          border: 'border-emerald-500',
          gradient: 'from-emerald-500 to-emerald-600',
          light: 'bg-emerald-50 dark:bg-emerald-900/20',
          ring: 'ring-emerald-500/20'
        };
      case 'orange':
        return {
          bg: 'bg-orange-500',
          text: 'text-orange-500',
          border: 'border-orange-500',
          gradient: 'from-orange-500 to-orange-600',
          light: 'bg-orange-50 dark:bg-orange-900/20',
          ring: 'ring-orange-500/20'
        };
      default:
        return {
          bg: 'bg-sky-500',
          text: 'text-sky-500',
          border: 'border-sky-500',
          gradient: 'from-sky-500 to-sky-600',
          light: 'bg-sky-50 dark:bg-sky-900/20',
          ring: 'ring-sky-500/20'
        };
    }
  };

  const colors = getColorClasses(game.color);

  return (
    <main className="min-h-screen bg-white dark:bg-slate-950 text-foreground">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800 py-8 sm:py-12 md:py-16 lg:py-20 overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className={`absolute top-20 left-10 w-32 h-32 ${colors.light} rounded-full opacity-60 animate-float`}></div>
          <div className={`absolute bottom-20 right-10 w-24 h-24 ${colors.light} rounded-full opacity-60 animate-float`} style={{ animationDelay: '2s' }}></div>
          <div className={`absolute top-1/2 left-1/4 w-16 h-16 ${colors.light} rounded-full opacity-60 animate-float`} style={{ animationDelay: '4s' }}></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          {/* Back Button */}
          <div className="mb-8">
            <Link 
              href="/gaming-software#featured-games"
              className="inline-flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors duration-300"
            >
              <ArrowLeft className="w-5 h-5" />
              <span>Back to Games</span>
            </Link>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
            {/* Game Info */}
            <div className="space-y-6">
              {/* Badge */}
              <div className={`inline-flex items-center gap-2 px-4 py-2 ${colors.light} rounded-full border ${colors.border}`}>
                <Trophy className={`w-5 h-5 ${colors.text}`} />
                <span className={`text-sm font-semibold ${colors.text}`}>{game.highlight}</span>
              </div>

              {/* Title */}
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white leading-tight">
                {game.name}
              </h1>

              {/* Category & Players */}
              <div className="flex flex-wrap gap-4">
                <div className="flex items-center gap-2">
                  <Gamepad2 className="w-5 h-5 text-gray-500" />
                  <span className="text-gray-600 dark:text-gray-300">{game.category}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="w-5 h-5 text-gray-500" />
                  <span className="text-gray-600 dark:text-gray-300">{game.players}</span>
                </div>
              </div>

              {/* Description */}
              <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
                {game.description}
              </p>

              {/* Rating & Downloads */}
              <div className="flex items-center gap-6">
                <div className="flex items-center gap-2">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <Star 
                        key={i} 
                        className={`w-5 h-5 ${i < Math.floor(game.downloadStats.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`} 
                      />
                    ))}
                  </div>
                  <span className="font-semibold text-gray-900 dark:text-white">{game.downloadStats.rating}</span>
                  <span className="text-gray-500">({game.downloadStats.reviews.toLocaleString()} reviews)</span>
                </div>
                <div className="flex items-center gap-2">
                  <Download className="w-5 h-5 text-gray-500" />
                  <span className="text-gray-600 dark:text-gray-300">{game.downloadStats.downloads} downloads</span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-wrap gap-4">
                <Button 
                  size="lg"
                  className={`bg-gradient-to-r ${colors.gradient} hover:opacity-90 text-white font-semibold px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105`}
                >
                  <Play className="w-5 h-5 mr-2" />
                  Play Now
                </Button>
                <Button 
                  variant="outline"
                  size="lg"
                  onClick={() => setIsLiked(!isLiked)}
                  className={`border-2 ${colors.border} ${isLiked ? colors.bg + ' text-white' : colors.text} hover:${colors.bg} hover:text-white transition-all duration-300`}
                >
                  <Heart className={`w-5 h-5 mr-2 ${isLiked ? 'fill-current' : ''}`} />
                  {isLiked ? 'Liked' : 'Like'}
                </Button>
                <Button 
                  variant="outline"
                  size="lg"
                  className="border-2 border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
                >
                  <Share2 className="w-5 h-5 mr-2" />
                  Share
                </Button>
              </div>
            </div>

            {/* Game Image */}
            <div className="relative">
              <div className="relative bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900 rounded-2xl p-8 shadow-2xl">
                <div className="relative w-full h-80 lg:h-96">
                  <Image
                    src={game.img}
                    alt={game.title}
                    fill
                    className="object-contain drop-shadow-2xl"
                    priority
                  />
                </div>
                
                {/* Floating Elements */}
                <div className={`absolute -top-4 -right-4 w-8 h-8 ${colors.bg} rounded-full opacity-80 animate-bounce`}></div>
                <div className={`absolute -bottom-4 -left-4 w-6 h-6 ${colors.bg} rounded-full opacity-60 animate-bounce`} style={{ animationDelay: '0.5s' }}></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Screenshots Section */}
      <section className="py-16 bg-gray-50 dark:bg-slate-900">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white mb-12">
            Game Screenshots
          </h2>
          
          <div className="relative max-w-4xl mx-auto">
            {/* Main Screenshot */}
            <div className="relative bg-white dark:bg-slate-800 rounded-2xl p-4 shadow-xl">
              <div className="relative w-full h-64 sm:h-80 md:h-96 rounded-xl overflow-hidden">
                <Image
                  src={game.screenshots[currentScreenshot] || game.img}
                  alt={`${game.name} screenshot ${currentScreenshot + 1}`}
                  fill
                  className="object-cover"
                />
              </div>
              
              {/* Navigation Arrows */}
              <button
                onClick={prevScreenshot}
                className="absolute left-2 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-300"
              >
                <ChevronLeft className="w-6 h-6" />
              </button>
              <button
                onClick={nextScreenshot}
                className="absolute right-2 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-300"
              >
                <ChevronRight className="w-6 h-6" />
              </button>
            </div>

            {/* Thumbnail Navigation */}
            <div className="flex justify-center gap-2 mt-6">
              {game.screenshots.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentScreenshot(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentScreenshot ? colors.bg : 'bg-gray-300 dark:bg-gray-600'
                  }`}
                />
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Game Features Section */}
      <section className="py-16 bg-white dark:bg-slate-950">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Features List */}
            <div>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
                Game Features
              </h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {game.gameplayFeatures.map((feature, index) => (
                  <div
                    key={index}
                    className={`flex items-center gap-3 p-4 ${colors.light} rounded-xl border ${colors.border} hover:shadow-lg transition-all duration-300`}
                  >
                    <div className={`w-2 h-2 ${colors.bg} rounded-full`}></div>
                    <span className="text-gray-700 dark:text-gray-300 font-medium">{feature}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Technical Specs */}
            <div>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
                Technical Specifications
              </h2>
              <div className="space-y-6">
                <div className="bg-gray-50 dark:bg-slate-800 rounded-xl p-6">
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-4">Platforms</h3>
                  <div className="flex flex-wrap gap-2">
                    {game.technicalSpecs.platforms.map((platform, index) => (
                      <span
                        key={index}
                        className={`px-3 py-1 ${colors.light} ${colors.text} rounded-full text-sm font-medium border ${colors.border}`}
                      >
                        {platform}
                      </span>
                    ))}
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-slate-800 rounded-xl p-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Engine</h4>
                      <p className="text-gray-600 dark:text-gray-300">{game.technicalSpecs.engine}</p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Graphics</h4>
                      <p className="text-gray-600 dark:text-gray-300">{game.technicalSpecs.graphics}</p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Storage</h4>
                      <p className="text-gray-600 dark:text-gray-300">{game.technicalSpecs.storage}</p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Multiplayer</h4>
                      <p className="text-gray-600 dark:text-gray-300">
                        {game.technicalSpecs.multiplayer ? 'Yes' : 'No'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Game Section */}
      <section className="py-16 bg-gray-50 dark:bg-slate-900">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
              About {game.name}
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-8">
              {game.longDescription}
            </p>

            {/* CTA Section */}
            <div className={`bg-gradient-to-r ${colors.gradient} rounded-2xl p-8 text-white`}>
              <h3 className="text-2xl font-bold mb-4">Ready to Play?</h3>
              <p className="text-lg mb-6 opacity-90">
                Join millions of players and experience the thrill of {game.name}
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <Button
                  size="lg"
                  className="bg-white text-gray-900 hover:bg-gray-100 font-semibold px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                >
                  <Play className="w-5 h-5 mr-2" />
                  Start Playing
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  className="border-2 border-white text-white hover:bg-white hover:text-gray-900 transition-all duration-300"
                >
                  <MessageCircle className="w-5 h-5 mr-2" />
                  Contact Us
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}
