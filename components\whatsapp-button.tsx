"use client";

import { MessageCircle } from "lucide-react";
import { useState } from "react";
import Image from "next/image";
import { FaWhatsapp } from "react-icons/fa";

export function WhatsAppButton() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="fixed bottom-6 right-6 z-50 group">
      {isOpen && (
        <div className="absolute bottom-20 right-0 w-72 bg-background border border-border rounded-xl shadow-2xl p-4 transform transition-all duration-300 origin-bottom-right scale-95 group-hover:scale-100">
          <div className="flex items-start gap-3">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
              <Image src="/assets/brtLogo.webp" alt="Logo" width={40} height={40} className="rounded-full" />
            </div>
            <div>
              <h3 className="font-bold text-foreground">BRT</h3>
              <p className="text-muted-foreground text-sm">We are available on WhatsApp</p>
            </div>
          </div>
          <a
            href="https://wa.me/917972443941"
            target="_blank"
            rel="noopener noreferrer"
            className="mt-3 flex items-center justify-center gap-2 bg-primary text-primary-foreground hover:bg-primary/90 rounded-lg px-4 py-2 text-sm font-semibold transition-colors"
          >
            <FaWhatsapp className="w-5 h-5" />
            Start Chat
          </a>
          <div className="text-center mt-2">
            <div className="text-muted-foreground font-medium text-sm whitespace-nowrap">
              Typically replies within a minute
            </div>
          </div>
        </div>
      )}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative"
        aria-label="Open WhatsApp chat"
      >
        <div className="w-16 h-16 bg-blue-500 hover:bg-blue-600 rounded-full flex items-center justify-center shadow-2xl hover:shadow-blue-500/25 transition-all duration-300 hover:scale-110 group-hover:animate-pulse">
          <FaWhatsapp className="w-8 h-8 text-white" />
        </div>
        {/* Pulsing animation */}
        <div className="absolute inset-0 rounded-full bg-blue-500 opacity-20 animate-ping"></div>
        <div className="absolute inset-0 rounded-full bg-blue-500 opacity-10 animate-ping" style={{ animationDelay: '0.5s' }}></div>
        
        {/* Online indicator */}
        <div className="absolute -top-1 -right-1 w-5 h-5 bg-blue-400 rounded-full border-2 border-white flex items-center justify-center">
          <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse"></div>
        </div>
      </button>
    </div>
  );
}