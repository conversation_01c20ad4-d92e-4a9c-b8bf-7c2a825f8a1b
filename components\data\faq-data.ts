export interface FAQItem {
  id: number;
  question: string;
  answer: string | string[];
}

export const faqData: FAQItem[] = [
  {
    id: 1,
    question: "What is BRT Multi Software?",
    answer: "BRT Multi Software is a leading provider of advanced technology solutions, specializing in blockchain, AI, and Web3 development. We are your trusted partner for digital transformation across industries."
  },
  {
    id: 2,
    question: "Do you belong to multiapisoft.com?",
    answer: "Absolutely! We are proud to operate under the domain multiapisoft.com."
  },
  {
    id: 3,
    question: "How does BRT Multi Software help businesses and enterprises?",
    answer: "From startups to established enterprises, we offer cutting-edge solutions to optimize operations, improve efficiency, and drive growth using technologies like AI, blockchain, and Web3."
  },
  {
    id: 4,
    question: "What primary services does BRT Multi Software provide?",
    answer: [
      "Blockchain Development",
      "Cryptocurrency Exchange Development",
      "Smart Contract Development",
      "Crypto Exchange Development",
      "DeFi Development",
      "Web3 Solutions",
      "AI Development",
      "Game Development"
    ]
  },
  {
    id: 5,
    question: "Does BRT Multi Software provide non-blockchain solutions?",
    answer: "Yes, we cater to both blockchain and non-blockchain needs, offering services like AI-driven applications, Web3 integrations, and more for diverse business sectors."
  },
  {
    id: 6,
    question: "What makes BRT Multi Software unique?",
    answer: "At BRT Multi Software, we combine deep technical expertise with a client-first approach to deliver innovative solutions. Our commitment to quality and adaptability ensures we meet the demands of a fast-evolving tech landscape."
  },
  {
    id: 7,
    question: "Can BRT Multi Software provide dedicated support for projects?",
    answer: "Definitely! We allocate skilled teams to provide end-to-end support for your project, ensuring success at every stage of development."
  },
  {
    id: 8,
    question: "Why should we trust BRT Multi Software?",
    answer: "With years of experience, a proven track record of delivering 500+ successful projects, and a global client base, we have earned a reputation for reliability and excellence in the tech industry."
  },
  {
    id: 9,
    question: "How do we contact BRT Multi Software's representatives?",
    answer: [
      "We're available 24/7 through the following channels:",
      "WhatsApp/Call: +91-7972443941",
      "Telegram: https://t.me/multiapisoftcrypto",
      "Email: <EMAIL>"
    ]
  },
  {
    id: 10,
    question: "Who is BRT Multi Software?",
    answer: "BRT Multi Software is a visionary technology partner helping businesses navigate the digital era with innovative solutions in blockchain, AI, Web3, and more."
  }
];

export const gamingFaqData: FAQItem[] = [
  {
    id: 1,
    question: "What types of games do you develop?",
    answer: "We develop a wide range of games including mobile games (iOS/Android), PC games, console games, VR/AR games, and multiplayer online games. Our expertise covers various genres from casual puzzle games to complex RPGs and action games."
  },
  {
    id: 2,
    question: "How long does it take to develop a game?",
    answer: "Development time varies greatly depending on the complexity and scope of the project. Simple mobile games can take 2-4 months, while complex PC or console games may take 12-24 months or more. We provide detailed timelines during the planning phase."
  },
  {
    id: 3,
    question: "What game engines do you use?",
    answer: "We primarily use Unity and Unreal Engine, but also work with custom engines when needed. We choose the best engine based on your project requirements, target platforms, and performance needs."
  },
  {
    id: 4,
    question: "Do you provide game design services?",
    answer: "Yes, we offer complete game design services including concept development, game mechanics design, level design, user experience optimization, and monetization strategy planning."
  },
  {
    id: 5,
    question: "Can you develop multiplayer games?",
    answer: "Yes, we specialize in multiplayer game development including real-time multiplayer, turn-based games, MMOs, and social games with features like matchmaking, leaderboards, and anti-cheat systems."
  },
  {
    id: 6,
    question: "Do you help with game publishing and marketing?",
    answer: "Yes, we can assist with publishing on various app stores and platforms, as well as provide marketing consultation to help promote your game effectively to your target audience."
  },
  {
    id: 7,
    question: "How do you handle intellectual property?",
    answer: "All intellectual property rights belong to you as the client. We sign comprehensive NDAs and IP agreements to protect your ideas and ensure you maintain full ownership of your game."
  },
  {
    id: 8,
    question: "What platforms can you develop games for?",
    answer: [
      "We develop games for all major platforms:",
      "Mobile: iOS, Android",
      "PC: Windows, Mac, Linux",
      "Console: PlayStation, Xbox, Nintendo Switch",
      "VR/AR: Oculus, HTC Vive, ARKit, ARCore",
      "Web: WebGL, HTML5"
    ]
  },
  {
    id: 9,
    question: "Do you provide post-launch support?",
    answer: "Yes, we offer comprehensive post-launch support including bug fixes, updates, new feature development, server maintenance, and ongoing optimization to ensure your game continues to perform well."
  },
  {
    id: 10,
    question: "How much does game development cost?",
    answer: "Game development costs vary widely based on complexity, features, platforms, and timeline. Simple mobile games start from $15,000, while complex games can cost $100,000+. We provide detailed quotes after understanding your requirements."
  }
];
