"use client";

import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { 
  Award, Users, Target, TrendingUp, ArrowRight, CheckCircle,
  Lightbulb, Shield, Clock, Globe
} from "lucide-react";

const features = [
  {
    icon: Award,
    title: "Proven Expertise",
    description: "Over 8 years of experience delivering successful digital marketing campaigns across various industries."
  },
  {
    icon: Users,
    title: "Dedicated Team",
    description: "A team of certified digital marketing specialists, each expert in their respective fields."
  },
  {
    icon: Target,
    title: "Results-Focused",
    description: "We focus on metrics that matter - conversions, ROI, and sustainable business growth."
  },
  {
    icon: TrendingUp,
    title: "Data-Driven Approach",
    description: "Every strategy is backed by comprehensive data analysis and continuous optimization."
  }
];

const achievements = [
  {
    icon: Globe,
    number: "500+",
    label: "Global Clients Served",
    description: "Businesses worldwide trust us"
  },
  {
    icon: TrendingUp,
    number: "250%",
    label: "Average ROI Increase",
    description: "Proven results across campaigns"
  },
  {
    icon: Award,
    number: "50+",
    label: "Industry Awards",
    description: "Recognition for excellence"
  },
  {
    icon: Clock,
    number: "24/7",
    label: "Support Available",
    description: "Always here when you need us"
  }
];

const values = [
  "Transparency in all our communications and reporting",
  "Continuous learning and adaptation to industry changes",
  "Client success is our primary measure of success",
  "Ethical marketing practices that build trust",
  "Innovation in strategies and implementation",
  "Long-term partnerships over short-term gains"
];

export function AboutSection() {
  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8 bg-muted/20">
      <div className="container mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20">
          {/* Left Content */}
          <div>
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-blue-500/10 border border-blue-500/20 mb-6 animate-fade-in-up">
              <Lightbulb className="h-4 w-4 text-blue-500 mr-2" />
              <span className="text-sm font-medium text-blue-500">About Our Agency</span>
            </div>
            
            <h2 className="text-4xl md:text-5xl font-bold mb-6 hero-title animate-fade-in-up">
              Driving Digital Success Since 2016
            </h2>
            
            <p className="text-xl text-muted-foreground mb-8 leading-relaxed animate-fade-in-up">
              We're a full-service digital marketing agency passionate about helping businesses grow through strategic, 
              data-driven marketing solutions. Our team combines creativity with analytics to deliver campaigns that 
              not only look great but drive real results.
            </p>
            
            <div className="space-y-4 mb-8">
              {values.map((value, index) => (
                <div 
                  key={index} 
                  className="flex items-start space-x-3 animate-fade-in-up"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                  <span className="text-muted-foreground">{value}</span>
                </div>
              ))}
            </div>
            
            <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white group animate-fade-in-up">
              Learn More About Us
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Button>
          </div>
          
          {/* Right Content - Features */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            {features.map((feature, index) => (
              <Card 
                key={index} 
                className="service-card hover:scale-105 transition-all duration-300 group animate-fade-in-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <CardContent className="p-6">
                  <div className="inline-flex items-center justify-center w-12 h-12 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 mb-4 group-hover:shadow-xl transition-all duration-300">
                    <feature.icon className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold mb-3 group-hover:text-blue-500 transition-colors">
                    {feature.title}
                  </h3>
                  <p className="text-muted-foreground text-sm leading-relaxed">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
        
        {/* Achievements Section */}
        <div className="text-center mb-12">
          <h3 className="text-3xl font-bold mb-4 hero-title animate-fade-in-up">
            Our Track Record Speaks for Itself
          </h3>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto animate-fade-in-up">
            Numbers that showcase our commitment to delivering exceptional results for our clients.
          </p>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          {achievements.map((achievement, index) => (
            <Card 
              key={index} 
              className="service-card text-center p-6 hover:scale-105 transition-all duration-300 group animate-fade-in-up"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 mb-4 group-hover:shadow-xl transition-all duration-300">
                <achievement.icon className="h-8 w-8 text-white" />
              </div>
              <div className="text-3xl font-bold text-blue-500 mb-2">{achievement.number}</div>
              <div className="font-semibold mb-2">{achievement.label}</div>
              <div className="text-sm text-muted-foreground">{achievement.description}</div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
