"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Search, MousePointer, MessageSquare, Mail, BarChart3, Globe,
  CheckCircle, ArrowRight
} from "lucide-react";

interface Service {
  icon: React.ComponentType<any>;
  title: string;
  description: string;
  features: string[];
  price: string;
  popular: boolean;
}

const services: Service[] = [
  {
    icon: Search,
    title: "Search Engine Optimization",
    description: "Boost your organic visibility with advanced SEO strategies that drive qualified traffic and improve search rankings.",
    features: ["Keyword Research", "On-Page SEO", "Technical SEO", "Link Building"],
    price: "Starting at $1,500/month",
    popular: false
  },
  {
    icon: MousePointer,
    title: "Pay-Per-Click Advertising",
    description: "Maximize ROI with data-driven PPC campaigns across Google Ads, Facebook, and other platforms.",
    features: ["Campaign Setup", "Ad Creation", "Bid Management", "Performance Tracking"],
    price: "Starting at $2,000/month",
    popular: true
  },
  {
    icon: MessageSquare,
    title: "Social Media Marketing",
    description: "Build engaged communities and amplify your brand presence across all major social platforms.",
    features: ["Content Creation", "Community Management", "Influencer Outreach", "Social Advertising"],
    price: "Starting at $1,200/month",
    popular: false
  },
  {
    icon: Mail,
    title: "Email Marketing",
    description: "Nurture leads and retain customers with personalized email campaigns that convert.",
    features: ["Email Design", "Automation Setup", "List Management", "A/B Testing"],
    price: "Starting at $800/month",
    popular: false
  },
  {
    icon: BarChart3,
    title: "Analytics & Reporting",
    description: "Make data-driven decisions with comprehensive tracking and detailed performance insights.",
    features: ["Custom Dashboards", "Monthly Reports", "Goal Tracking", "ROI Analysis"],
    price: "Starting at $600/month",
    popular: false
  },
  {
    icon: Globe,
    title: "Content Marketing",
    description: "Attract and engage your audience with high-quality content that drives organic growth.",
    features: ["Content Strategy", "Blog Writing", "Video Production", "Content Distribution"],
    price: "Starting at $1,000/month",
    popular: false
  }
];

export function ServicesSection() {
  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8">
      <div className="container mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 hero-title animate-fade-in-up">
            Our Digital Marketing Services
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto animate-fade-in-up">
            Comprehensive digital marketing solutions designed to drive growth, increase visibility, and maximize your return on investment.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <Card 
              key={index} 
              className={`service-card hover:scale-105 transition-all duration-300 group relative animate-fade-in-up ${
                service.popular ? 'ring-2 ring-blue-500' : ''
              }`}
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              {service.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-blue-500 text-white px-4 py-1">Most Popular</Badge>
                </div>
              )}
              <CardContent className="p-8">
                <div className="flex items-center mb-6">
                  <div className="p-3 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 shadow-lg mr-4 group-hover:shadow-xl transition-all duration-300">
                    <service.icon className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold group-hover:text-blue-500 transition-colors">
                    {service.title}
                  </h3>
                </div>
                
                <p className="text-muted-foreground mb-6 leading-relaxed">
                  {service.description}
                </p>
                
                <ul className="space-y-3 mb-6">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center text-sm">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-3 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>
                
                <div className="border-t pt-6">
                  <div className="text-2xl font-bold text-blue-500 mb-4">{service.price}</div>
                  <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white group">
                    Get Started
                    <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
