"use client";

import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, ArrowRight, Star } from "lucide-react";

interface PricingTier {
  name: string;
  price: string;
  period: string;
  description: string;
  features: string[];
  popular?: boolean;
  cta: string;
  badge?: string;
}

interface ServicePricingProps {
  title: string;
  subtitle: string;
  tiers: PricingTier[];
}

export function ServicePricing({ title, subtitle, tiers }: ServicePricingProps) {
  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8 bg-muted/20">
      <div className="container mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 hero-title animate-fade-in-up">
            {title}
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto animate-fade-in-up">
            {subtitle}
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {tiers.map((tier, index) => (
            <Card 
              key={index} 
              className={`service-card hover:scale-105 transition-all duration-500 group relative animate-fade-in-up ${
                tier.popular ? 'ring-2 ring-blue-500 scale-105' : ''
              }`}
              style={{ 
                animationDelay: `${index * 0.1}s`,
                transform: tier.popular 
                  ? 'perspective(1000px) rotateY(0deg) rotateX(-2deg) scale(1.05)' 
                  : `perspective(1000px) rotateY(${(index % 3 - 1) * 3}deg) rotateX(1deg)`
              }}
            >
              {tier.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                  <Badge className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-2 animate-pulse">
                    <Star className="h-4 w-4 mr-1" />
                    {tier.badge || 'Most Popular'}
                  </Badge>
                </div>
              )}
              
              <CardContent className="p-8 relative overflow-hidden">
                {/* 3D Background Glow */}
                <div className={`absolute inset-0 bg-gradient-to-br ${
                  tier.popular 
                    ? 'from-blue-500/5 to-purple-500/5' 
                    : 'from-gray-500/5 to-gray-500/5'
                } rounded-lg group-hover:from-blue-500/10 group-hover:to-purple-500/10 transition-all duration-500`}></div>
                
                {/* Floating Elements */}
                <div className="absolute top-4 right-4 w-8 h-8 bg-blue-500/10 rounded-full animate-float"></div>
                <div className="absolute bottom-8 left-4 w-6 h-6 bg-purple-500/10 rounded-full animate-float delay-500"></div>
                
                <div className="relative z-10">
                  <div className="text-center mb-8">
                    <h3 className="text-2xl font-bold mb-2 group-hover:text-blue-500 transition-colors">
                      {tier.name}
                    </h3>
                    <p className="text-muted-foreground text-sm mb-4">
                      {tier.description}
                    </p>
                    
                    <div className="mb-6">
                      <span className="text-4xl font-bold text-blue-500 animate-stats-bounce">
                        {tier.price}
                      </span>
                      <span className="text-muted-foreground">/{tier.period}</span>
                    </div>
                  </div>
                  
                  <ul className="space-y-3 mb-8">
                    {tier.features.map((feature, featureIndex) => (
                      <li 
                        key={featureIndex} 
                        className="flex items-center text-sm opacity-0 animate-fade-in-up"
                        style={{ animationDelay: `${(index * 0.1) + (featureIndex * 0.05)}s` }}
                      >
                        <CheckCircle className="h-4 w-4 text-green-500 mr-3 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  
                  <Button 
                    className={`w-full group transition-all duration-300 ${
                      tier.popular 
                        ? 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl' 
                        : 'bg-blue-600 hover:bg-blue-700 text-white'
                    }`}
                  >
                    {tier.cta}
                    <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        
        <div className="text-center mt-12">
          <p className="text-muted-foreground mb-4">
            Need a custom solution? We've got you covered.
          </p>
          <Button variant="outline" size="lg" className="group">
            Contact for Custom Pricing
            <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
          </Button>
        </div>
      </div>
    </section>
  );
}
