"use client";

import { useState } from "react";
import { MarketingNavigation } from "@/components/marketing-navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { 
  ChevronDown, ChevronUp, Search, HelpCircle, ArrowRight,
  MessageSquare, Phone, Calendar, CheckCircle, Star,
  Clock, Shield, Zap, Award
} from "lucide-react";

export default function FAQPage() {
  const [openIndex, setOpenIndex] = useState<number | null>(null);
  const [searchTerm, setSearchTerm] = useState("");

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  const faqs = [
    {
      category: "General",
      question: "What digital marketing services do you offer?",
      answer: "We offer a comprehensive range of digital marketing services including SEO, PPC advertising, social media marketing, email marketing, content marketing, and analytics & reporting. Each service is tailored to meet your specific business goals and target audience."
    },
    {
      category: "Pricing",
      question: "How much do your services cost?",
      answer: "Our pricing varies based on the services you need and the scope of work. We offer packages starting from $600/month for basic analytics up to enterprise solutions. We provide custom quotes after understanding your specific requirements during our free consultation."
    },
    {
      category: "Results",
      question: "How long does it take to see results?",
      answer: "Results vary by service and industry. PPC campaigns can show immediate traffic increases, while SEO typically takes 3-6 months for significant ranking improvements. Social media engagement usually improves within the first month. We provide realistic timelines during our initial consultation."
    },
    {
      category: "Process",
      question: "What is your onboarding process?",
      answer: "Our onboarding process includes: 1) Free consultation and audit, 2) Strategy development and proposal, 3) Contract signing and account setup, 4) Campaign launch and initial optimization, 5) Regular monitoring and reporting. The entire process typically takes 1-2 weeks."
    },
    {
      category: "Reporting",
      question: "How do you measure and report success?",
      answer: "We use comprehensive analytics tools to track KPIs relevant to your goals, such as traffic growth, conversion rates, ROI, and engagement metrics. You'll receive detailed monthly reports with insights and recommendations, plus access to real-time dashboards."
    },
    {
      category: "Support",
      question: "What kind of support do you provide?",
      answer: "We provide dedicated account management, regular strategy sessions, 24/7 monitoring of campaigns, and responsive support via email and phone. Enterprise clients get priority support and weekly check-ins with their dedicated account manager."
    },
    {
      category: "Contracts",
      question: "Do you require long-term contracts?",
      answer: "No, we don't require long-term contracts. While we recommend at least 6 months for optimal results, especially for SEO, you can cancel with 30 days notice. We believe in earning your business through results, not binding contracts."
    },
    {
      category: "Industries",
      question: "Do you work with businesses in my industry?",
      answer: "We work with businesses across various industries including e-commerce, SaaS, healthcare, finance, real estate, and more. Our strategies are customized based on industry best practices and your specific market dynamics."
    },
    {
      category: "Getting Started",
      question: "How do I get started?",
      answer: "Getting started is easy! Contact us for a free consultation where we'll discuss your goals, audit your current digital presence, and provide recommendations. If you decide to work with us, we'll create a custom strategy and timeline for your business."
    },
    {
      category: "ROI",
      question: "What kind of ROI can I expect?",
      answer: "ROI varies by industry and service, but our clients typically see 3-5x return on their marketing investment. We focus on metrics that matter to your business, whether that's leads, sales, or brand awareness, and optimize campaigns for maximum ROI."
    }
  ];

  const categories = ["All", ...Array.from(new Set(faqs.map(faq => faq.category)))];
  const [selectedCategory, setSelectedCategory] = useState("All");

  const filteredFAQs = faqs.filter(faq => {
    const matchesCategory = selectedCategory === "All" || faq.category === selectedCategory;
    const matchesSearch = faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const stats = [
    { number: "500+", label: "Questions Answered", icon: HelpCircle },
    { number: "98%", label: "Client Satisfaction", icon: Star },
    { number: "24/7", label: "Support Available", icon: Clock },
    { number: "2hrs", label: "Average Response Time", icon: Zap }
  ];

  return (
    <main className="min-h-screen">
      <MarketingNavigation />
      
      {/* Hero Section */}
      <section className="relative pt-24 pb-20 px-4 sm:px-6 lg:px-8 overflow-hidden">
        {/* 3D Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900/10 via-purple-900/5 to-blue-900/10"></div>
        <div className="absolute top-20 left-10 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-float delay-1000"></div>
        
        <div className="container mx-auto relative z-10">
          <div className="text-center max-w-4xl mx-auto mb-16">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-blue-500/10 border border-blue-500/20 mb-6 animate-fade-in-up">
              <HelpCircle className="h-4 w-4 text-blue-500 mr-2" />
              <span className="text-sm font-medium text-blue-500">Frequently Asked Questions</span>
            </div>
            
            <h1 className="text-5xl md:text-7xl font-bold mb-6 hero-title leading-tight animate-fade-in-up">
              Got Questions?
              <span className="block bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent">
                We Have Answers
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed animate-fade-in-up">
              Find answers to common questions about our digital marketing services, processes, and how we can help your business grow.
            </p>
            
            {/* Search Bar */}
            <div className="relative max-w-2xl mx-auto mb-8 animate-fade-in-up">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Search for answers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-12 pr-4 py-4 text-lg rounded-xl border-2 border-blue-500/20 focus:border-blue-500"
              />
            </div>
          </div>
          
          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <div 
                key={index} 
                className="text-center p-6 service-card rounded-xl hover:scale-105 transition-all duration-300 animate-fade-in-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="inline-flex items-center justify-center w-12 h-12 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 mb-3">
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
                <div className="text-2xl md:text-3xl font-bold text-blue-500 mb-1 animate-stats-bounce">{stat.number}</div>
                <div className="text-sm text-muted-foreground">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          {/* Category Filter */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {categories.map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                onClick={() => setSelectedCategory(category)}
                className={`${
                  selectedCategory === category 
                    ? "bg-blue-600 text-white" 
                    : "hover:bg-blue-50"
                }`}
              >
                {category}
              </Button>
            ))}
          </div>
          
          {/* FAQ List */}
          <div className="max-w-4xl mx-auto space-y-4">
            {filteredFAQs.map((faq, index) => (
              <Card 
                key={index} 
                className="service-card transition-all duration-300 animate-fade-in-up"
                style={{ animationDelay: `${index * 0.05}s` }}
              >
                <button
                  onClick={() => toggleFAQ(index)}
                  className="w-full p-6 text-left flex items-center justify-between hover:bg-muted/20 transition-colors"
                >
                  <div className="flex-1">
                    <div className="flex items-center mb-2">
                      <span className="text-xs font-semibold text-blue-500 bg-blue-500/10 px-2 py-1 rounded-full mr-3">
                        {faq.category}
                      </span>
                    </div>
                    <h3 className="text-lg font-semibold text-blue-500 pr-4">
                      {faq.question}
                    </h3>
                  </div>
                  {openIndex === index ? (
                    <ChevronUp className="h-5 w-5 text-blue-500 flex-shrink-0" />
                  ) : (
                    <ChevronDown className="h-5 w-5 text-blue-500 flex-shrink-0" />
                  )}
                </button>
                
                {openIndex === index && (
                  <div className="px-6 pb-6">
                    <div className="border-t pt-4">
                      <p className="text-muted-foreground leading-relaxed">
                        {faq.answer}
                      </p>
                    </div>
                  </div>
                )}
              </Card>
            ))}
          </div>
          
          {filteredFAQs.length === 0 && (
            <div className="text-center py-12">
              <HelpCircle className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">No results found</h3>
              <p className="text-muted-foreground">
                Try adjusting your search terms or browse all categories.
              </p>
            </div>
          )}
          
          <div className="text-center mt-12">
            <p className="text-muted-foreground mb-4">
              Still have questions? We're here to help!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button className="bg-blue-600 hover:bg-blue-700 text-white group">
                <MessageSquare className="mr-2 h-5 w-5" />
                Contact Support
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button variant="outline" className="group">
                <Calendar className="mr-2 h-5 w-5" />
                Schedule Consultation
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600 via-blue-700 to-purple-700"></div>
        <div className="absolute top-10 left-10 w-72 h-72 bg-white/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-10 right-10 w-96 h-96 bg-white/3 rounded-full blur-3xl animate-pulse delay-1000"></div>
        
        <div className="container mx-auto text-center relative z-10">
          <div className="max-w-4xl mx-auto text-white">
            <h2 className="text-4xl md:text-6xl font-bold mb-6 leading-tight animate-fade-in-up">
              Ready to Get
              <span className="block bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">
                Started?
              </span>
            </h2>
            
            <p className="text-xl md:text-2xl mb-8 opacity-90 leading-relaxed animate-fade-in-up">
              Don't let questions hold you back. Contact us today and let's discuss your digital marketing goals.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-6 justify-center mb-8 animate-fade-in-up">
              <Button 
                size="lg" 
                className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 text-lg font-semibold group"
              >
                <Calendar className="mr-2 h-5 w-5" />
                Book Free Consultation
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button 
                size="lg" 
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4 text-lg font-semibold"
              >
                <Phone className="mr-2 h-5 w-5" />
                Call Us Now
              </Button>
            </div>
            
            <div className="flex flex-col sm:flex-row items-center justify-center gap-8 text-sm opacity-80">
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 mr-2 text-green-400" />
                Free consultation & audit
              </div>
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 mr-2 text-green-400" />
                No long-term contracts
              </div>
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 mr-2 text-green-400" />
                Expert guidance
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}
