'use client';

import React from 'react';
import { Trophy, Zap, Users, Shield, Gamepad2, Code, Globe, Headphones } from 'lucide-react';
import Image from 'next/image';

const features = [
  {
    icon: Trophy,
    title: 'Award-Winning Games',
    description: 'Our games have won multiple industry awards and achieved millions of downloads worldwide.',
    color: '#0EA5E9', // sky-500 (plasma blue)
    stats: '50+ Awards'
  },
  {
    icon: Zap,
    title: 'High-Performance Engines',
    description: 'Optimized game engines that deliver smooth 60+ FPS gameplay across all platforms.',
    color: '#A855F7', // purple-500 (laser purple)
    stats: '60+ FPS'
  },
  {
    icon: Users,
    title: 'Expert Team',
    description: 'Seasoned game developers with 10+ years of experience in AAA game development.',
    color: '#10B981', // emerald-500 (energy green)
    stats: '50+ Developers'
  },
  {
    icon: Shield,
    title: 'Secure & Scalable',
    description: 'Enterprise-grade security and cloud infrastructure supporting millions of players.',
    color: '#0EA5E9', // sky-500 (plasma blue)
    stats: '99.9% Uptime'
  },
  {
    icon: Code,
    title: 'Cross-Platform',
    description: 'Deploy your game across mobile, PC, console, and VR platforms with a single codebase.',
    color: '#A855F7', // purple-500 (laser purple)
    stats: '8+ Platforms'
  },
  {
    icon: Globe,
    title: 'Global Reach',
    description: 'Games published in 50+ countries with localization and cultural adaptation.',
    color: '#10B981', // emerald-500 (energy green)
    stats: '50+ Countries'
  },
  {
    icon: Gamepad2,
    title: 'Latest Technologies',
    description: 'Cutting-edge tech including AI, blockchain, AR/VR, and cloud gaming solutions.',
    color: '#0EA5E9', // sky-500 (plasma blue)
    stats: 'AI Powered'
  },
  {
    icon: Headphones,
    title: '24/7 Support',
    description: 'Round-the-clock technical support and maintenance for your gaming projects.',
    color: '#A855F7', // purple-500 (laser purple)
    stats: '24/7 Available'
  }
];

const GamingWhyChooseUs: React.FC = () => {
  return (
    <section className="relative py-8 xs:py-10 sm:py-12 md:py-16 lg:py-20 px-2 xs:px-3 sm:px-4 overflow-hidden bg-white dark:bg-slate-950 transition-all duration-500">
      {/* Clean Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Simple Floating Circles */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-sky-100 dark:bg-sky-800/40 rounded-full opacity-60 animate-float border border-sky-200 dark:border-sky-700"></div>
        <div className="absolute bottom-20 right-10 w-24 h-24 bg-purple-100 dark:bg-purple-800/40 rounded-full opacity-60 animate-float border border-purple-200 dark:border-purple-700" style={{ animationDelay: '2s' }}></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-emerald-100 dark:bg-emerald-800/40 rounded-full opacity-60 animate-float border border-emerald-200 dark:border-emerald-700" style={{ animationDelay: '4s' }}></div>

        {/* Simple Gaming Icons */}
        <div className="absolute top-20 right-20 w-8 h-8 text-sky-500 dark:text-sky-400 animate-bounce" style={{ animationDuration: '3s' }}>
          <Trophy className="w-full h-full" />
        </div>
        <div className="absolute bottom-1/3 left-20 w-6 h-6 text-purple-500 dark:text-purple-400 animate-bounce" style={{ animationDuration: '4s', animationDelay: '1s' }}>
          <Zap className="w-full h-full" />
        </div>
        <div className="absolute top-1/3 right-1/3 w-7 h-7 text-emerald-500 dark:text-emerald-400 animate-bounce" style={{ animationDuration: '5s', animationDelay: '2s' }}>
          <Users className="w-full h-full" />
        </div>
      </div>

      <div className="relative container-responsive">
        {/* Clean Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 px-4 py-2 mb-6 bg-sky-50 dark:bg-sky-900/30 rounded-full border border-sky-200 dark:border-sky-700">
            <Trophy className="w-5 h-5 text-sky-500 dark:text-sky-400" />
            <span className="text-sm font-semibold text-sky-600 dark:text-sky-300">Why Choose Us</span>
          </div>

          <h2 className="text-3xl xs:text-4xl sm:text-5xl font-bold mb-6 text-gray-900 dark:text-white">
            Why Choose Us for <span className="text-sky-500 dark:text-sky-400">Game Development?</span>
          </h2>

          <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto leading-relaxed">
            We combine cutting-edge technology with creative excellence to deliver games that captivate players worldwide
          </p>
        </div>

        {/* Enhanced Features Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {features.map((feature, index) => {
            const IconComponent = feature.icon;
            const colors = ['blue', 'indigo', 'emerald', 'amber', 'rose', 'violet', 'cyan', 'orange'];
            const cardColor = colors[index % colors.length];

            return (
              <div
                key={index}
                className="group relative bg-white dark:bg-blue-900 rounded-2xl p-6 shadow-lg hover:shadow-xl border border-gray-200 dark:border-blue-700 transition-all duration-300 hover:-translate-y-3 hover:scale-105 overflow-hidden"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                {/* Enhanced Side Color Overlap */}
                <div className={`absolute -left-3 top-4 bottom-4 w-6 bg-gradient-to-b from-${cardColor}-400 via-${cardColor}-500 to-${cardColor}-600 dark:from-${cardColor}-500 dark:via-${cardColor}-600 dark:to-${cardColor}-700 rounded-r-xl shadow-lg z-20`}></div>

                {/* Top Highlight Text */}
                <div className={`absolute -top-2 left-6 right-6 bg-${cardColor}-500 dark:bg-${cardColor}-600 rounded-lg px-3 py-1 z-20`}>
                  <span className="text-white text-xs font-bold tracking-wide">
                    {feature.title.split(' ')[0]}
                  </span>
                </div>

                {/* Background Overlay */}
                <div className={`absolute inset-0 bg-${cardColor}-50 dark:bg-${cardColor}-900/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl`}></div>

                {/* Stats Badge */}
                <div className={`absolute top-3 right-3 px-2 py-1 bg-${cardColor}-100 dark:bg-${cardColor}-800 rounded-lg border border-${cardColor}-200 dark:border-${cardColor}-600`}>
                  <span className={`text-${cardColor}-700 dark:text-${cardColor}-300 text-xs font-bold`}>
                    {feature.stats}
                  </span>
                </div>

                {/* Icon Container */}
                <div className="flex items-center justify-center mb-4 mt-6 relative z-10">
                  <div className={`w-16 h-16 rounded-xl bg-${cardColor}-100 dark:bg-${cardColor}-800 flex items-center justify-center group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 border-2 border-${cardColor}-200 dark:border-${cardColor}-600 shadow-md`}>
                    <IconComponent className={`w-8 h-8 text-${cardColor}-600 dark:text-${cardColor}-400`} />
                  </div>
                </div>

                {/* Content */}
                <div className="text-center relative z-10">
                  <h3 className={`text-lg font-bold mb-3 text-gray-900 dark:text-white group-hover:text-${cardColor}-600 dark:group-hover:text-${cardColor}-400 transition-colors duration-300`}>
                    {feature.title}
                  </h3>

                  <p className="text-gray-600 dark:text-gray-200 leading-relaxed text-sm">
                    {feature.description}
                  </p>

                 
                </div>

              </div>
            );
          })}
        </div>

        {/* Gaming Banner Section */}
        <div className="mt-12 xs:mt-14 sm:mt-16 mb-8 xs:mb-10 sm:mb-12 animate-fade-in-up" style={{ animationDelay: '0.8s' }}>
          <div className="relative bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 rounded-2xl overflow-hidden shadow-2xl">
            <div className="absolute inset-0 opacity-20">
              <Image
                src="/gaming/game-banner.png"
                alt="Gaming Development Banner"
                fill
                className="object-cover"
              />
            </div>
            <div className="relative grid grid-cols-1 lg:grid-cols-2 gap-8 p-6 xs:p-8 sm:p-10">
              {/* Content Side */}
              <div className="text-white">
                <h3 className="text-2xl xs:text-3xl font-bold mb-4">
                  Professional Game Development Services
                </h3>
                <p className="text-blue-100 mb-6 leading-relaxed">
                  From concept to launch, we create engaging gaming experiences that captivate players worldwide.
                  Our expert team delivers high-quality games across all platforms.
                </p>
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-200">150+</div>
                    <div className="text-sm text-blue-100">Games Developed</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-200">5M+</div>
                    <div className="text-sm text-blue-100">Players Reached</div>
                  </div>
                </div>
              </div>

              {/* Image Side */}
              <div className="relative">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-4">
                    <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 hover:bg-white/20 transition-all duration-300">
                      <Image
                        src="/gaming/ludogame.png"
                        alt="Ludo Game"
                        width={80}
                        height={80}
                        className="w-full h-16 object-cover rounded-md mb-2"
                      />
                      <p className="text-white text-xs text-center">Ludo Game</p>
                    </div>
                    <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 hover:bg-white/20 transition-all duration-300">
                      <Image
                        src="/gaming/rumygame.png"
                        alt="Rummy Game"
                        width={80}
                        height={80}
                        className="w-full h-16 object-cover rounded-md mb-2"
                      />
                      <p className="text-white text-xs text-center">Rummy Game</p>
                    </div>
                  </div>
                  <div className="space-y-4 mt-8">
                    <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 hover:bg-white/20 transition-all duration-300">
                      <Image
                        src="/gaming/teenpatti.png"
                        alt="Teen Patti Game"
                        width={80}
                        height={80}
                        className="w-full h-16 object-cover rounded-md mb-2"
                      />
                      <p className="text-white text-xs text-center">Teen Patti</p>
                    </div>
                    <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 hover:bg-white/20 transition-all duration-300">
                      <Image
                        src="/gaming/pockergame.png"
                        alt="Poker Game"
                        width={80}
                        height={80}
                        className="w-full h-16 object-cover rounded-md mb-2"
                      />
                      <p className="text-white text-xs text-center">Poker Game</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      
      </div>
    </section>
  );
};

export default GamingWhyChooseUs;
