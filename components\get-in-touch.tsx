'use client';
import React, { useState } from "react";
import { Phone, MapPin, Mail, MessageSquare, Send, User, Building2 } from "lucide-react";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Textarea } from "./ui/textarea";

const GetInTouch: React.FC = () => {
  const [form, setForm] = useState({
    name: "",
    email: "",
    mobile: "",
    subject: "",
    message: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formStatus, setFormStatus] = useState('');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 1500));

    setFormStatus('Message sent successfully!');
    setIsSubmitting(false);
    setForm({ name: "", email: "", mobile: "", subject: "", message: "" });

    setTimeout(() => setFormStatus(''), 4000);
  };

  return (
    <section className="relative py-8 px-4 sm:px-6 lg:px-8 bg-transparent overflow-hidden">
      {/* Enhanced floating elements with varied colors */}
      <div className="absolute top-10 left-10 w-72 h-72 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-full blur-3xl animate-float" />
      <div className="absolute top-32 right-20 w-64 h-64 bg-gradient-to-r from-emerald-500/10 to-cyan-500/10 rounded-full blur-3xl animate-bounce-gentle" />
      <div className="absolute bottom-20 left-32 w-80 h-80 bg-gradient-to-r from-rose-500/10 to-orange-500/10 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }} />

      {/* Corner SVG shapes */}
      <div className="absolute top-8 right-8 w-24 h-24 opacity-20">
        <svg viewBox="0 0 100 100" className="w-full h-full text-primary animate-spin-slow">
          <circle cx="50" cy="50" r="40" fill="none" stroke="currentColor" strokeWidth="2" strokeDasharray="10,5" />
        </svg>
      </div>
      <div className="absolute bottom-8 left-8 w-20 h-20 opacity-20">
        <svg viewBox="0 0 100 100" className="w-full h-full text-secondary animate-bounce-gentle">
          <circle cx="50" cy="50" r="35" fill="currentColor" />
        </svg>
      </div>

      <div className="relative max-w-7xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-8 animate-fade-in-up">
          <div className="inline-flex items-center gap-3 mb-3 px-5 py-2 rounded-full bg-primary/10 border border-primary/20">
            <MessageSquare className="w-5 h-5 text-primary animate-bounce-gentle" />
            <span className="text-primary font-medium">Contact Us</span>
          </div>
          <h2 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-primary via-secondary to-primary bg-clip-text text-transparent mb-3">
            Let's Start a Conversation
          </h2>
          <p className="text-base text-muted-foreground max-w-2xl mx-auto">
            Ready to transform your ideas into reality? We're here to help you every step of the way.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-6 items-stretch">
          {/* Left - Contact Details */}
          <div className="relative p-6 rounded-2xl bg-gradient-to-br from-card/80 to-card/40 backdrop-blur-xl border border-border/50 shadow-xl animate-slide-in-left flex flex-col h-full">
            {/* Corner decoration */}
            <div className="absolute top-4 right-4 w-16 h-16 opacity-30">
              <svg viewBox="0 0 100 100" className="w-full h-full text-primary">
                <circle cx="50" cy="50" r="30" fill="currentColor" />
              </svg>
            </div>

            <div className="relative z-10 flex flex-col h-full">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 rounded-lg bg-gradient-to-r from-primary/20 to-secondary/20 shadow-lg animate-glow">
                  <Building2 className="w-5 h-5 text-primary" />
                </div>
                <h3 className="text-xl font-bold text-foreground">Contact Information</h3>
              </div>

              <p className="text-muted-foreground mb-6 leading-relaxed text-sm">
                Ready to discuss your project? Reach out to us through any of these channels.
              </p>

              <div className="flex-grow">
                <div className="flex flex-col sm:flex-row gap-4 p-4 rounded-xl bg-gradient-to-r from-primary/5 to-secondary/5 border border-border/30">
                  <div className="group flex items-center gap-2 flex-1">
                    <div className="p-1.5 rounded-lg bg-gradient-to-r from-green-500/20 to-emerald-500/20 group-hover:scale-110 transition-transform duration-300">
                      <Phone className="text-green-600 dark:text-green-400 w-3 h-3" />
                    </div>
                    <div>
                      <p className="text-muted-foreground text-xs">+91-7972443941</p>
                    </div>
                  </div>

                  <div className="group flex items-center gap-2 flex-1">
                    <div className="p-1.5 rounded-lg bg-gradient-to-r from-blue-500/20 to-cyan-500/20 group-hover:scale-110 transition-transform duration-300">
                      <Mail className="text-blue-600 dark:text-blue-400 w-3 h-3" />
                    </div>
                    <div>
                      <p className="text-muted-foreground text-xs"><EMAIL></p>
                    </div>
                  </div>

                  <div className="group flex items-center gap-2 flex-1">
                    <div className="p-1.5 rounded-lg bg-gradient-to-r from-purple-500/20 to-pink-500/20 group-hover:scale-110 transition-transform duration-300">
                      <MapPin className="text-purple-600 dark:text-purple-400 w-3 h-3" />
                    </div>
                    <div>
                      <p className="text-muted-foreground text-xs">Greater Noida, India</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Google Map */}
            <div className="mt-6">
              <div className="flex items-center gap-2 mb-3">
                <div className="p-1.5 rounded-lg bg-gradient-to-r from-orange-500/20 to-red-500/20">
                  <MapPin className="text-orange-600 dark:text-orange-400 w-3 h-3" />
                </div>
                <h4 className="text-base font-medium text-foreground">Find Us Here</h4>
              </div>
              <div className="relative rounded-xl overflow-hidden border border-border/50 shadow-lg group">
                <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-secondary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10 pointer-events-none" />
                <iframe
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3505.************!2d77.51309717424602!3d28.47311707573531!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0xa264998d9fc03a4f%3A0x83c030b94e86c769!2sBRT%20Multi%20Software%20LLP!5e0!3m2!1sen!2sin!4v1718873767139!5m2!1sen!2sin"
                  width="100%"
                  height="200"
                  style={{ border: 0 }}
                  allowFullScreen={true}
                  loading="lazy"
                  title="Google Map"
                  className="transition-all duration-300 group-hover:scale-105"
                ></iframe>
              </div>
            </div>
          </div>

          {/* Right - Contact Form */}
          <div className="relative p-6 rounded-2xl bg-gradient-to-br from-card/80 to-card/40 backdrop-blur-xl border border-border/50 shadow-xl animate-slide-in-right flex flex-col h-full">
            {/* Corner decoration */}
            <div className="absolute top-4 left-4 w-16 h-16 opacity-30">
              <svg viewBox="0 0 100 100" className="w-full h-full text-secondary">
                <circle cx="50" cy="50" r="30" fill="currentColor" />
              </svg>
            </div>

            <div className="relative z-10 flex flex-col h-full">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 rounded-lg bg-gradient-to-r from-secondary/20 to-primary/20 shadow-lg animate-glow">
                  <Send className="w-5 h-5 text-secondary" />
                </div>
                <h3 className="text-xl font-bold text-foreground">Send Us A Message</h3>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4 flex-grow flex flex-col">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div className="group">
                    <div className="relative">
                      <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4 group-focus-within:text-primary transition-colors" />
                      <Input
                        type="text"
                        name="name"
                        value={form.name}
                        onChange={handleChange}
                        placeholder="Your Name"
                        className="pl-10 h-10 border-border/50 focus:border-primary/50 transition-all duration-300 hover:border-primary/30"
                        required
                      />
                    </div>
                  </div>
                  <div className="group">
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4 group-focus-within:text-primary transition-colors" />
                      <Input
                        type="email"
                        name="email"
                        value={form.email}
                        onChange={handleChange}
                        placeholder="Your Email"
                        className="pl-10 h-10 border-border/50 focus:border-primary/50 transition-all duration-300 hover:border-primary/30"
                        required
                      />
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div className="group">
                    <div className="relative">
                      <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4 group-focus-within:text-primary transition-colors" />
                      <Input
                        type="tel"
                        name="mobile"
                        value={form.mobile}
                        onChange={handleChange}
                        placeholder="Your Phone Number"
                        className="pl-10 h-10 border-border/50 focus:border-primary/50 transition-all duration-300 hover:border-primary/30"
                      />
                    </div>
                  </div>
                  <div className="group">
                    <div className="relative">
                      <MessageSquare className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4 group-focus-within:text-primary transition-colors" />
                      <Input
                        type="text"
                        name="subject"
                        value={form.subject}
                        onChange={handleChange}
                        placeholder="Subject"
                        className="pl-10 h-10 border-border/50 focus:border-primary/50 transition-all duration-300 hover:border-primary/30"
                        required
                      />
                    </div>
                  </div>
                </div>

                <div className="group flex-grow">
                  <Textarea
                    name="message"
                    value={form.message}
                    onChange={handleChange}
                    placeholder="Tell us about your project or ask any questions..."
                    rows={5}
                    className="resize-none border-border/50 focus:border-primary/50 transition-all duration-300 hover:border-primary/30 h-full min-h-[100px]"
                    required
                  />
                </div>

                <div className="mt-auto">
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full h-12 text-base font-semibold bg-gradient-to-r from-primary to-secondary hover:from-primary/90 hover:to-secondary/90 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed group"
                  >
                    <div className="flex items-center justify-center gap-2">
                      {isSubmitting ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                          <span>Sending...</span>
                        </>
                      ) : (
                        <>
                          <Send className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
                          <span>Send Message</span>
                        </>
                      )}
                    </div>
                  </Button>
                </div>
              </form>

              {formStatus && (
                <div className="mt-4 p-3 rounded-lg bg-gradient-to-r from-green-500/10 to-emerald-500/10 border border-green-500/20 animate-fade-in">
                  <p className="text-green-600 dark:text-green-400 text-center text-sm font-medium flex items-center justify-center gap-2">
                    <div className="w-4 h-4 rounded-full bg-green-500 flex items-center justify-center">
                      <svg className="w-2.5 h-2.5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                    {formStatus}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default GetInTouch;
