import Image from 'next/image';
import Link from 'next/link';
import { Zap } from 'lucide-react';

// Professional SVG Illustrations
const GameDevelopmentSVG = () => (
  <svg viewBox="0 0 120 120" className="w-full h-full">
    <defs>
      <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
        <feDropShadow dx="2" dy="2" stdDeviation="3" floodColor="#000" floodOpacity="0.3"/>
      </filter>
    </defs>

    {/* Background Circle */}
    <circle cx="60" cy="60" r="55" fill="#0EA5E9" filter="url(#shadow)" />

    {/* Game Controller */}
    <g transform="translate(60,60)">
      {/* Controller Body */}
      <rect x="-25" y="-12" width="50" height="24" rx="12" fill="white" />

      {/* D-Pad */}
      <rect x="-18" y="-6" width="8" height="2" rx="1" fill="#0EA5E9" />
      <rect x="-15" y="-9" width="2" height="8" rx="1" fill="#0EA5E9" />

      {/* Action Buttons */}
      <circle cx="12" cy="-6" r="2.5" fill="#A855F7" />
      <circle cx="18" cy="-6" r="2.5" fill="#10B981" />
      <circle cx="12" cy="0" r="2.5" fill="#A855F7" />
      <circle cx="18" cy="0" r="2.5" fill="#10B981" />

      {/* Controller Grips */}
      <ellipse cx="-20" cy="12" rx="6" ry="10" fill="white" />
      <ellipse cx="20" cy="12" rx="6" ry="10" fill="white" />

      {/* Game Elements */}
      <rect x="-8" y="-25" width="4" height="4" fill="#0EA5E9" className="animate-bounce" style={{ animationDelay: '0s' }} />
      <rect x="-2" y="-28" width="4" height="4" fill="#10B981" className="animate-bounce" style={{ animationDelay: '0.5s' }} />
      <rect x="4" y="-25" width="4" height="4" fill="#A855F7" className="animate-bounce" style={{ animationDelay: '1s' }} />
    </g>
  </svg>
);

const TechnologySVG = () => (
  <svg viewBox="0 0 120 120" className="w-full h-full">
    <defs>
      <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
        <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
        <feMerge>
          <feMergeNode in="coloredBlur"/>
          <feMergeNode in="SourceGraphic"/>
        </feMerge>
      </filter>
    </defs>

    {/* Background Circle */}
    <circle cx="60" cy="60" r="55" fill="#A855F7" filter="url(#shadow)" />

    {/* Circuit Board */}
    <g transform="translate(60,60)">
      <rect x="-30" y="-30" width="60" height="60" rx="8" fill="white" fillOpacity="0.9" />

      {/* Animated Circuit Lines */}
      <g stroke="#A855F7" strokeWidth="2" fill="none">
        <path d="M-25,-20 L25,-20" className="animate-pulse" style={{ animationDelay: '0s' }} />
        <path d="M-25,-10 L25,-10" className="animate-pulse" style={{ animationDelay: '0.3s' }} />
        <path d="M-25,0 L25,0" className="animate-pulse" style={{ animationDelay: '0.6s' }} />
        <path d="M-25,10 L25,10" className="animate-pulse" style={{ animationDelay: '0.9s' }} />
        <path d="M-25,20 L25,20" className="animate-pulse" style={{ animationDelay: '1.2s' }} />

        <path d="M-20,-25 L-20,25" className="animate-pulse" style={{ animationDelay: '0.2s' }} />
        <path d="M-10,-25 L-10,25" className="animate-pulse" style={{ animationDelay: '0.5s' }} />
        <path d="M0,-25 L0,25" className="animate-pulse" style={{ animationDelay: '0.8s' }} />
        <path d="M10,-25 L10,25" className="animate-pulse" style={{ animationDelay: '1.1s' }} />
        <path d="M20,-25 L20,25" className="animate-pulse" style={{ animationDelay: '1.4s' }} />
      </g>

      {/* Microchips */}
      <rect x="-15" y="-15" width="10" height="6" rx="2" fill="#A855F7" />
      <rect x="5" y="-15" width="10" height="6" rx="2" fill="#A855F7" />
      <rect x="-15" y="9" width="10" height="6" rx="2" fill="#A855F7" />
      <rect x="5" y="9" width="10" height="6" rx="2" fill="#A855F7" />

      {/* Central Processor */}
      <rect x="-8" y="-4" width="16" height="8" rx="2" fill="#0EA5E9" filter="url(#glow)" />
      <text x="0" y="1" textAnchor="middle" fontSize="6" fill="white" fontWeight="bold">CPU</text>
    </g>
  </svg>
);

const MonetizationSVG = () => (
  <svg viewBox="0 0 120 120" className="w-full h-full">
    <defs>
      <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
        <polygon points="0 0, 10 3.5, 0 7" fill="white" />
      </marker>
    </defs>

    {/* Background Circle */}
    <circle cx="60" cy="60" r="55" fill="#10B981" filter="url(#shadow)" />

    <g transform="translate(60,60)">
      {/* Coin Stack with Animation */}
      <g className="animate-bounce" style={{ animationDuration: '2s' }}>
        <ellipse cx="-15" cy="15" rx="12" ry="4" fill="#0EA5E9" />
        <ellipse cx="-15" cy="11" rx="12" ry="4" fill="#0EA5E9" />
        <ellipse cx="-15" cy="7" rx="12" ry="4" fill="#0EA5E9" />
        <ellipse cx="-15" cy="3" rx="12" ry="4" fill="#0EA5E9" />
        <text x="-15" y="10" textAnchor="middle" fontSize="8" fill="white" fontWeight="bold">$</text>
      </g>

      {/* Growth Chart */}
      <g stroke="white" strokeWidth="2" fill="none">
        <path d="M5,-20 L5,20 M5,20 L25,20" />
      </g>

      {/* Animated Chart Bars */}
      <rect x="8" y="10" width="3" height="0" fill="white" className="animate-pulse">
        <animate attributeName="height" values="0;10;0" dur="2s" repeatCount="indefinite" />
        <animate attributeName="y" values="20;10;20" dur="2s" repeatCount="indefinite" />
      </rect>
      <rect x="13" y="5" width="3" height="0" fill="white" className="animate-pulse" style={{ animationDelay: '0.5s' }}>
        <animate attributeName="height" values="0;15;0" dur="2s" repeatCount="indefinite" begin="0.5s" />
        <animate attributeName="y" values="20;5;20" dur="2s" repeatCount="indefinite" begin="0.5s" />
      </rect>
      <rect x="18" y="0" width="3" height="0" fill="white" className="animate-pulse" style={{ animationDelay: '1s' }}>
        <animate attributeName="height" values="0;20;0" dur="2s" repeatCount="indefinite" begin="1s" />
        <animate attributeName="y" values="20;0;20" dur="2s" repeatCount="indefinite" begin="1s" />
      </rect>

      {/* Growth Arrow */}
      <path d="M8,-15 L20,-5" stroke="white" strokeWidth="3" strokeLinecap="round" markerEnd="url(#arrowhead)" />
    </g>
  </svg>
);

const SupportSVG = () => (
  <svg viewBox="0 0 120 120" className="w-full h-full">
    {/* Background Circle */}
    <circle cx="60" cy="60" r="55" fill="#F59E0B" filter="url(#shadow)" />

    <g transform="translate(60,60)">
      {/* Shield */}
      <path d="M0,-30 L20,-20 L20,10 Q20,25 0,35 Q-20,25 -20,10 L-20,-20 Z" fill="white" fillOpacity="0.9" />

      {/* Animated Checkmark */}
      <g stroke="#F59E0B" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round" fill="none">
        <path d="M-8,0 L-2,6 L10,-6" className="animate-pulse">
          <animate attributeName="stroke-dasharray" values="0,100;100,0;0,100" dur="3s" repeatCount="indefinite" />
        </path>
      </g>

      {/* Support Hands */}
      <g fill="white" className="animate-pulse" style={{ animationDuration: '2s' }}>
        <path d="M-35,-5 Q-30,-10 -25,-5 Q-20,0 -25,5 Q-30,0 -35,-5" />
        <path d="M35,-5 Q30,-10 25,-5 Q20,0 25,5 Q30,0 35,-5" />
      </g>

      {/* 24/7 Text */}
      <text x="0" y="25" textAnchor="middle" fontSize="8" fill="white" fontWeight="bold">24/7</text>

      {/* Floating Hearts */}
      <g fill="#F59E0B" className="animate-bounce" style={{ animationDelay: '0s' }}>
        <path d="M-25,-25 Q-23,-27 -21,-25 Q-19,-27 -17,-25 Q-19,-23 -21,-21 Q-23,-23 -25,-25" />
      </g>
      <g fill="#F59E0B" className="animate-bounce" style={{ animationDelay: '1s' }}>
        <path d="M20,-25 Q22,-27 24,-25 Q26,-27 28,-25 Q26,-23 24,-21 Q22,-23 20,-25" />
      </g>
    </g>
  </svg>
);

export default function GamingNextLevel() {
  return (
    <section
      className="relative w-full bg-white dark:bg-slate-950 py-20 md:py-28 overflow-hidden"
      aria-label="Gaming Next Level"
    >

      <div className="container mx-auto px-4 relative z-10">
        <div className="flex flex-col lg:flex-row gap-8 lg:gap-12 items-center">
          <div className="lg:w-1/2 w-full flex justify-center lg:justify-start animate-fade-in-up">
            <div className="relative w-full max-w-lg">
              {/* Optimized Image Container */}
              <div className="relative bg-gray-50 dark:bg-slate-900 rounded-2xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-slate-700">
                <Image
                  src="/gaming/about.png"
                  alt="Gaming Next Level Illustration"
                  width={450}
                  height={350}
                  className="rounded-xl object-contain w-full h-auto"
                  priority
                />
              </div>
            </div>
          </div>
          <div className="lg:w-1/2 w-full flex flex-col items-start animate-fade-in-up" style={{ animationDelay: '0.3s' }}>
            <div className="w-full space-y-6">
              {/* Professional Badge */}
              <div className="inline-flex items-center gap-3 px-4 py-2 bg-sky-50 dark:bg-sky-900/30 rounded-lg border border-sky-200 dark:border-sky-700">
                <Zap className="w-5 h-5 text-sky-500 dark:text-sky-400" />
                <span className="text-sm font-semibold text-sky-600 dark:text-sky-300">
                  Next Generation Gaming
                </span>
              </div>

              {/* Enhanced Title */}
              <div className="space-y-4">
                <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white leading-tight">
                  Gaming Next Level
                </h2>

                {/* Simple Accent Line */}
                <div className="w-20 h-1 bg-sky-500 dark:bg-sky-400 rounded"></div>
              </div>

              {/* Professional Subtitle */}
              <h3 className="text-xl md:text-2xl font-medium text-gray-700 dark:text-white">
                Step Into the Future of Game Development
              </h3>

              {/* Enhanced Description */}
              <div className="space-y-4">
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed text-lg">
                  BRT Multi Software empowers creators with its advanced <span className="text-sky-500 dark:text-sky-400 font-semibold">Asset Creator tool</span>. Design and develop custom game assets, craft unique characters, and monetize your creations through our integrated marketplace.
                </p>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  Whether you're enhancing existing games or building new worlds, BRT gives you the tools to shape the future of gaming with cutting-edge technology and innovative solutions.
                </p>
              </div>

              {/* Key Features List */}
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-sky-500 dark:bg-sky-400 rounded-full"></div>
                  <span className="text-gray-700 dark:text-gray-300 font-medium">Advanced Asset Creation Tools</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-purple-500 dark:bg-purple-400 rounded-full"></div>
                  <span className="text-gray-700 dark:text-gray-300 font-medium">Integrated Marketplace Platform</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-emerald-500 dark:bg-emerald-400 rounded-full"></div>
                  <span className="text-gray-700 dark:text-gray-300 font-medium">Cross-Platform Game Development</span>
                </div>
              </div>

              {/* Professional CTA Button */}
              <div className="pt-4">
                <Link href="/contact" className="inline-block">
                  <button className="bg-sky-500 hover:bg-sky-600 dark:bg-sky-400 dark:hover:bg-sky-500 text-white font-semibold py-4 px-8 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 text-lg hover:-translate-y-1">
                    Start Building with BRT
                  </button>
                </Link>
              </div>
            </div>
          </div>
        </div>
        {/* Enhanced Feature Cards Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 xs:gap-8 mt-20">
          {[
            {
              icon: GameDevelopmentSVG,
              title: 'Custom Games',
              description: 'Innovative games tailored for all platforms',
              color: 'blue',
              delay: '0.1s'
            },
            {
              icon: TechnologySVG,
              title: 'Cutting-Edge Tech',
              description: 'Advanced tools for seamless development',
              color: 'purple',
              delay: '0.2s'
            },
            {
              icon: MonetizationSVG,
              title: 'Smart Monetization',
              description: 'Boost revenue with in-game strategies',
              color: 'green',
              delay: '0.3s'
            },
            {
              icon: SupportSVG,
              title: 'Full Support',
              description: 'End-to-end game development services',
              color: 'orange',
              delay: '0.4s'
            }
          ].map((feature, index) => {
            const IconComponent = feature.icon;
            return (
              <div
                key={index}
                className="group bg-white dark:bg-blue-900 rounded-2xl p-8 shadow-lg hover:shadow-2xl border border-gray-200 dark:border-blue-700 transition-all duration-700 hover:-translate-y-6 hover:scale-110 text-center animate-fade-in-up overflow-hidden relative cursor-pointer"
                style={{ animationDelay: feature.delay }}
              >
                {/* Subtle Background Animation */}
                <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-700">
                  <div className={`absolute inset-0 bg-${feature.color}-50 dark:bg-${feature.color}-900/30 rounded-2xl`}></div>
                </div>

                {/* Enhanced SVG Icon Container */}
                <div className="relative mb-8 w-24 h-24 mx-auto group-hover:scale-125 group-hover:rotate-6 transition-all duration-700 transform-gpu">
                  {/* Icon Background */}
                  <div className="absolute inset-0 bg-white dark:bg-gray-700 rounded-3xl shadow-lg group-hover:shadow-2xl transition-all duration-700"></div>
                  <div className={`absolute inset-0 bg-${feature.color}-500/10 dark:bg-${feature.color}-400/20 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-700`}></div>

                  {/* SVG Container */}
                  <div className="relative p-2 w-full h-full transform group-hover:scale-110 transition-transform duration-500">
                    <IconComponent />
                  </div>

                  {/* Floating Particles with Staggered Animation */}
                  <div className={`absolute -top-2 -right-2 w-4 h-4 bg-${feature.color}-400 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-500 animate-bounce shadow-lg`}></div>
                  <div className={`absolute -bottom-2 -left-2 w-3 h-3 bg-${feature.color}-500 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-500 animate-bounce shadow-lg`} style={{ animationDelay: '0.3s' }}></div>
                  <div className={`absolute top-1/2 -right-3 w-2 h-2 bg-${feature.color}-600 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-500 animate-bounce shadow-lg`} style={{ animationDelay: '0.6s' }}></div>

                  {/* Orbital Ring */}
                  <div className={`absolute inset-0 border-2 border-${feature.color}-300 dark:border-${feature.color}-600 rounded-full opacity-0 group-hover:opacity-50 transition-all duration-700 animate-spin`} style={{ animationDuration: '3s' }}></div>
                </div>

                {/* Enhanced Content with Animations */}
                <div className="relative z-10 space-y-4">
                  <h4 className={`text-xl font-bold text-gray-900 dark:text-white mb-4 uppercase group-hover:text-${feature.color}-600 dark:group-hover:text-${feature.color}-400 transition-all duration-500 transform group-hover:scale-105`}>
                    {feature.title}
                  </h4>

                  <p className="text-gray-600 dark:text-gray-300 leading-relaxed group-hover:text-gray-800 dark:group-hover:text-gray-100 transition-all duration-500 transform group-hover:scale-105">
                    {feature.description}
                  </p>

                  {/* Animated Progress Bar */}
                  {/* <div className="mt-6 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden">
                    <div className={`h-full bg-${feature.color}-500 transition-all duration-1000 group-hover:w-full w-0 rounded-full`}></div>
                  </div> */}

                  {/* Feature Badge */}
                  <div className={`inline-flex items-center gap-2 px-3 py-1 bg-${feature.color}-100 dark:bg-${feature.color}-900/50 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-y-4 group-hover:translate-y-0`}>
                    <div className={`w-2 h-2 bg-${feature.color}-500 rounded-full`}></div>
                    <span className={`text-xs font-semibold text-${feature.color}-700 dark:text-${feature.color}-300`}>Premium</span>
                  </div>
                </div>

                {/* Enhanced Corner Accents */}
                <div className={`absolute top-0 right-0 w-12 h-12 bg-${feature.color}-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-tr-2xl`}></div>
                <div className={`absolute bottom-0 left-0 w-8 h-8 bg-${feature.color}-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-bl-2xl`}></div>

                {/* Enhanced Glow Effect */}
                <div className={`absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-700 shadow-2xl shadow-${feature.color}-500/30 blur-xl`}></div>

                {/* Ripple Effect */}
                <div className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className={`absolute inset-0 bg-${feature.color}-500/10 rounded-2xl animate-ping`} style={{ animationDuration: '2s' }}></div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
} 