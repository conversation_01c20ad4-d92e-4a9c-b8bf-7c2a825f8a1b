"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Star } from "lucide-react";

interface Testimonial {
  name: string;
  position: string;
  company: string;
  content: string;
  rating: number;
  image: string;
}

const testimonials: Testimonial[] = [
  {
    name: "<PERSON>",
    position: "Marketing Director",
    company: "TechStart Inc.",
    content: "Their digital marketing strategies transformed our online presence. We saw a 300% increase in qualified leads within just 3 months.",
    rating: 5,
    image: "/api/placeholder/60/60"
  },
  {
    name: "<PERSON>",
    position: "CEO",
    company: "E-commerce Plus",
    content: "The ROI from their PPC campaigns exceeded our expectations. Professional team with exceptional results.",
    rating: 5,
    image: "/api/placeholder/60/60"
  },
  {
    name: "<PERSON>",
    position: "Founder",
    company: "Local Services Co.",
    content: "Outstanding SEO work that got us ranking #1 for our key terms. Highly recommend their services.",
    rating: 5,
    image: "/api/placeholder/60/60"
  },
  {
    name: "<PERSON>",
    position: "Marketing Manager",
    company: "Growth Startup",
    content: "Their social media campaigns increased our engagement by 400% and brought in countless new customers.",
    rating: 5,
    image: "/api/placeholder/60/60"
  },
  {
    name: "<PERSON>",
    position: "Business Owner",
    company: "Retail Solutions",
    content: "Professional, results-driven team that delivered exactly what they promised. Our sales have doubled!",
    rating: 5,
    image: "/api/placeholder/60/60"
  },
  {
    name: "James Miller",
    position: "VP Marketing",
    company: "Tech Innovations",
    content: "Best digital marketing agency we've worked with. Their data-driven approach delivers consistent results.",
    rating: 5,
    image: "/api/placeholder/60/60"
  }
];

export function TestimonialsSection() {
  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8">
      <div className="container mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 hero-title animate-fade-in-up">
            What Our Clients Say
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto animate-fade-in-up">
            Don't just take our word for it. Here's what our satisfied clients have to say about our digital marketing services.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card 
              key={index} 
              className="service-card hover:scale-105 transition-all duration-300 group animate-fade-in-up"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <CardContent className="p-8">
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                
                <p className="text-muted-foreground mb-6 leading-relaxed italic">
                  "{testimonial.content}"
                </p>
                
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center mr-4">
                    <span className="text-white font-bold text-lg">
                      {testimonial.name.charAt(0)}
                    </span>
                  </div>
                  <div>
                    <div className="font-semibold">{testimonial.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {testimonial.position} at {testimonial.company}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
