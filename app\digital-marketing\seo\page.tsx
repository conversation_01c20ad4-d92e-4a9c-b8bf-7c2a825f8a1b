"use client";

import { MarketingNavigation } from "@/components/marketing-navigation";
import { ServiceHero, ServiceFeatures, ServicePricing, ServiceCTA } from "@/components/digital-marketing/shared";
import {
  Search, TrendingUp, FileText, Link, BarChart3, Globe,
  Target, Zap, Award, Users, CheckCircle, ArrowUp
} from "lucide-react";

export default function SEOServicesPage() {
  const heroData = {
    title: "SEO Services That",
    subtitle: "Drive Real Results",
    description: "Boost your organic visibility and drive qualified traffic with our comprehensive SEO strategies. We help businesses rank higher, attract more customers, and grow sustainably.",
    features: [
      "Comprehensive keyword research and strategy",
      "Technical SEO optimization and site audits",
      "High-quality content creation and optimization",
      "Local SEO for location-based businesses",
      "Monthly reporting and performance tracking"
    ],
    primaryCTA: "Get Free SEO Audit",
    secondaryCTA: "View Case Studies",
    badge: "🔍 SEO Excellence",
    stats: [
      { value: "300%", label: "Average Traffic Increase" },
      { value: "#1", label: "Rankings Achieved" },
      { value: "90%", label: "Client Retention Rate" },
      { value: "6 Months", label: "Average Results Timeline" }
    ],
    backgroundGradient: "from-green-900/10 via-blue-900/5 to-green-900/10"
  };

  const features = [
    {
      icon: Search,
      title: "Keyword Research & Strategy",
      description: "In-depth keyword analysis to identify high-value opportunities that drive qualified traffic to your website.",
      benefits: [
        "Competitor keyword analysis",
        "Long-tail keyword identification",
        "Search intent mapping",
        "Keyword difficulty assessment"
      ]
    },
    {
      icon: FileText,
      title: "Content Optimization",
      description: "Create and optimize content that ranks well and converts visitors into customers.",
      benefits: [
        "SEO-optimized blog posts",
        "Landing page optimization",
        "Meta tags and descriptions",
        "Content gap analysis"
      ],
      popular: true
    },
    {
      icon: Zap,
      title: "Technical SEO",
      description: "Optimize your website's technical foundation for better search engine crawling and indexing.",
      benefits: [
        "Site speed optimization",
        "Mobile responsiveness",
        "Schema markup implementation",
        "XML sitemap optimization"
      ]
    },
    {
      icon: Link,
      title: "Link Building",
      description: "Build high-quality backlinks that improve your domain authority and search rankings.",
      benefits: [
        "Guest posting opportunities",
        "Resource page link building",
        "Broken link building",
        "Digital PR campaigns"
      ]
    },
    {
      icon: Globe,
      title: "Local SEO",
      description: "Dominate local search results and attract customers in your geographic area.",
      benefits: [
        "Google My Business optimization",
        "Local citation building",
        "Review management",
        "Local keyword targeting"
      ]
    },
    {
      icon: BarChart3,
      title: "SEO Analytics & Reporting",
      description: "Track your SEO performance with detailed analytics and actionable insights.",
      benefits: [
        "Monthly performance reports",
        "Ranking tracking",
        "Traffic analysis",
        "ROI measurement"
      ]
    }
  ];

  const pricingTiers = [
    {
      name: "Starter SEO",
      price: "$1,500",
      period: "month",
      description: "Perfect for small businesses starting their SEO journey",
      features: [
        "Keyword research (up to 50 keywords)",
        "On-page optimization (5 pages)",
        "Technical SEO audit",
        "Monthly reporting",
        "Google My Business setup"
      ],
      cta: "Start SEO Journey",
      badge: "Best for Startups"
    },
    {
      name: "Professional SEO",
      price: "$3,500",
      period: "month",
      description: "Comprehensive SEO for growing businesses",
      features: [
        "Keyword research (up to 150 keywords)",
        "On-page optimization (15 pages)",
        "Content creation (4 blog posts)",
        "Link building (10 quality links)",
        "Technical SEO optimization",
        "Local SEO (if applicable)",
        "Bi-weekly reporting"
      ],
      popular: true,
      cta: "Get Professional SEO",
      badge: "Most Popular"
    },
    {
      name: "Enterprise SEO",
      price: "$7,500",
      period: "month",
      description: "Advanced SEO for large businesses and e-commerce",
      features: [
        "Unlimited keyword research",
        "On-page optimization (unlimited pages)",
        "Content creation (8 blog posts)",
        "Advanced link building (25+ links)",
        "Technical SEO optimization",
        "E-commerce SEO",
        "Dedicated account manager",
        "Weekly reporting",
        "Priority support"
      ],
      cta: "Scale with Enterprise SEO",
      badge: "Maximum Results"
    }
  ];

  const ctaData = {
    title: "Ready to Dominate Search Results?",
    subtitle: "Join hundreds of businesses that have transformed their online presence with our proven SEO strategies.",
    primaryCTA: "Get Free SEO Audit",
    secondaryCTA: "Schedule Consultation",
    features: [
      "Free comprehensive SEO audit",
      "No long-term contracts required",
      "Proven results within 6 months"
    ],
    backgroundGradient: "from-green-600 via-blue-700 to-green-700"
  };

  return (
    <main className="min-h-screen">
      <MarketingNavigation />
      <ServiceHero {...heroData} />
      <ServiceFeatures
        title="Complete SEO Solutions"
        subtitle="Everything you need to dominate search results and drive organic growth"
        features={features}
      />
      <ServicePricing
        title="SEO Packages That Deliver Results"
        subtitle="Choose the perfect SEO package for your business needs and budget"
        tiers={pricingTiers}
      />
      <ServiceCTA {...ctaData} />
    </main>
  );
}
