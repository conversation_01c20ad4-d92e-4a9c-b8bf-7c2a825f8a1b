<svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
<style>


@-webkit-keyframes animate-svg-stroke-1 {
  0% {
    stroke-dashoffset: 65.33037567138672px;
    stroke-dasharray: 65.33037567138672px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 65.33037567138672px;
  }
}

@keyframes animate-svg-stroke-1 {
  0% {
    stroke-dashoffset: 65.33037567138672px;
    stroke-dasharray: 65.33037567138672px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 65.33037567138672px;
  }
}

.svg-elem-1 {
  -webkit-animation: animate-svg-stroke-1 1s cubic-bezier(0.47, 0, 0.745, 0.715) 0s alternate-reverse infinite,
                       animate-svg-fill-1 0.7s cubic-bezier(0.47, 0, 0.745, 0.715) 0.8s alternate-reverse infinite;
          animation: animate-svg-stroke-1 1s cubic-bezier(0.47, 0, 0.745, 0.715) 0s alternate-reverse infinite,
               animate-svg-fill-1 0.7s cubic-bezier(0.47, 0, 0.745, 0.715) 0.8s alternate-reverse infinite;
}

@-webkit-keyframes animate-svg-stroke-2 {
  0% {
    stroke-dashoffset: 20.666479110717773px;
    stroke-dasharray: 20.666479110717773px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 20.666479110717773px;
  }
}

@keyframes animate-svg-stroke-2 {
  0% {
    stroke-dashoffset: 20.666479110717773px;
    stroke-dasharray: 20.666479110717773px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 20.666479110717773px;
  }
}

.svg-elem-2 {
  -webkit-animation: animate-svg-stroke-2 1s cubic-bezier(0.47, 0, 0.745, 0.715) 0.12s alternate-reverse infinite,
                       animate-svg-fill-2 0.7s cubic-bezier(0.47, 0, 0.745, 0.715) 0.9s alternate-reverse infinite;
          animation: animate-svg-stroke-2 1s cubic-bezier(0.47, 0, 0.745, 0.715) 0.12s alternate-reverse infinite,
               animate-svg-fill-2 0.7s cubic-bezier(0.47, 0, 0.745, 0.715) 0.9s alternate-reverse infinite;
}

@-webkit-keyframes animate-svg-stroke-3 {
  0% {
    stroke-dashoffset: 4.333330154418945px;
    stroke-dasharray: 4.333330154418945px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 4.333330154418945px;
  }
}

@keyframes animate-svg-stroke-3 {
  0% {
    stroke-dashoffset: 4.333330154418945px;
    stroke-dasharray: 4.333330154418945px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 4.333330154418945px;
  }
}

.svg-elem-3 {
  -webkit-animation: animate-svg-stroke-3 1s cubic-bezier(0.47, 0, 0.745, 0.715) 0.24s alternate-reverse infinite,
                       animate-svg-fill-3 0.7s cubic-bezier(0.47, 0, 0.745, 0.715) 1s alternate-reverse infinite;
          animation: animate-svg-stroke-3 1s cubic-bezier(0.47, 0, 0.745, 0.715) 0.24s alternate-reverse infinite,
               animate-svg-fill-3 0.7s cubic-bezier(0.47, 0, 0.745, 0.715) 1s alternate-reverse infinite;
}

@-webkit-keyframes animate-svg-stroke-4 {
  0% {
    stroke-dashoffset: 4.333330154418945px;
    stroke-dasharray: 4.333330154418945px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 4.333330154418945px;
  }
}

@keyframes animate-svg-stroke-4 {
  0% {
    stroke-dashoffset: 4.333330154418945px;
    stroke-dasharray: 4.333330154418945px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 4.333330154418945px;
  }
}

.svg-elem-4 {
  -webkit-animation: animate-svg-stroke-4 1s cubic-bezier(0.47, 0, 0.745, 0.715) 0.36s alternate-reverse infinite,
                       animate-svg-fill-4 0.7s cubic-bezier(0.47, 0, 0.745, 0.715) 1.1s alternate-reverse infinite;
          animation: animate-svg-stroke-4 1s cubic-bezier(0.47, 0, 0.745, 0.715) 0.36s alternate-reverse infinite,
               animate-svg-fill-4 0.7s cubic-bezier(0.47, 0, 0.745, 0.715) 1.1s alternate-reverse infinite;
}

@-webkit-keyframes animate-svg-stroke-5 {
  0% {
    stroke-dashoffset: 4.333330154418945px;
    stroke-dasharray: 4.333330154418945px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 4.333330154418945px;
  }
}

@keyframes animate-svg-stroke-5 {
  0% {
    stroke-dashoffset: 4.333330154418945px;
    stroke-dasharray: 4.333330154418945px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 4.333330154418945px;
  }
}

.svg-elem-5 {
  -webkit-animation: animate-svg-stroke-5 1s cubic-bezier(0.47, 0, 0.745, 0.715) 0.48s alternate-reverse infinite,
                       animate-svg-fill-5 0.7s cubic-bezier(0.47, 0, 0.745, 0.715) 1.2000000000000002s alternate-reverse infinite;
          animation: animate-svg-stroke-5 1s cubic-bezier(0.47, 0, 0.745, 0.715) 0.48s alternate-reverse infinite,
               animate-svg-fill-5 0.7s cubic-bezier(0.47, 0, 0.745, 0.715) 1.2000000000000002s alternate-reverse infinite;
}

@-webkit-keyframes animate-svg-stroke-6 {
  0% {
    stroke-dashoffset: 4.333330154418945px;
    stroke-dasharray: 4.333330154418945px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 4.333330154418945px;
  }
}

@keyframes animate-svg-stroke-6 {
  0% {
    stroke-dashoffset: 4.333330154418945px;
    stroke-dasharray: 4.333330154418945px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 4.333330154418945px;
  }
}

.svg-elem-6 {
  -webkit-animation: animate-svg-stroke-6 1s cubic-bezier(0.47, 0, 0.745, 0.715) 0.6s alternate-reverse infinite,
                       animate-svg-fill-6 0.7s cubic-bezier(0.47, 0, 0.745, 0.715) 1.3s alternate-reverse infinite;
          animation: animate-svg-stroke-6 1s cubic-bezier(0.47, 0, 0.745, 0.715) 0.6s alternate-reverse infinite,
               animate-svg-fill-6 0.7s cubic-bezier(0.47, 0, 0.745, 0.715) 1.3s alternate-reverse infinite;
}

@-webkit-keyframes animate-svg-stroke-7 {
  0% {
    stroke-dashoffset: 4.33329963684082px;
    stroke-dasharray: 4.33329963684082px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 4.33329963684082px;
  }
}

@keyframes animate-svg-stroke-7 {
  0% {
    stroke-dashoffset: 4.33329963684082px;
    stroke-dasharray: 4.33329963684082px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 4.33329963684082px;
  }
}

.svg-elem-7 {
  -webkit-animation: animate-svg-stroke-7 1s cubic-bezier(0.47, 0, 0.745, 0.715) 0.72s alternate-reverse infinite,
                       animate-svg-fill-7 0.7s cubic-bezier(0.47, 0, 0.745, 0.715) 1.4000000000000001s alternate-reverse infinite;
          animation: animate-svg-stroke-7 1s cubic-bezier(0.47, 0, 0.745, 0.715) 0.72s alternate-reverse infinite,
               animate-svg-fill-7 0.7s cubic-bezier(0.47, 0, 0.745, 0.715) 1.4000000000000001s alternate-reverse infinite;
}

@-webkit-keyframes animate-svg-stroke-8 {
  0% {
    stroke-dashoffset: 4.33329963684082px;
    stroke-dasharray: 4.33329963684082px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 4.33329963684082px;
  }
}

@keyframes animate-svg-stroke-8 {
  0% {
    stroke-dashoffset: 4.33329963684082px;
    stroke-dasharray: 4.33329963684082px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 4.33329963684082px;
  }
}

.svg-elem-8 {
  -webkit-animation: animate-svg-stroke-8 1s cubic-bezier(0.47, 0, 0.745, 0.715) 0.84s alternate-reverse infinite,
                       animate-svg-fill-8 0.7s cubic-bezier(0.47, 0, 0.745, 0.715) 1.5s alternate-reverse infinite;
          animation: animate-svg-stroke-8 1s cubic-bezier(0.47, 0, 0.745, 0.715) 0.84s alternate-reverse infinite,
               animate-svg-fill-8 0.7s cubic-bezier(0.47, 0, 0.745, 0.715) 1.5s alternate-reverse infinite;
}

@-webkit-keyframes animate-svg-stroke-9 {
  0% {
    stroke-dashoffset: 4.333398818969727px;
    stroke-dasharray: 4.333398818969727px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 4.333398818969727px;
  }
}

@keyframes animate-svg-stroke-9 {
  0% {
    stroke-dashoffset: 4.333398818969727px;
    stroke-dasharray: 4.333398818969727px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 4.333398818969727px;
  }
}

.svg-elem-9 {
  -webkit-animation: animate-svg-stroke-9 1s cubic-bezier(0.47, 0, 0.745, 0.715) 0.96s alternate-reverse infinite,
                       animate-svg-fill-9 0.7s cubic-bezier(0.47, 0, 0.745, 0.715) 1.6s alternate-reverse infinite;
          animation: animate-svg-stroke-9 1s cubic-bezier(0.47, 0, 0.745, 0.715) 0.96s alternate-reverse infinite,
               animate-svg-fill-9 0.7s cubic-bezier(0.47, 0, 0.745, 0.715) 1.6s alternate-reverse infinite;
}

@-webkit-keyframes animate-svg-stroke-10 {
  0% {
    stroke-dashoffset: 4.333398818969727px;
    stroke-dasharray: 4.333398818969727px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 4.333398818969727px;
  }
}

@keyframes animate-svg-stroke-10 {
  0% {
    stroke-dashoffset: 4.333398818969727px;
    stroke-dasharray: 4.333398818969727px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 4.333398818969727px;
  }
}

.svg-elem-10 {
  -webkit-animation: animate-svg-stroke-10 1s cubic-bezier(0.47, 0, 0.745, 0.715) 1.08s alternate-reverse infinite,
                       animate-svg-fill-10 0.7s cubic-bezier(0.47, 0, 0.745, 0.715) 1.7000000000000002s alternate-reverse infinite;
          animation: animate-svg-stroke-10 1s cubic-bezier(0.47, 0, 0.745, 0.715) 1.08s alternate-reverse infinite,
               animate-svg-fill-10 0.7s cubic-bezier(0.47, 0, 0.745, 0.715) 1.7000000000000002s alternate-reverse infinite;
}

@-webkit-keyframes animate-svg-stroke-11 {
  0% {
    stroke-dashoffset: 114px;
    stroke-dasharray: 114px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 114px;
  }
}

@keyframes animate-svg-stroke-11 {
  0% {
    stroke-dashoffset: 114px;
    stroke-dasharray: 114px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 114px;
  }
}

@-webkit-keyframes animate-svg-fill-11 {
  0% {
    fill: transparent;
  }

  100% {
    fill: rgb(255, 255, 255);
  }
}

@keyframes animate-svg-fill-11 {
  0% {
    fill: transparent;
  }

  100% {
    fill: rgb(255, 255, 255);
  }
}

.svg-elem-11 {
  -webkit-animation: animate-svg-stroke-11 1s cubic-bezier(0.47, 0, 0.745, 0.715) 1.2s alternate-reverse infinite,
                       animate-svg-fill-11 0.7s cubic-bezier(0.47, 0, 0.745, 0.715) 1.8s alternate-reverse infinite;
          animation: animate-svg-stroke-11 1s cubic-bezier(0.47, 0, 0.745, 0.715) 1.2s alternate-reverse infinite,
               animate-svg-fill-11 0.7s cubic-bezier(0.47, 0, 0.745, 0.715) 1.8s alternate-reverse infinite;
}

</style>
<g clip-path="url(#clip0_248_1618)">
<path d="M5.83301 6.99992C5.83301 6.6905 5.95592 6.39375 6.17472 6.17496C6.39351 5.95617 6.69026 5.83325 6.99967 5.83325H20.9997C21.3091 5.83325 21.6058 5.95617 21.8246 6.17496C22.0434 6.39375 22.1663 6.6905 22.1663 6.99992V20.9999C22.1663 21.3093 22.0434 21.6061 21.8246 21.8249C21.6058 22.0437 21.3091 22.1666 20.9997 22.1666H6.99967C6.69026 22.1666 6.39351 22.0437 6.17472 21.8249C5.95592 21.6061 5.83301 21.3093 5.83301 20.9999V6.99992Z" stroke="#021639" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="svg-elem-1"></path>
<path d="M9.33301 11.6666V9.33325H11.6663M18.6663 16.3333V18.6666H16.333M11.6663 18.6666H9.33301V16.3333M18.6663 11.6666V9.33325H16.333" stroke="#021639" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="svg-elem-2"></path>
<path d="M3.5 11.6667H5.83333" stroke="#021639" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="svg-elem-3"></path>
<path d="M3.5 16.3333H5.83333" stroke="#021639" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="svg-elem-4"></path>
<path d="M11.667 3.5V5.83333" stroke="#021639" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="svg-elem-5"></path>
<path d="M16.333 3.5V5.83333" stroke="#021639" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="svg-elem-6"></path>
<path d="M24.5003 11.6667H22.167" stroke="#021639" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="svg-elem-7"></path>
<path d="M24.5003 16.3333H22.167" stroke="#021639" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="svg-elem-8"></path>
<path d="M16.333 24.5001V22.1667" stroke="#021639" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="svg-elem-9"></path>
<path d="M11.667 24.5001V22.1667" stroke="#021639" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="svg-elem-10"></path>
</g>
<defs>
<clipPath id="clip0_248_1618">
<rect width="28" height="28" fill="white" class="svg-elem-11"></rect>
</clipPath>
</defs>
</svg>
