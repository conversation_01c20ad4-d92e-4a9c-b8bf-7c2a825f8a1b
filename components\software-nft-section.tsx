"use client";
import React, { memo, useRef, useMemo } from "react";
import Image from "next/image";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ap, <PERSON>, Rocket } from "lucide-react";
import { motion, useInView, Variants } from "framer-motion";

interface SoftwareNFTSectionProps {
  title: string;
  description: string;
  features: string[];
  image?: string;
  lightColor?: string;
  darkColor?: string;
}

export const SoftwareNFTSection: React.FC<SoftwareNFTSectionProps> = memo(({
  title,
  description,
  features,
  image = "/software/NFT.png",
  lightColor = "#f5d0fe",
  darkColor = "#a21caf",
}) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.2 });

  const accentStyle = useMemo(() => ({
    '--accent-light': lightColor,
    '--accent-dark': darkColor,
  } as React.CSSProperties), [lightColor, darkColor]);

  const cardVariants: Variants = useMemo(() => ({
    hidden: { opacity: 0, y: 30, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.8,
        ease: [0.25, 0.46, 0.45, 0.94],
        staggerChildren: 0.1,
      },
    },
  }), []);

  const imageVariants: Variants = useMemo(() => ({
    hidden: {
      opacity: 0,
      y: -120,
      x: 30,
      rotate: -15,
      scale: 0.7,
      rotateX: 45,
      rotateY: -20
    },
    visible: {
      opacity: 1,
      y: 0,
      x: 0,
      rotate: 0,
      scale: 1,
      rotateX: 0,
      rotateY: 0,
      transition: {
        type: "spring",
        stiffness: 40,
        damping: 12,
        mass: 1.5,
        delay: 0.3,
        duration: 1.2,
      },
    },
  }), []);

  const imageFloatVariants: Variants = useMemo(() => ({
    float: {
      y: [0, -15, 0],
      rotate: [0, 2, 0],
      scale: [1, 1.02, 1],
      transition: {
        duration: 4,
        repeat: Infinity,
        ease: "easeInOut",
      },
    },
  }), []);

  const contentItemVariants: Variants = useMemo(() => ({
    hidden: { opacity: 0, x: -20, y: 10 },
    visible: {
      opacity: 1,
      x: 0,
      y: 0,
      transition: {
        duration: 0.6,
        ease: [0.25, 0.46, 0.45, 0.94],
      },
    },
  }), []);

  return (
    <section
      ref={ref}
      className="py-4 sm:py-6 md:py-8 lg:py-12 px-3 sm:px-4 md:px-6 lg:px-8 bg-background relative overflow-hidden transition-all duration-700"
      style={accentStyle}
    >
      {/* Enhanced animated background with depth */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Dynamic gradient mesh */}
        <motion.div
          className="absolute inset-0 opacity-30"
          animate={{
            background: [
              "radial-gradient(circle at 20% 20%, var(--accent-light)/10 0%, transparent 50%), radial-gradient(circle at 80% 80%, var(--accent-dark)/10 0%, transparent 50%)",
              "radial-gradient(circle at 80% 20%, var(--accent-dark)/10 0%, transparent 50%), radial-gradient(circle at 20% 80%, var(--accent-light)/10 0%, transparent 50%)",
              "radial-gradient(circle at 20% 20%, var(--accent-light)/10 0%, transparent 50%), radial-gradient(circle at 80% 80%, var(--accent-dark)/10 0%, transparent 50%)"
            ]
          }}
          transition={{ duration: 12, repeat: Infinity, ease: "easeInOut" }}
        />

        {/* Floating orbs with trails */}
        <motion.div
          className="absolute top-1/4 left-1/4 w-32 h-32 bg-gradient-to-r from-[var(--accent-light)]/15 to-[var(--accent-dark)]/15 rounded-full blur-2xl"
          animate={{
            y: [0, -30, 0],
            x: [0, 20, 0],
            scale: [1, 1.3, 1],
            opacity: [0.15, 0.4, 0.15]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute bottom-1/4 right-1/4 w-48 h-48 bg-gradient-to-r from-[var(--accent-dark)]/12 to-[var(--accent-light)]/12 rounded-full blur-3xl"
          animate={{
            y: [0, 25, 0],
            x: [0, -25, 0],
            scale: [1, 0.8, 1],
            opacity: [0.12, 0.3, 0.12]
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
        />

        {/* Animated geometric constellation */}
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-[var(--accent-light)] dark:bg-[var(--accent-dark)] rounded-full opacity-60"
            style={{
              top: `${15 + (i * 10)}%`,
              left: `${10 + (i % 3) * 30}%`,
            }}
            animate={{
              y: [0, -15, 0],
              x: [0, (i % 2 ? 8 : -8), 0],
              opacity: [0.6, 1, 0.6],
              scale: [1, 1.5, 1],
            }}
            transition={{
              duration: 4 + (i * 0.3),
              repeat: Infinity,
              ease: "easeInOut",
              delay: i * 0.5,
            }}
          />
        ))}

        {/* Connecting lines between dots */}
        <svg className="absolute inset-0 w-full h-full opacity-20">
          <motion.path
            d="M 100 100 Q 200 50 300 100 T 500 100"
            stroke="var(--accent-light)"
            strokeWidth="1"
            fill="none"
            initial={{ pathLength: 0, opacity: 0 }}
            animate={{ pathLength: 1, opacity: 0.3 }}
            transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
          />
          <motion.path
            d="M 50 200 Q 150 150 250 200 T 450 200"
            stroke="var(--accent-dark)"
            strokeWidth="1"
            fill="none"
            initial={{ pathLength: 0, opacity: 0 }}
            animate={{ pathLength: 1, opacity: 0.3 }}
            transition={{ duration: 4, repeat: Infinity, ease: "easeInOut", delay: 1 }}
          />
        </svg>
      </div>

      <div className="container mx-auto flex justify-center relative z-10">
        <motion.div
          variants={cardVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="w-full max-w-xs sm:max-w-lg md:max-w-4xl lg:max-w-5xl xl:max-w-6xl bg-transparent backdrop-blur-[2px] rounded-2xl sm:rounded-3xl border border-white/10 dark:border-white/5 hover:border-[var(--accent-light)]/20 dark:hover:border-[var(--accent-dark)]/20 p-4 sm:p-6 md:p-8 lg:p-12 xl:p-16 flex flex-col lg:flex-row gap-6 sm:gap-8 md:gap-10 lg:gap-12 items-center relative overflow-hidden group transition-all duration-700 hover:scale-[1.005] hover:backdrop-blur-[4px]"
          whileHover={{
            y: -3,
            transition: { duration: 0.4, ease: "easeOut" }
          }}
          style={{
            background: "linear-gradient(135deg, rgba(255,255,255,0.02) 0%, rgba(255,255,255,0.01) 100%)",
            boxShadow: "0 8px 32px rgba(0,0,0,0.1), inset 0 1px 0 rgba(255,255,255,0.1)"
          }}
        >
          {/* Professional SVG corner accents */}
          <svg className="absolute -top-3 -left-3 w-12 h-12 opacity-70 group-hover:opacity-90 transition-opacity duration-500" viewBox="0 0 48 48">
            <path d="M48,0 A48,48 0 0,0 0,48 L0,0 Z" fill={lightColor} />
          </svg>
          <svg className="absolute -top-3 -right-3 w-12 h-12 opacity-70 group-hover:opacity-90 transition-opacity duration-500" viewBox="0 0 48 48">
            <path d="M0,0 A48,48 0 0,1 48,48 L48,0 Z" fill={darkColor} />
          </svg>
          <svg className="absolute -bottom-3 -left-3 w-12 h-12 opacity-70 group-hover:opacity-90 transition-opacity duration-500" viewBox="0 0 48 48">
            <path d="M48,48 A48,48 0 0,1 0,0 L0,48 Z" fill={darkColor} />
          </svg>
          <svg className="absolute -bottom-3 -right-3 w-12 h-12 opacity-70 group-hover:opacity-90 transition-opacity duration-500" viewBox="0 0 48 48">
            <path d="M0,48 A48,48 0 0,0 48,0 L48,48 Z" fill={lightColor} />
          </svg>

          {/* Center Top Circle with dynamic colors */}
          <motion.svg
            className="absolute -top-10 -right-10 w-32 h-32 z-30 pointer-events-none"
            viewBox="0 0 128 128"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            animate={{ rotate: [0, 360] }}
            transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
          >
            <motion.circle
              cx="64"
              cy="64"
              r="56"
              fill={lightColor}
              initial={{ opacity: 0.6 }}
              animate={{
                opacity: [0.6, 0.8, 0.6],
                r: [56, 60, 56]
              }}
              transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
            />
            <motion.circle
              cx="64"
              cy="64"
              r="40"
              fill="none"
              stroke={darkColor}
              strokeWidth="2"
              initial={{ opacity: 0.4 }}
              animate={{
                opacity: [0.4, 0.7, 0.4],
                r: [40, 44, 40]
              }}
              transition={{ duration: 3, repeat: Infinity, ease: "easeInOut", delay: 1 }}
            />
            <motion.circle
              cx="64"
              cy="64"
              r="24"
              fill={darkColor}
              initial={{ opacity: 0.5 }}
              animate={{
                opacity: [0.5, 0.8, 0.5],
                r: [24, 28, 24]
              }}
              transition={{ duration: 2.5, repeat: Infinity, ease: "easeInOut", delay: 0.5 }}
            />
          </motion.svg>

          {/* Professional glow effect on hover */}
          <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-all duration-1000 pointer-events-none">
            <div className="absolute inset-0 bg-gradient-to-br from-[var(--accent-light)]/3 via-transparent to-[var(--accent-dark)]/3 rounded-3xl"></div>
            <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-[var(--accent-light)]/2 to-transparent rounded-3xl"></div>
          </div>

          {/* Subtle inner glow */}
          <div className="absolute inset-[1px] rounded-3xl bg-gradient-to-br from-white/5 to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-500 pointer-events-none"></div>

          {/* Minimalist decorative element */}
          <motion.div
            className="absolute top-6 right-6 w-4 h-4 bg-[var(--accent-light)]/30 dark:bg-[var(--accent-dark)]/30 rounded-full"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.6, 0.3]
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />

          {/* Amazing Animated Image Section */}
          <motion.div
            variants={imageVariants}
            className="flex-shrink-0 flex justify-center w-full lg:w-auto order-1 lg:order-1 perspective-1000"
          >
            <div className="relative group/image">
              {/* Multiple layered glows for depth */}
              <motion.div
                className="absolute -inset-8 rounded-full bg-[var(--accent-light)]/15 dark:bg-[var(--accent-dark)]/15 blur-3xl"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.15, 0.3, 0.15]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />
              <motion.div
                className="absolute -inset-4 rounded-full bg-[var(--accent-dark)]/20 dark:bg-[var(--accent-light)]/20 blur-xl"
                animate={{
                  scale: [1.2, 1, 1.2],
                  opacity: [0.2, 0.4, 0.2]
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 1
                }}
              />

              {/* Floating particles around image */}
              {[...Array(6)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-2 h-2 bg-[var(--accent-light)]/60 dark:bg-[var(--accent-dark)]/60 rounded-full"
                  style={{
                    top: `${20 + (i * 15)}%`,
                    left: `${10 + (i % 2) * 80}%`,
                  }}
                  animate={{
                    y: [0, -20, 0],
                    x: [0, (i % 2 ? 10 : -10), 0],
                    opacity: [0.6, 1, 0.6],
                    scale: [1, 1.5, 1],
                  }}
                  transition={{
                    duration: 3 + (i * 0.5),
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: i * 0.3,
                  }}
                />
              ))}

              {/* Main image container with 3D effects */}
              <motion.div
                className="relative w-48 h-48 xs:w-56 xs:h-56 sm:w-64 sm:h-64 md:w-80 md:h-80 lg:w-96 lg:h-96 rounded-2xl sm:rounded-3xl overflow-hidden transform-gpu"
                animate={{
                  y: [0, -15, 0],
                  rotate: [0, 2, 0],
                  scale: [1, 1.02, 1],
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
                whileHover={{
                  scale: 1.05,
                  rotateY: 8,
                  rotateX: 5,
                  z: 50,
                  transition: { duration: 0.5, ease: "easeOut" }
                }}
                style={{
                  transformStyle: "preserve-3d",
                }}
              >
                {/* Animated background layers */}
                <motion.div
                  className="absolute inset-0 bg-gradient-to-br from-[var(--accent-light)]/20 via-card/60 to-[var(--accent-dark)]/20 backdrop-blur-sm"
                  animate={{
                    background: [
                      "linear-gradient(135deg, var(--accent-light)/20, var(--accent-dark)/20)",
                      "linear-gradient(225deg, var(--accent-dark)/20, var(--accent-light)/20)",
                      "linear-gradient(135deg, var(--accent-light)/20, var(--accent-dark)/20)"
                    ]
                  }}
                  transition={{ duration: 6, repeat: Infinity, ease: "easeInOut" }}
                />

                {/* Breaking effect - fragments */}
                <motion.div
                  className="absolute -top-4 -right-4 w-8 h-8 bg-[var(--accent-light)]/40 dark:bg-[var(--accent-dark)]/40 rounded-lg"
                  initial={{ opacity: 0, y: -50, rotate: 45 }}
                  animate={{
                    opacity: [0, 1, 0],
                    y: [-50, -20, -50],
                    rotate: [45, 90, 45]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 0.5
                  }}
                />
                <motion.div
                  className="absolute -top-6 -left-2 w-4 h-4 bg-[var(--accent-dark)]/50 dark:bg-[var(--accent-light)]/50 rounded-full"
                  initial={{ opacity: 0, y: -40, x: -10 }}
                  animate={{
                    opacity: [0, 1, 0],
                    y: [-40, -15, -40],
                    x: [-10, 5, -10]
                  }}
                  transition={{
                    duration: 2.5,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 1
                  }}
                />

                {/* Main image with enhanced effects */}
                <motion.div
                  className="relative w-full h-full"
                  whileHover={{
                    scale: 1.02,
                    transition: { duration: 0.3 }
                  }}
                >
                  <Image
                    src={image}
                    alt="NFT Development"
                    fill
                    className="relative object-contain z-10 drop-shadow-2xl group-hover/image:drop-shadow-[0_25px_50px_rgba(0,0,0,0.5)] transition-all duration-700"
                    priority
                    sizes="(max-width: 768px) 288px, 384px"
                  />
                </motion.div>

                {/* Dynamic border with pulse effect */}
                <motion.div
                  className="absolute inset-0 rounded-3xl border-2 border-[var(--accent-light)]/40 dark:border-[var(--accent-dark)]/40"
                  animate={{
                    borderColor: [
                      "var(--accent-light)/40",
                      "var(--accent-light)/80",
                      "var(--accent-light)/40"
                    ],
                    boxShadow: [
                      "0 0 20px var(--accent-light)/20",
                      "0 0 40px var(--accent-light)/40",
                      "0 0 20px var(--accent-light)/20"
                    ]
                  }}
                  transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
                />

                {/* Corner decorations with animation */}
                <motion.div
                  className="absolute top-4 left-4 w-6 h-6 border-l-2 border-t-2 border-[var(--accent-light)] dark:border-[var(--accent-dark)] rounded-tl-xl"
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.7, 1, 0.7]
                  }}
                  transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                />
                <motion.div
                  className="absolute bottom-4 right-4 w-6 h-6 border-r-2 border-b-2 border-[var(--accent-light)] dark:border-[var(--accent-dark)] rounded-br-xl"
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.7, 1, 0.7]
                  }}
                  transition={{ duration: 2, repeat: Infinity, ease: "easeInOut", delay: 1 }}
                />

                {/* Holographic effect overlay */}
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover/image:opacity-100"
                  animate={{
                    x: ["-100%", "100%"]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                    repeatDelay: 3
                  }}
                />
              </motion.div>
            </div>
          </motion.div>

          {/* Optimized Content Section */}
          <div className="flex-1 text-center lg:text-left z-10 order-2 lg:order-2 w-full">
            {/* Icon and title */}
            <motion.div variants={contentItemVariants} className="flex flex-col sm:flex-row items-center sm:items-center gap-3 sm:gap-4 mb-4 sm:mb-6">
              <motion.div
                className="relative"
                whileHover={{ scale: 1.05, rotate: 5 }}
                transition={{ duration: 0.3 }}
              >
                <div className="absolute inset-0 bg-[var(--accent-light)]/30 dark:bg-[var(--accent-dark)]/30 rounded-xl blur-sm"></div>
                <div className="relative bg-[var(--accent-light)]/80 dark:bg-[var(--accent-dark)]/80 hover:bg-[var(--accent-light)] dark:hover:bg-[var(--accent-dark)] p-2 sm:p-3 rounded-xl transition-all duration-300 backdrop-blur-sm">
                  <Sparkles className="w-5 h-5 sm:w-6 sm:h-6 md:w-7 md:h-7 text-foreground/80 hover:text-foreground transition-colors duration-300" />
                </div>
              </motion.div>
              <motion.h2
                className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold text-foreground group-hover:text-[var(--accent-dark)] dark:group-hover:text-[var(--accent-light)] transition-colors duration-500 text-center sm:text-left"
                whileHover={{ x: 5 }}
                transition={{ duration: 0.3 }}
              >
                {title}
              </motion.h2>
            </motion.div>

            <motion.p
              variants={contentItemVariants}
              className="text-muted-foreground group-hover:text-foreground/90 mb-6 sm:mb-8 text-sm sm:text-base md:text-lg leading-relaxed transition-colors duration-500 px-2 sm:px-0"
            >
              {description}
            </motion.p>

            {/* Optimized features list */}
            <div className="space-y-3">
              {features.map((feature, idx) => {
                const icons = [Shield, Zap, Rocket, Check, Sparkles];
                const IconComponent = icons[idx % icons.length];

                return (
                  <motion.div
                    variants={contentItemVariants}
                    key={idx}
                    className="flex items-center gap-4 group/feature cursor-pointer"
                    whileHover={{
                      x: 8,
                      transition: { duration: 0.3, ease: "easeOut" }
                    }}
                  >
                    <motion.div
                      className="relative p-2.5 rounded-lg bg-[var(--accent-light)]/70 dark:bg-[var(--accent-dark)]/70 hover:bg-[var(--accent-light)] dark:hover:bg-[var(--accent-dark)] shadow-md group-hover/feature:shadow-lg transition-all duration-300"
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      transition={{ duration: 0.2 }}
                    >
                      <IconComponent className="w-4 h-4 text-foreground/80 group-hover/feature:text-foreground transition-colors duration-300" />
                    </motion.div>
                    <span className="text-muted-foreground group-hover/feature:text-foreground font-medium transition-colors duration-300">
                      {feature}
                    </span>
                  </motion.div>
                );
              })}
            </div>

            {/* Enhanced CTA Button */}
            <motion.div variants={contentItemVariants} className="mt-8">
              <motion.button
                className="group/cta relative px-6 py-3 bg-[var(--accent-light)]/80 dark:bg-[var(--accent-dark)]/80 hover:bg-[var(--accent-light)] dark:hover:bg-[var(--accent-dark)] rounded-xl font-semibold text-foreground shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-[var(--accent-light)]/50 dark:border-[var(--accent-dark)]/50 hover:border-[var(--accent-light)] dark:hover:border-[var(--accent-dark)]"
                whileHover={{
                  scale: 1.05,
                  y: -2,
                  transition: { duration: 0.2 }
                }}
                whileTap={{ scale: 0.98 }}
              >
                <span className="relative flex items-center gap-2">
                  Get Started
                  <motion.div
                    animate={{ x: [0, 3, 0] }}
                    transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                  >
                    <Rocket className="w-4 h-4 group-hover/cta:rotate-12 transition-transform duration-300" />
                  </motion.div>
                </span>
                {/* Subtle shimmer effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover/cta:opacity-100 group-hover/cta:translate-x-full transition-all duration-500 -translate-x-full"></div>
              </motion.button>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
});
