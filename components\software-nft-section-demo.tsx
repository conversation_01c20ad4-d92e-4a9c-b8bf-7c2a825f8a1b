"use client";
import React from "react";
import { SoftwareNFTSection } from "./software-nft-section";

export const SoftwareNFTSectionDemo: React.FC = () => {
  const sampleData = {
    title: "NFT Development",
    description: "Create unique digital assets with our cutting-edge NFT development services. Build, mint, and deploy NFTs on multiple blockchain networks with advanced smart contract functionality.",
    features: [
      "Smart Contract Development",
      "Multi-Chain Support",
      "Custom Marketplace Integration",
      "Advanced Security Features",
      "Royalty Management System"
    ],
    image: "/software/NFT.png", // Make sure this image exists in your public folder
    lightColor: "#f5d0fe",
    darkColor: "#a21caf"
  };

  return (
    <div className="min-h-screen bg-background">
      <SoftwareNFTSection
        title={sampleData.title}
        description={sampleData.description}
        features={sampleData.features}
        image={sampleData.image}
        lightColor={sampleData.lightColor}
        darkColor={sampleData.darkColor}
      />
    </div>
  );
};
