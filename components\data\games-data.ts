export interface Game {
  id: string;
  name: string;
  img: string;
  title: string;
  description: string;
  longDescription: string;
  price: string;
  category: string;
  players: string;
  features: string[];
  color: 'sky' | 'purple' | 'emerald' | 'orange';
  highlight: string;
  screenshots: string[];
  technicalSpecs: {
    platforms: string[];
    engine: string;
    graphics: string;
    storage: string;
    multiplayer: boolean;
  };
  gameplayFeatures: string[];
  downloadStats: {
    downloads: string;
    rating: number;
    reviews: number;
  };
}

export const gamesData: Game[] = [
  {
    id: 'teen-patti',
    name: 'Teen Patti',
    img: '/gaming/teenpatti.png',
    title: 'Teen Patti - The Ultimate Card Game',
    description: 'Experience the thrill of traditional Indian card game with modern features and real-time multiplayer action',
    longDescription: 'Teen Patti is the ultimate digital adaptation of India\'s most beloved card game. Our version combines traditional gameplay with cutting-edge technology to deliver an immersive gaming experience. Play with friends or challenge players worldwide in real-time multiplayer matches. Features include advanced AI opponents, multiple game variants, tournament modes, and social features that make every game exciting.',
    price: 'Customizable Packages',
    category: 'Card Game',
    players: '2-6 Players',
    features: ['Multiplayer', 'Real-time', 'Tournaments', 'Chat System'],
    color: 'sky',
    highlight: 'Most Popular',
    screenshots: [
      '/gaming/teenpatti-screen1.png',
      '/gaming/teenpatti-screen2.png',
      '/gaming/teenpatti-screen3.png',
      '/gaming/teenpatti-screen4.png'
    ],
    technicalSpecs: {
      platforms: ['Android', 'iOS', 'Web', 'Windows'],
      engine: 'Unity 2022.3',
      graphics: 'HD Graphics with 60 FPS',
      storage: '150 MB',
      multiplayer: true
    },
    gameplayFeatures: [
      'Classic Teen Patti Rules',
      'Multiple Game Variants',
      'Private Table Creation',
      'Real-time Chat System',
      'Tournament Mode',
      'Daily Challenges',
      'Leaderboards',
      'Social Sharing'
    ],
    downloadStats: {
      downloads: '1M+',
      rating: 4.8,
      reviews: 25000
    }
  },
  {
    id: 'rummy-game',
    name: 'Rummy Game',
    img: '/gaming/rumygame.png',
    title: 'Rummy - A Game of Skill & Strategy',
    description: 'Master the art of melding cards in this strategic rummy experience with AI opponents and tutorials',
    longDescription: 'Our Rummy game is designed for players who love strategic card games. With multiple variants including Points Rummy, Pool Rummy, and Deals Rummy, players can enjoy different gameplay styles. The game features advanced AI opponents that adapt to your skill level, comprehensive tutorials for beginners, and competitive tournaments for experienced players.',
    price: 'Customizable Packages',
    category: 'Strategy',
    players: '2-4 Players',
    features: ['AI Opponents', 'Tutorials', 'Leaderboards', 'Practice Mode'],
    color: 'purple',
    highlight: 'Best Strategy',
    screenshots: [
      '/gaming/rummy-screen1.png',
      '/gaming/rummy-screen2.png',
      '/gaming/rummy-screen3.png',
      '/gaming/rummy-screen4.png'
    ],
    technicalSpecs: {
      platforms: ['Android', 'iOS', 'Web'],
      engine: 'Unity 2022.3',
      graphics: 'HD Graphics with Smooth Animations',
      storage: '120 MB',
      multiplayer: true
    },
    gameplayFeatures: [
      'Points Rummy',
      'Pool Rummy',
      'Deals Rummy',
      'Practice Mode',
      'AI Opponents',
      'Tutorial System',
      'Statistics Tracking',
      'Achievement System'
    ],
    downloadStats: {
      downloads: '800K+',
      rating: 4.6,
      reviews: 18500
    }
  },
  {
    id: 'ludo-game',
    name: 'Ludo Game',
    img: '/gaming/ludogame.png',
    title: 'Ludo - The Classic Multiplayer Fun',
    description: 'Relive childhood memories with friends in this classic board game featuring voice chat and themes',
    longDescription: 'Bring back the nostalgia of childhood with our beautifully crafted Ludo game. Play with family and friends online or challenge random opponents from around the world. The game features multiple board themes, voice chat functionality, private rooms, and quick match options. Perfect for family game nights or casual gaming sessions.',
    price: 'Customizable Packages',
    category: 'Board Game',
    players: '2-4 Players',
    features: ['Custom Themes', 'Quick Match', 'Private Rooms'],
    color: 'emerald',
    highlight: 'Family Favorite',
    screenshots: [
      '/gaming/ludo-screen1.png',
      '/gaming/ludo-screen2.png',
      '/gaming/ludo-screen3.png',
      '/gaming/ludo-screen4.png'
    ],
    technicalSpecs: {
      platforms: ['Android', 'iOS', 'Web', 'Windows'],
      engine: 'Unity 2022.3',
      graphics: 'Colorful HD Graphics',
      storage: '100 MB',
      multiplayer: true
    },
    gameplayFeatures: [
      'Classic Ludo Rules',
      'Multiple Board Themes',
      'Voice Chat',
      'Private Rooms',
      'Quick Match',
      'Offline Mode',
      'Custom Avatars',
      'Friend Invitations'
    ],
    downloadStats: {
      downloads: '2M+',
      rating: 4.7,
      reviews: 35000
    }
  },
  {
    id: 'poker-game',
    name: 'Poker Game',
    img: '/gaming/pockergame.png',
    title: 'Poker - Your Strategic Edge',
    description: 'Test your poker skills in high-stakes tournaments and cash games with advanced statistics tracking',
    longDescription: 'Experience the thrill of professional poker with our advanced poker game. Featuring Texas Hold\'em, Omaha, and other popular variants, this game is perfect for both beginners and experienced players. Advanced AI opponents, detailed statistics tracking, tournament modes, and social features make this the ultimate poker experience.',
    price: 'Customizable Packages',
    category: 'Casino',
    players: '2-8 Players',
    features: ['Texas Hold\'em', 'Bluffing AI', 'Statistics', 'Tournaments'],
    color: 'orange',
    highlight: 'Pro Level',
    screenshots: [
      '/gaming/poker-screen1.png',
      '/gaming/poker-screen2.png',
      '/gaming/poker-screen3.png',
      '/gaming/poker-screen4.png'
    ],
    technicalSpecs: {
      platforms: ['Android', 'iOS', 'Web', 'Windows', 'Mac'],
      engine: 'Unity 2022.3',
      graphics: 'Realistic 3D Graphics',
      storage: '200 MB',
      multiplayer: true
    },
    gameplayFeatures: [
      'Texas Hold\'em',
      'Omaha Poker',
      'Tournament Mode',
      'Cash Games',
      'Bluffing AI',
      'Statistics Tracking',
      'Hand History',
      'Professional Tables'
    ],
    downloadStats: {
      downloads: '1.5M+',
      rating: 4.9,
      reviews: 42000
    }
  }
];

export const getGameById = (id: string): Game | undefined => {
  return gamesData.find(game => game.id === id);
};

export const getAllGameIds = (): string[] => {
  return gamesData.map(game => game.id);
};
