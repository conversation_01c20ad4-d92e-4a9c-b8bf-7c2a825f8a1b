'use client';

import React from 'react';
import { Lightbulb, Palette, Code, TestTube, Rocket, Users, Gamepad2, Settings } from 'lucide-react';
import Image from 'next/image';

const processSteps = [
  {
    icon: Lightbulb,
    title: 'Concept & Planning',
    description: 'Define game concept, target audience, and core mechanics',
    details: ['Game concept development', 'Market research', 'Technical feasibility', 'Project roadmap'],
    color: '#1e40af', // blue-800
    delay: 0
  },
  {
    icon: Palette,
    title: 'Game Design',
    description: 'Create detailed game design document and visual style',
    details: ['Game mechanics design', 'Level design', 'Art style guide', 'UI/UX wireframes'],
    color: '#2563eb', // blue-600
    delay: 0.1
  },
  {
    icon: Code,
    title: 'Development',
    description: 'Build the game using cutting-edge technologies',
    details: ['Core gameplay programming', 'Graphics implementation', 'Audio integration', 'Platform optimization'],
    color: '#3b82f6', // blue-500
    delay: 0.2
  },
  {
    icon: TestTube,
    title: 'Testing & QA',
    description: 'Comprehensive testing across all platforms',
    details: ['Functionality testing', 'Performance optimization', 'Bug fixing', 'User acceptance testing'],
    color: '#60a5fa', // blue-400
    delay: 0.3
  },
  {
    icon: Rocket,
    title: 'Launch & Deploy',
    description: 'Deploy to app stores and gaming platforms',
    details: ['Store submission', 'Marketing support', 'Launch strategy', 'Distribution setup'],
    color: '#93c5fd', // blue-300
    delay: 0.4
  },
  {
    icon: Settings,
    title: 'Post-Launch Support',
    description: 'Ongoing maintenance and feature updates',
    details: ['Bug fixes', 'Performance monitoring', 'New features', 'Community support'],
    color: '#1d4ed8', // blue-700
    delay: 0.5
  }
];

const GamingDevelopmentProcess: React.FC = () => {
  return (
    <section className="relative py-8 xs:py-10 sm:py-12 md:py-16 lg:py-20 px-2 xs:px-3 sm:px-4 overflow-hidden bg-white dark:bg-blue-950 transition-all duration-500">
      {/* Enhanced Responsive Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Animated gradient orbs */}
        <div className="absolute -top-20 xs:-top-30 sm:-top-40 -right-20 xs:-right-30 sm:-right-40 w-40 xs:w-60 sm:w-80 h-40 xs:h-60 sm:h-80 bg-gradient-to-br from-blue-500/40 via-blue-600/30 to-blue-700/20 dark:from-blue-400/30 dark:via-blue-500/20 dark:to-blue-600/15 rounded-full blur-2xl xs:blur-3xl animate-pulse" />
        <div className="absolute -bottom-20 xs:-bottom-30 sm:-bottom-40 -left-20 xs:-left-30 sm:-left-40 w-40 xs:w-60 sm:w-80 h-40 xs:h-60 sm:h-80 bg-gradient-to-tr from-blue-600/35 via-blue-700/25 to-blue-800/15 dark:from-blue-500/25 dark:via-blue-600/15 dark:to-blue-700/10 rounded-full blur-2xl xs:blur-3xl animate-pulse" style={{ animationDelay: '1s' }} />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-48 xs:w-72 sm:w-96 h-48 xs:h-72 sm:h-96 bg-gradient-to-r from-blue-400/20 via-blue-500/15 to-blue-600/10 dark:from-blue-300/15 dark:via-blue-400/10 dark:to-blue-500/8 rounded-full blur-2xl xs:blur-3xl animate-pulse" style={{ animationDelay: '2s' }} />

        {/* Floating gaming elements */}
        <div className="absolute top-16 left-16 w-6 h-6 text-blue-400/40 dark:text-blue-300/30 animate-bounce" style={{ animationDelay: '0.5s' }}>
          <Code className="w-full h-full" />
        </div>
        <div className="absolute top-32 right-24 w-8 h-8 text-blue-500/50 dark:text-blue-400/40 animate-bounce" style={{ animationDelay: '1.5s' }}>
          <Rocket className="w-full h-full" />
        </div>
        <div className="absolute bottom-24 left-32 w-7 h-7 text-blue-600/45 dark:text-blue-500/35 animate-bounce" style={{ animationDelay: '2.5s' }}>
          <Settings className="w-full h-full" />
        </div>
      </div>

      <div className="relative container-responsive">
        {/* Modern Header Design */}
        <div className="relative text-center mb-12 xs:mb-16 sm:mb-20 md:mb-24 animate-fade-in-up">
          {/* Process Badge */}
          <div className="inline-flex items-center gap-3 px-6 py-3 mb-8 bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-full border border-blue-200/50 dark:border-blue-700/50 shadow-xl hover:shadow-2xl transition-all duration-500 group">
            <div className="relative">
              <Settings className="w-6 h-6 text-blue-600 dark:text-blue-400 animate-spin" style={{ animationDuration: '10s' }} />
              <div className="absolute inset-0 bg-blue-500/20 dark:bg-blue-400/20 rounded-full animate-ping"></div>
            </div>
            <span className="text-base font-bold text-gray-700 dark:text-gray-200 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
              Professional Development Process
            </span>
          </div>

          {/* Main Title */}
          <div className="relative mb-10">
            <h1 className="text-4xl xs:text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-black mb-6 leading-tight">
              <span className="block bg-gradient-to-r from-slate-900 via-blue-800 to-indigo-900 dark:from-slate-100 dark:via-blue-200 dark:to-indigo-100 bg-clip-text text-transparent hover:from-blue-800 hover:to-indigo-800 dark:hover:from-blue-100 dark:hover:to-indigo-50 transition-all duration-700 transform hover:scale-105">
                Development
              </span>
              <span className="block bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 dark:from-blue-400 dark:via-indigo-400 dark:to-purple-400 bg-clip-text text-transparent hover:from-indigo-600 hover:to-purple-600 dark:hover:from-indigo-300 dark:hover:to-purple-300 transition-all duration-700 transform hover:scale-105">
                Process
              </span>
            </h1>

            {/* Animated underline */}
            <div className="flex justify-center">
              <div className="relative">
                <div className="w-40 h-2 bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 dark:from-blue-400 dark:via-indigo-400 dark:to-purple-400 rounded-full"></div>
                <div className="absolute inset-0 w-40 h-2 bg-gradient-to-r from-blue-400 via-indigo-400 to-purple-400 dark:from-blue-300 dark:via-indigo-300 dark:to-purple-300 rounded-full animate-pulse"></div>
              </div>
            </div>
          </div>

          {/* Enhanced Description */}
          <div className="relative max-w-5xl mx-auto">
            <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-xl rounded-3xl p-8 xs:p-10 sm:p-12 border border-white/20 dark:border-gray-700/20 shadow-2xl hover:shadow-3xl transition-all duration-500 group">
              <p className="text-lg xs:text-xl sm:text-2xl text-gray-700 dark:text-gray-200 leading-relaxed font-medium group-hover:text-gray-900 dark:group-hover:text-gray-100 transition-colors duration-300">
                <span className="hidden sm:inline">
                  Our <span className="text-blue-600 dark:text-blue-400 font-semibold">comprehensive game development process</span> ensures every project is delivered with precision, creativity, and technical excellence. From initial concept to final deployment, we follow industry best practices to create exceptional gaming experiences.
                </span>
                <span className="sm:hidden">
                  <span className="text-blue-600 dark:text-blue-400 font-semibold">Professional game development</span> process from concept to deployment
                </span>
              </p>
            </div>
          </div>
        </div>

        {/* Process Steps */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 xs:gap-7 sm:gap-8">
          {processSteps.map((step, index) => {
            const IconComponent = step.icon;
            return (
              <div
                key={index}
                className="group relative bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-3xl p-6 shadow-xl hover:shadow-2xl border border-gray-200/50 dark:border-gray-600/50 transition-all duration-500 hover:-translate-y-4 hover:scale-105 animate-fade-in-up overflow-hidden"
                style={{ animationDelay: `${step.delay}s` }}
              >
                {/* Enhanced Side Color Overlap */}
                <div className="absolute -right-3 top-4 bottom-4 w-6 bg-gradient-to-b from-blue-400 via-blue-500 to-blue-600 dark:from-blue-500 dark:via-blue-600 dark:to-blue-700 rounded-l-xl shadow-lg z-20"></div>
                {/* Gradient background overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-50/60 via-transparent to-blue-100/40 dark:from-blue-900/40 dark:via-transparent dark:to-blue-800/30 opacity-0 group-hover:opacity-100 transition-all duration-500 rounded-3xl"></div>

                {/* Enhanced step number */}
                <div className="absolute top-4 right-4 w-10 h-10 rounded-full bg-gradient-to-r from-blue-100 to-blue-200 dark:from-blue-800 dark:to-blue-700 flex items-center justify-center text-sm font-bold text-blue-700 dark:text-blue-200 shadow-lg group-hover:scale-110 transition-all duration-300">
                  {index + 1}
                </div>

                {/* Enhanced colored top bar with gradient */}
                <div className="absolute top-0 left-0 w-full h-3 rounded-t-3xl bg-gradient-to-r" style={{ background: `linear-gradient(90deg, ${step.color}, ${step.color}80, ${step.color})` }}></div>

                {/* Animated corner decorations */}
                <div className="absolute -top-3 -left-3 w-20 h-20 rounded-full opacity-5 group-hover:opacity-15 transition-all duration-500 animate-pulse" style={{ backgroundColor: step.color }}></div>
                <div className="absolute -bottom-3 -right-3 w-16 h-16 rounded-full opacity-5 group-hover:opacity-10 transition-all duration-500 animate-pulse" style={{ backgroundColor: step.color, animationDelay: '1s' }}></div>

                {/* Enhanced Icon container with floating effect */}
                <div className="relative mb-6 w-20 h-20 mx-auto flex items-center justify-center rounded-3xl group-hover:scale-125 group-hover:rotate-6 transition-all duration-500 shadow-lg group-hover:shadow-xl" style={{ backgroundColor: `${step.color}15` }}>
                  <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-white/30 to-transparent dark:from-white/20 dark:to-transparent"></div>
                  <IconComponent className="w-10 h-10 transition-all duration-500 relative z-10" style={{ color: step.color }} />

                  {/* Floating particles around icon */}
                  <div className="absolute -top-1 -right-1 w-3 h-3 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-500 animate-ping" style={{ backgroundColor: step.color }}></div>
                  <div className="absolute -bottom-1 -left-1 w-2 h-2 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-500 animate-ping" style={{ backgroundColor: step.color, animationDelay: '0.5s' }}></div>
                  <div className="absolute top-1/2 -right-2 w-1.5 h-1.5 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-500 animate-ping" style={{ backgroundColor: step.color, animationDelay: '1s' }}></div>
                </div>

                {/* Enhanced Content */}
                <h3 className="text-xl font-bold mb-3 text-center text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-all duration-500 relative z-10">
                  {step.title}
                </h3>

                <p className="text-gray-600 dark:text-gray-300 text-center leading-relaxed mb-4 group-hover:text-gray-800 dark:group-hover:text-gray-100 transition-all duration-500 relative z-10">
                  {step.description}
                </p>

                {/* Enhanced Details list */}
                <ul className="space-y-3 relative z-10">
                  {step.details.map((detail, detailIndex) => (
                    <li key={detailIndex} className="flex items-center text-sm text-gray-500 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-200 transition-all duration-300" style={{ animationDelay: `${detailIndex * 0.1}s` }}>
                      <div className="w-3 h-3 rounded-full mr-3 flex-shrink-0 shadow-sm group-hover:scale-125 transition-all duration-300" style={{ backgroundColor: step.color }}></div>
                      <span className="group-hover:font-medium transition-all duration-300">{detail}</span>
                    </li>
                  ))}
                </ul>

                {/* Progress indicator */}
                <div className="mt-4 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden relative z-10">
                  <div className="h-full bg-gradient-to-r transition-all duration-1000 group-hover:w-full w-0" style={{ background: `linear-gradient(90deg, ${step.color}, ${step.color}80)` }}></div>
                </div>

                {/* Enhanced Hover border with glow */}
                <div className="absolute inset-0 rounded-3xl border-2 border-transparent group-hover:border-blue-400/50 dark:group-hover:border-blue-500/50 transition-all duration-500"></div>
                <div className="absolute inset-0 rounded-3xl opacity-0 group-hover:opacity-100 transition-all duration-500" style={{ boxShadow: `0 0 30px ${step.color}20` }}></div>
              </div>
            );
          })}
        </div>

        {/* Gaming Showcase Section */}
        <div className="mt-12 xs:mt-14 sm:mt-16 mb-8 xs:mb-10 sm:mb-12 animate-fade-in-up" style={{ animationDelay: '0.6s' }}>
          <h3 className="text-2xl xs:text-3xl font-bold text-center mb-8 text-gray-900 dark:text-white">
            Our <span className="text-blue-600 dark:text-blue-400">Gaming Portfolio</span>
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 xs:gap-6">
            <div className="group relative bg-white dark:bg-gray-800 rounded-xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2">
              <Image
                src="/gaming/ludogame.png"
                alt="Ludo Game Development"
                width={150}
                height={150}
                className="w-full h-24 xs:h-28 sm:h-32 object-cover rounded-lg mb-3"
              />
              <h4 className="text-sm xs:text-base font-semibold text-gray-900 dark:text-white text-center">Ludo Game</h4>
              <p className="text-xs text-gray-600 dark:text-gray-300 text-center">Multiplayer Board Game</p>
            </div>

            <div className="group relative bg-white dark:bg-gray-800 rounded-xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2">
              <Image
                src="/gaming/teenpatti.png"
                alt="Teen Patti Game Development"
                width={150}
                height={150}
                className="w-full h-24 xs:h-28 sm:h-32 object-cover rounded-lg mb-3"
              />
              <h4 className="text-sm xs:text-base font-semibold text-gray-900 dark:text-white text-center">Teen Patti</h4>
              <p className="text-xs text-gray-600 dark:text-gray-300 text-center">Card Game</p>
            </div>

            <div className="group relative bg-white dark:bg-gray-800 rounded-xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2">
              <Image
                src="/gaming/rumygame.png"
                alt="Rummy Game Development"
                width={150}
                height={150}
                className="w-full h-24 xs:h-28 sm:h-32 object-cover rounded-lg mb-3"
              />
              <h4 className="text-sm xs:text-base font-semibold text-gray-900 dark:text-white text-center">Rummy Game</h4>
              <p className="text-xs text-gray-600 dark:text-gray-300 text-center">Strategy Card Game</p>
            </div>

            <div className="group relative bg-white dark:bg-gray-800 rounded-xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2">
              <Image
                src="/gaming/pockergame.png"
                alt="Poker Game Development"
                width={150}
                height={150}
                className="w-full h-24 xs:h-28 sm:h-32 object-cover rounded-lg mb-3"
              />
              <h4 className="text-sm xs:text-base font-semibold text-gray-900 dark:text-white text-center">Poker Game</h4>
              <p className="text-xs text-gray-600 dark:text-gray-300 text-center">Casino Game</p>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center mt-12 xs:mt-14 sm:mt-16 animate-fade-in-up" style={{ animationDelay: '0.7s' }}>
          <div className="bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 rounded-2xl p-6 xs:p-7 sm:p-8 text-white shadow-2xl border border-blue-500/20">
            <div className="flex items-center justify-center mb-4">
              <Gamepad2 className="w-8 h-8 mr-3 text-blue-200" />
              <h3 className="text-xl xs:text-2xl font-bold">Ready to Start Your Game Development Journey?</h3>
            </div>
            <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
              Let's bring your game idea to life with our proven development process and expert team.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-white text-blue-700 px-6 xs:px-8 py-3 xs:py-4 rounded-full font-semibold hover:bg-blue-50 transition-all duration-300 transform hover:scale-105 shadow-lg border-2 border-blue-200">
                Start Your Project
              </button>
              <button className="border-2 border-blue-300 text-blue-100 px-6 xs:px-8 py-3 xs:py-4 rounded-full font-semibold hover:bg-blue-600 hover:border-blue-400 transition-all duration-300 transform hover:scale-105">
                View Portfolio
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default GamingDevelopmentProcess;
