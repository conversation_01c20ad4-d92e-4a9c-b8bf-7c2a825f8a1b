import { statsData } from "@/components/data/stats-data";
import type { Metadata } from 'next';
import SoftwareNavigation from "@/components/software-navigation";
import dynamic from 'next/dynamic';
import { LoadingSpinner, SkeletonLoader, HeroSkeleton } from '@/components/ui/LoadingSpinner';
import ErrorBoundary from '@/components/ui/ErrorBoundary';


// Lazy load all heavy components to improve performance
const LazyHeroSlider = dynamic(() => import('@/components/software/HeroSlider'), {
  loading: () => <HeroSkeleton />,
  ssr: true
});

const LazyWhatWeDoSection = dynamic(() => import('@/components/what-we-do-section'), {
  loading: () => <SkeletonLoader className="py-16 px-4" />,
  ssr: false
});

const LazyStats = dynamic(() => import('@/components/stats').then(mod => ({ default: mod.Stats })), {
  loading: () => <LoadingSpinner text="Loading statistics..." />,
  ssr: false
});

const LazyServicesSection = dynamic(() => import('@/components/software/LazyServicesSection'), {
  loading: () => <SkeletonLoader className="py-16 px-4" />,
  ssr: false
});

const LazyWhyChooseUs = dynamic(() => import('@/components/software/whyChooseUs'), {
  loading: () => <SkeletonLoader className="py-16 px-4" />,
  ssr: false
});

const LazyDevelopmentProcess = dynamic(() => import('@/components/software/DevelopmentProcess'), {
  loading: () => <SkeletonLoader className="py-16 px-4" />,
  ssr: false
});

const LazyTestimonialSlider = dynamic(() => import('@/components/TestimonialSlider'), {
  loading: () => <LoadingSpinner text="Loading testimonials..." />,
  ssr: false
});

const LazyFAQSection = dynamic(() => import('@/components/faq-section'), {
  loading: () => <SkeletonLoader className="py-16 px-4" />,
  ssr: false
});

const LazyAboutUsSection = dynamic(() => import('@/components/about-us-section'), {
  loading: () => <SkeletonLoader className="py-16 px-4" />,
  ssr: false
});

const LazyAllServicesTabs = dynamic(() => import('@/components/software/AllServicesTabs'), {
  loading: () => <LoadingSpinner text="Loading services..." />,
  ssr: false
});

export const metadata: Metadata = {
  metadataBase: new URL('https://brtmultisoftware.com'),
  title: 'Software Development Services | Custom Software Solutions | BRT Multi Software',
  description: 'Get top-notch custom software development, web & mobile app solutions, blockchain, AI, and cloud integration from BRT Multi Software. Trusted by 500+ clients worldwide. Accelerate your business with our expert developers.',
  keywords: [
    'Software Development',
    'Custom Software',
    'Web Development',
    'Mobile App Development',
    'Blockchain Development',
    'AI Solutions',
    'Cloud Integration',
    'Enterprise Software',
    'BRT Multi Software',
    'IT Services',
    'SaaS',
    'India',
    'Outsourcing',
    'Software Company'
  ],
  authors: [{ name: 'BRT Multi Software', url: 'https://brtmultisoftware.com/' }],
  creator: 'BRT Multi Software',
  robots: {
    index: true,
    follow: true,
    nocache: false,
    googleBot: {
      index: true,
      follow: true,
      noimageindex: false,
      'max-snippet': -1,
      'max-image-preview': 'large',
      'max-video-preview': -1,
    },
  },
  openGraph: {
    title: 'Software Development Services | Custom Software Solutions | BRT Multi Software',
    description: 'Get top-notch custom software development, web & mobile app solutions, blockchain, AI, and cloud integration from BRT Multi Software. Trusted by 500+ clients worldwide.',
    url: 'https://brtmultisoftware.com/software-development',
    siteName: 'BRT Multi Software',
    images: [
      {
        url: 'https://brtmultisoftware.com/img/Software-animation.gif',
        width: 1200,
        height: 630,
        alt: 'Custom Software Development by BRT Multi Software',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Software Development Services | Custom Software Solutions | BRT Multi Software',
    description: 'Get top-notch custom software development, web & mobile app solutions, blockchain, AI, and cloud integration from BRT Multi Software. Trusted by 500+ clients worldwide.',
    site: '@brtmultisoftware',
    images: ['https://brtmultisoftware.com/img/Software-animation.gif'],
  },
  alternates: {
    canonical: 'https://brtmultisoftware.com/software-development',
  },
};

export default function SoftwareDevelopmentPage() {
  return (
    <main className="min-h-screen bg-background text-foreground overflow-x-hidden">
      <SoftwareNavigation />

      <ErrorBoundary>
        {/* Hero Section - Enhanced Responsive */}
        <section id="home" className="w-full min-h-[60vh] xs:min-h-[65vh] sm:min-h-[70vh] md:min-h-[75vh] lg:min-h-[80vh] xl:min-h-[85vh]">
          <LazyHeroSlider />
        </section>
      </ErrorBoundary>

      <ErrorBoundary>
        {/* Services Section - Enhanced Responsive */}
        <section id="services" className="w-full py-4 xs:py-6 sm:py-8 md:py-12 lg:py-16">
          <LazyWhatWeDoSection />
        </section>
      </ErrorBoundary>

      <ErrorBoundary>
        {/* Stats Section - Enhanced Responsive */}
        <section className="w-full py-4 xs:py-6 sm:py-8 md:py-10 lg:py-12">
          <LazyStats stats={statsData} />
        </section>
      </ErrorBoundary>

      <ErrorBoundary>
        {/* Software Services Sections - Enhanced Responsive */}
        <div className="w-full py-2 xs:py-3 sm:py-4 md:py-6 lg:py-8">
          <LazyServicesSection />
        </div>
      </ErrorBoundary>

      <ErrorBoundary>
        {/* Why Choose Us Section - Enhanced Responsive */}
        <section id="why-choose-us" className="w-full py-4 xs:py-6 sm:py-8 md:py-12 lg:py-16">
          <LazyWhyChooseUs />
        </section>
      </ErrorBoundary>

      <ErrorBoundary>
        {/* Development Process Section - Enhanced Responsive */}
        <section id="development-process" className="w-full py-4 xs:py-6 sm:py-8 md:py-12 lg:py-16">
          <LazyDevelopmentProcess />
        </section>
      </ErrorBoundary>

      <ErrorBoundary>
        {/* Testimonials Section - Enhanced Responsive */}
        <section id="testimonials" className="w-full py-4 xs:py-6 sm:py-8 md:py-12 lg:py-16">
          <LazyTestimonialSlider />
        </section>
      </ErrorBoundary>

      <ErrorBoundary>
        {/* About Section - Enhanced Responsive */}
        <section id="about" className="w-full py-4 xs:py-6 sm:py-8 md:py-12 lg:py-16">
          <LazyAboutUsSection />
        </section>
      </ErrorBoundary>

      <ErrorBoundary>
        {/* All Services Section - Enhanced Responsive */}
        <section id="all-services" className="w-full py-4 xs:py-6 sm:py-8 md:py-12 lg:py-16">
          <LazyAllServicesTabs />
        </section>
      </ErrorBoundary>

      <ErrorBoundary>
        {/* FAQ Section - Enhanced Responsive */}
        <section id="faq" className="w-full py-4 xs:py-6 sm:py-8 md:py-12 lg:py-16">
          <LazyFAQSection />
        </section>
      </ErrorBoundary>

      {/* Enhanced Responsive CTA Section */}
      <section id="contact" className="w-full py-6 xs:py-8 sm:py-10 md:py-12 lg:py-16 px-3 xs:px-4 sm:px-6 md:px-8 lg:px-12 bg-gradient-to-r from-gray-900 via-gray-800 to-black relative overflow-hidden">
        {/* Responsive background patterns */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/10 to-transparent"></div>
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-white/5 to-transparent"></div>

        {/* Responsive accent elements */}
        <div className="absolute top-2 right-2 xs:top-3 xs:right-3 sm:top-4 sm:right-4 w-12 h-12 xs:w-16 xs:h-16 sm:w-20 sm:h-20 bg-blue-500/10 rounded-full blur-xl"></div>
        <div className="absolute bottom-2 left-2 xs:bottom-3 xs:left-3 sm:bottom-4 sm:left-4 w-10 h-10 xs:w-12 xs:h-12 sm:w-16 sm:h-16 bg-purple-500/10 rounded-full blur-xl"></div>

        <div className="container-responsive text-center relative z-10">
          <h2 className="text-responsive-lg font-bold text-white mb-3 xs:mb-4 sm:mb-6">
            Let's Build Something Amazing! 🚀
          </h2>
          <p className="text-responsive-sm text-gray-300 mb-6 xs:mb-8 sm:mb-10 max-w-xs xs:max-w-sm sm:max-w-md md:max-w-lg mx-auto leading-relaxed">
            Ready to transform your ideas into reality?
          </p>

          <div className="flex flex-col xs:flex-row gap-3 xs:gap-4 sm:gap-5 justify-center items-center max-w-xs xs:max-w-sm sm:max-w-md md:max-w-lg mx-auto">
            <a
              href="mailto:<EMAIL>"
              className="group w-full xs:w-auto px-4 xs:px-5 sm:px-6 md:px-8 py-2.5 xs:py-3 sm:py-3.5 md:py-4 bg-blue-600 text-white rounded-full font-semibold hover:bg-blue-700 transition-all duration-300 hover:scale-105 shadow-lg shadow-blue-600/25 flex items-center justify-center gap-2 text-sm xs:text-base"
            >
              <span>Get Started</span>
              <span className="group-hover:translate-x-1 transition-transform">→</span>
            </a>
            <a
              href="tel:+1234567890"
              className="w-full xs:w-auto px-4 xs:px-5 sm:px-6 md:px-8 py-2.5 xs:py-3 sm:py-3.5 md:py-4 border-2 border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white hover:border-gray-500 rounded-full font-semibold transition-all duration-300 hover:scale-105 text-sm xs:text-base"
            >
              📞 Call Now
            </a>
          </div>
        </div>
      </section>

    </main>
  );
}