"use client"
import React from "react";
import Link from "next/link";
import Image from "next/image";
import { motion } from "framer-motion";
import { serviceGroups, companyLinks, socialLinks } from "./data/links";
import GetInTouch from "./get-in-touch";

export function Footer() {
  return (<>
   <GetInTouch />
   <motion.footer 
      className="bg-background text-foreground border-t border-border mt-16"
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-8">
          
          <div className="col-span-2 md:col-span-4 lg:col-span-1">
            <Link href="/" className="flex items-center gap-2">
              <Image src="/assets/images/brt-logo.png" alt="BRT Logo" width={40} height={40} />
              <span className="text-lg font-bold text-primary">BRT Multi Software</span>
            </Link>
            <p className="text-muted-foreground text-xs leading-snug mt-2">
            At BRT Multi Software, we redefine excellence in digital transformation solutions. Our mission is clear: to provide top-notch AI, Blockchain, and Web3 solutions that empower businesses and exceed expectations. Partner with us to shape a dynamic digital future — your success is our priority
            </p>
          </div>
          
          {serviceGroups.map((group) => (
            <div key={group.title}>
              <h3 className="text-sm font-semibold text-primary mb-4 flex items-center gap-2">
                <group.icon className="h-4 w-4" />
                {group.title}
              </h3>
              <ul className="text-xs space-y-2">
                {group.services.map((service) => (
                  <li key={service.name} className="flex items-center gap-2">
                    <service.icon className="h-3 w-3 text-primary" />
                    <Link 
                      href={service.href} 
                      className="hover:text-primary transition-all duration-300 hover:pl-1"
                    >
                      {service.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}

          <div>
            <h3 className="text-sm font-semibold text-primary mb-4">Company</h3>
            <ul className="text-xs space-y-2">
              {companyLinks.map((link) => (
                <li key={link.label}>
                  <Link 
                    href={link.href} 
                    className="hover:text-primary transition-all duration-300 hover:pl-1"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>
        
        <div className="border-t border-border mt-8 pt-6 flex flex-col sm:flex-row justify-between items-center">
          <p className="text-xs text-muted-foreground mb-4 sm:mb-0">
            &copy; {new Date().getFullYear()} BRT Multi Software. All rights reserved.
          </p>
          <div className="flex items-center space-x-2">
            {socialLinks.map((social) => (
              <motion.a 
                key={social.name} 
                href={social.href} 
                className="text-muted-foreground hover:text-primary p-2 rounded-full transition-colors hover:bg-primary/10"
                whileHover={{ scale: 1.1, rotate: 5 }}
                whileTap={{ scale: 0.95 }}
                aria-label={social.name}
              >
                <social.icon className="h-5 w-5" />
              </motion.a>
            ))}
          </div>
        </div>
      </div>
    </motion.footer>
  </>
   
  );
} 