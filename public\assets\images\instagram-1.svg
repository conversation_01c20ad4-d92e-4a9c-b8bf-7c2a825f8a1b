<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 128 128" id="instagram">
  <defs>
    <clipPath id="b">
      <circle cx="64" cy="64" r="64" fill="none"></circle>
    </clipPath>
    <clipPath id="c">
      <path fill="none" d="M104-163H24a24.07 24.07 0 0 0-24 24v80a24.07 24.07 0 0 0 24 24h80a24.07 24.07 0 0 0 24-24v-80a24.07 24.07 0 0 0-24-24Zm16 104a16 16 0 0 1-16 16H24A16 16 0 0 1 8-59v-80a16 16 0 0 1 16-16h80a16 16 0 0 1 16 16Z"></path>
    </clipPath>
    <clipPath id="e">
      <circle cx="82" cy="209" r="5" fill="none"></circle>
    </clipPath>
    <clipPath id="g">
      <path fill="none" d="M64-115a16 16 0 0 0-16 16 16 16 0 0 0 16 16 16 16 0 0 0 16-16 16 16 0 0 0-16-16Zm0 24a8 8 0 0 1-8-8 8 8 0 0 1 8-8 8 8 0 0 1 8 8 8 8 0 0 1-8 8Z"></path>
    </clipPath>
    <clipPath id="h">
      <path fill="none" d="M84-63H44a16 16 0 0 1-16-16v-40a16 16 0 0 1 16-16h40a16 16 0 0 1 16 16v40a16 16 0 0 1-16 16Zm-40-64a8 8 0 0 0-8 8v40a8 8 0 0 0 8 8h40a8 8 0 0 0 8-8v-40a8 8 0 0 0-8-8Z"></path>
    </clipPath>
    <clipPath id="i">
      <circle cx="82" cy="-117" r="5" fill="none"></circle>
    </clipPath>
    <radialGradient id="a" cx="27.5" cy="121.5" r="137.5" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#ffd676"></stop>
      <stop offset=".25" stop-color="#f2a454"></stop>
      <stop offset=".38" stop-color="#f05c3c"></stop>
      <stop offset=".7" stop-color="#c22f86"></stop>
      <stop offset=".96" stop-color="#6666ad"></stop>
      <stop offset=".99" stop-color="#5c6cb2"></stop>
    </radialGradient>
    <radialGradient xlink:href="#a" id="d" cx="27.5" cy="-41.5" r="148.5"></radialGradient>
    <radialGradient xlink:href="#a" id="f" cx="13.87" cy="303.38" r="185.63"></radialGradient>
    <radialGradient xlink:href="#a" id="j" cx="13.87" cy="-22.62" r="185.63"></radialGradient>
  </defs>
  <g clip-path="url(#b)">
    <circle cx="27.5" cy="121.5" r="137.5" fill="url(#a)"></circle>
  </g>
  <g clip-path="url(#c)">
    <circle cx="27.5" cy="-41.5" r="148.5" fill="url(#d)"></circle>
  </g>
  <g clip-path="url(#e)">
    <circle cx="13.87" cy="303.38" r="185.63" fill="url(#f)"></circle>
  </g>
  <g clip-path="url(#g)">
    <circle cx="27.5" cy="-41.5" r="148.5" fill="url(#d)"></circle>
  </g>
  <g clip-path="url(#h)">
    <circle cx="27.5" cy="-41.5" r="148.5" fill="url(#d)"></circle>
  </g>
  <g clip-path="url(#i)">
    <circle cx="13.87" cy="-22.62" r="185.63" fill="url(#j)"></circle>
  </g>
  <circle cx="82" cy="46" r="5" fill="#fff"></circle>
  <path fill="#fff" d="M64 48a16 16 0 1 0 16 16 16 16 0 0 0-16-16Zm0 24a8 8 0 1 1 8-8 8 8 0 0 1-8 8Z"></path>
  <rect width="64" height="64" x="32" y="32" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="8" rx="12" ry="12"></rect>
</svg>
