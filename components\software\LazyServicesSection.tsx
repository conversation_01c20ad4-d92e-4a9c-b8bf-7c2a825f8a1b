'use client';

import React, { Suspense } from 'react';
import { softwareServices } from "@/components/data/software";
import { SoftwareNFTSection } from "@/components/software-nft-section";
import ErrorBoundary from '@/components/ui/ErrorBoundary';
import { SkeletonLoader } from '@/components/ui/LoadingSpinner';

export default function LazyServicesSection() {
  return (
    <div>
      {/* Enhanced Responsive Header Section */}
      <section className="py-8 xs:py-10 sm:py-12 md:py-16 px-2 xs:px-3 sm:px-4 text-center">
        <div className="container-responsive">
          <h1 className="text-responsive-2xl font-bold mb-4 xs:mb-5 sm:mb-6 bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent">
            <span className="hidden sm:inline">Your Partner in</span>
            <span className="sm:hidden">Partner in</span>
          </h1>
          <h2 className="text-responsive-xl font-bold mb-6 xs:mb-7 sm:mb-8 bg-gradient-to-r from-purple-600 via-blue-600 to-cyan-600 bg-clip-text text-transparent">
            Digital Transformation
          </h2>
          <p className="text-responsive-base text-gray-600 dark:text-gray-300 max-w-xs xs:max-w-sm sm:max-w-md md:max-w-lg lg:max-w-3xl mx-auto leading-relaxed">
            <span className="hidden sm:inline">Driving Growth with Blockchain, AI, Crypto, and More.</span>
            <span className="sm:hidden">Growth with Blockchain, AI & Crypto.</span>
          </p>
        </div>
      </section>

      {/* Services Sections */}
      {softwareServices.map((service, index) => (
        <ErrorBoundary key={index}>
          <Suspense fallback={<SkeletonLoader className="py-16 px-4" />}>
            <section>
              <SoftwareNFTSection {...service} />
            </section>
          </Suspense>
        </ErrorBoundary>
      ))}
    </div>
  );
}
