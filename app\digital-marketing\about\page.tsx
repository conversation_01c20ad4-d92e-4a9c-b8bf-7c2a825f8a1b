"use client";

import { MarketingNavigation } from "@/components/marketing-navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Award, Users, Target, TrendingUp, ArrowRight, CheckCircle,
  Lightbulb, Shield, Clock, Globe, Heart, Zap, Star,
  Calendar, Phone, Mail, MapPin
} from "lucide-react";

export default function AboutPage() {
  const stats = [
    { number: "8+", label: "Years Experience", icon: Calendar },
    { number: "500+", label: "Clients Served", icon: Users },
    { number: "50+", label: "Team Members", icon: Award },
    { number: "99%", label: "Client Satisfaction", icon: Star }
  ];

  const values = [
    {
      icon: Target,
      title: "Results-Driven",
      description: "We focus on delivering measurable results that directly impact your business growth and success."
    },
    {
      icon: Shield,
      title: "Transparency",
      description: "Complete transparency in our processes, reporting, and communication with all our clients."
    },
    {
      icon: Lightbulb,
      title: "Innovation",
      description: "We stay ahead of industry trends and continuously innovate our strategies and approaches."
    },
    {
      icon: Heart,
      title: "Client-Centric",
      description: "Your success is our success. We build long-term partnerships based on trust and results."
    }
  ];

  const team = [
    {
      name: "<PERSON> <PERSON>",
      position: "CEO & Founder",
      experience: "12+ years",
      specialty: "Digital Strategy",
      image: "/api/placeholder/300/300"
    },
    {
      name: "Michael Chen",
      position: "Head of SEO",
      experience: "8+ years", 
      specialty: "Technical SEO",
      image: "/api/placeholder/300/300"
    },
    {
      name: "Emily Rodriguez",
      position: "PPC Director",
      experience: "10+ years",
      specialty: "Paid Advertising",
      image: "/api/placeholder/300/300"
    },
    {
      name: "David Thompson",
      position: "Creative Director",
      experience: "9+ years",
      specialty: "Content & Design",
      image: "/api/placeholder/300/300"
    }
  ];

  return (
    <main className="min-h-screen">
      <MarketingNavigation />
      
      {/* Hero Section */}
      <section className="relative pt-24 pb-20 px-4 sm:px-6 lg:px-8 overflow-hidden">
        {/* 3D Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900/10 via-purple-900/5 to-blue-900/10"></div>
        <div className="absolute top-20 left-10 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-float delay-1000"></div>
        
        <div className="container mx-auto relative z-10">
          <div className="text-center max-w-4xl mx-auto mb-16">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-blue-500/10 border border-blue-500/20 mb-6 animate-fade-in-up">
              <Lightbulb className="h-4 w-4 text-blue-500 mr-2" />
              <span className="text-sm font-medium text-blue-500">About Our Agency</span>
            </div>
            
            <h1 className="text-5xl md:text-7xl font-bold mb-6 hero-title leading-tight animate-fade-in-up">
              Driving Digital Success
              <span className="block bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent">
                Since 2016
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed animate-fade-in-up">
              We're a passionate team of digital marketing experts dedicated to helping businesses grow through innovative, data-driven strategies that deliver real results.
            </p>
          </div>
          
          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <div 
                key={index} 
                className="text-center p-6 service-card rounded-xl hover:scale-105 transition-all duration-300 animate-fade-in-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="inline-flex items-center justify-center w-12 h-12 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 mb-3">
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
                <div className="text-2xl md:text-3xl font-bold text-blue-500 mb-1 animate-stats-bounce">{stat.number}</div>
                <div className="text-sm text-muted-foreground">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <h2 className="text-4xl md:text-5xl font-bold mb-6 hero-title animate-fade-in-up">
                Our Story
              </h2>
              <p className="text-xl text-muted-foreground mb-6 leading-relaxed animate-fade-in-up">
                Founded in 2016 with a simple mission: to help businesses thrive in the digital age. What started as a small team of passionate marketers has grown into a full-service digital marketing agency serving clients worldwide.
              </p>
              <p className="text-lg text-muted-foreground mb-8 leading-relaxed animate-fade-in-up">
                We believe that every business deserves to succeed online, regardless of size or industry. That's why we combine cutting-edge technology with human creativity to deliver personalized strategies that drive real growth.
              </p>
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white group animate-fade-in-up">
                Join Our Journey
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Button>
            </div>
            
            <div className="grid grid-cols-2 gap-6">
              {values.map((value, index) => (
                <Card 
                  key={index} 
                  className="service-card hover:scale-105 transition-all duration-300 group animate-fade-in-up"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <CardContent className="p-6">
                    <div className="inline-flex items-center justify-center w-12 h-12 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 mb-4 group-hover:shadow-xl transition-all duration-300">
                      <value.icon className="h-6 w-6 text-white" />
                    </div>
                    <h3 className="text-lg font-semibold mb-3 group-hover:text-blue-500 transition-colors">
                      {value.title}
                    </h3>
                    <p className="text-muted-foreground text-sm leading-relaxed">
                      {value.description}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-muted/20">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 hero-title animate-fade-in-up">
              Meet Our Expert Team
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto animate-fade-in-up">
              Our diverse team of digital marketing specialists brings together years of experience and expertise to drive your success.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <Card 
                key={index} 
                className="service-card hover:scale-105 transition-all duration-300 group animate-fade-in-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <CardContent className="p-6 text-center">
                  <div className="w-24 h-24 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 mx-auto mb-4 flex items-center justify-center group-hover:shadow-xl transition-all duration-300">
                    <span className="text-white font-bold text-2xl">
                      {member.name.charAt(0)}
                    </span>
                  </div>
                  <h3 className="text-xl font-bold mb-2 group-hover:text-blue-500 transition-colors">
                    {member.name}
                  </h3>
                  <p className="text-blue-500 font-semibold mb-2">{member.position}</p>
                  <div className="space-y-1 text-sm text-muted-foreground">
                    <p>{member.experience}</p>
                    <p>{member.specialty}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600 via-blue-700 to-purple-700"></div>
        <div className="absolute top-10 left-10 w-72 h-72 bg-white/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-10 right-10 w-96 h-96 bg-white/3 rounded-full blur-3xl animate-pulse delay-1000"></div>
        
        <div className="container mx-auto text-center relative z-10">
          <div className="max-w-4xl mx-auto text-white">
            <h2 className="text-4xl md:text-6xl font-bold mb-6 leading-tight animate-fade-in-up">
              Ready to Work
              <span className="block bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">
                Together?
              </span>
            </h2>
            
            <p className="text-xl md:text-2xl mb-8 opacity-90 leading-relaxed animate-fade-in-up">
              Let's discuss how we can help your business achieve its digital marketing goals.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-6 justify-center mb-8 animate-fade-in-up">
              <Button 
                size="lg" 
                className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 text-lg font-semibold group"
              >
                <Calendar className="mr-2 h-5 w-5" />
                Schedule a Meeting
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button 
                size="lg" 
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4 text-lg font-semibold"
              >
                <Phone className="mr-2 h-5 w-5" />
                Call Us Today
              </Button>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}
