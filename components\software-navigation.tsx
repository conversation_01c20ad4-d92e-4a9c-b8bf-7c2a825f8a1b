"use client";

import * as React from "react";
import Link from "next/link";
import { useTheme } from "next-themes";
import { Moon, Sun, Menu, X, Home, Info, Phone, HelpCircle, Layers } from "lucide-react";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import { usePathname } from "next/navigation";

const navItems = [
  { href: "#home", label: "Home", icon: <Home className="mr-2 h-5 w-5" /> },
  { href: "#services", label: "Services", icon: <Layers className="mr-2 h-5 w-5" /> },
  { href: "#about", label: "About", icon: <Info className="mr-2 h-5 w-5" /> },
  { href: "#contact", label: "Contact", icon: <Phone className="mr-2 h-5 w-5" /> },
  { href: "#faq", label: "FAQ", icon: <HelpCircle className="mr-2 h-5 w-5" /> },
];

function SoftwareNavigation() {
  const { theme, setTheme } = useTheme();
  const [isMenuOpen, setIsMenuOpen] = React.useState(false);
  const pathname = usePathname();

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  return (
    <nav className="fixed top-0 w-full z-50 bg-background/95 backdrop-blur-sm shadow-lg border-b border-border/20 transition-all duration-300">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-20">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-3">
            <Image
              src="https://brtmultisoftware.com/software/assets/images/brt-logo.png"
              alt="BRT Logo"
              width={64}
              height={64}
              className="h-16 w-auto"
            />
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-2">
            {navItems.map((item) => (
              <a
                key={item.href}
                href={item.href}
                className={`flex items-center px-3 py-2 rounded-md font-medium transition-colors duration-200 text-muted-foreground hover:text-foreground/90 ${
                  pathname === item.href ? 'bg-muted text-foreground/90' : ''
                }`}
              >
                {item.icon}
                {item.label}
              </a>
            ))}
            <a
              href="#all-services"
              className="flex items-center px-3 py-2 rounded-md font-medium border border-transparent text-primary bg-primary/10 hover:bg-primary/20 transition-colors duration-200"
            >
              <Layers className="mr-2 h-5 w-5" />
              All Services
            </a>
          </div>

          {/* Theme Toggle & Mobile Menu */}
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="icon"
              onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
              className="relative transition-all"
            >
              <Sun className="h-6 w-6 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
              <Moon className="absolute h-6 w-6 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
              <span className="sr-only">Toggle theme</span>
            </Button>

            {/* Mobile Menu Button */}
            <Button
              variant="outline"
              size="icon"
              className="md:hidden transition-all"
              onClick={toggleMenu}
            >
              {isMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden animate-fade-in-down">
            <div className="px-2 pt-2 pb-3 space-y-1 bg-background/95 backdrop-blur-lg rounded-xl mt-2 border border-border/20 shadow-xl">
              {navItems.map((item) => (
                <a
                  key={item.href}
                  href={item.href}
                  className={`flex items-center px-3 py-2 rounded-md text-base font-medium text-muted-foreground hover:text-foreground hover:bg-muted ${
                    pathname === item.href ? 'bg-muted text-foreground' : ''
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.icon}
                  {item.label}
                </a>
              ))}
              <a
                href="#all-services"
                className="flex items-center px-3 py-2 rounded-md text-base font-medium border border-transparent text-primary bg-primary/10 hover:bg-primary/20 transition-colors duration-200 mt-1"
                onClick={() => setIsMenuOpen(false)}
              >
                <Layers className="mr-2 h-5 w-5" />
                All Services
              </a>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}

export default SoftwareNavigation;