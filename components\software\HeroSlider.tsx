'use client';
import React, { useRef, useState, useEffect } from 'react';
import Image from 'next/image';
import { EffectFade, Autoplay } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import 'swiper/css/effect-fade';

// Text slides - Optimized for performance
const textSlides = [
  'Blockchain',
  'Crypto Exchange',
  'NFT Development',
  'AI Solutions',
  'Game Development',
  'Web3 & DeFi',
  'Smart Contracts',
  'Custom Software',
];

// Image slides - Optimized for performance
const imageSlides = [
  { src: '/assets/images/block chain development.png', alt: 'Blockchain Development' },
  { src: '/assets/images/crypto_exchange.png', alt: 'Crypto Exchange' },
  { src: '/assets/images/NFT.png', alt: 'NFT Development' },
  { src: '/assets/images/blockchainsmart.png', alt: 'AI Solutions' },
  { src: '/assets/images/mlm gentation.png', alt: 'Game Development' },
  { src: '/assets/images/WEB3_mlm.png', alt: 'Web3 & DeFi' },
  { src: '/assets/images/blockchainsmart.png', alt: 'Smart Contracts' },
  { src: '/assets/images/custom__crm.png', alt: 'Custom Software' },
];

// Descriptions - Optimized
const descriptions = [
  'Development',
  'Exchange Development',
  'NFT Development',
  'AI Solutions',
  'Game Development',
  'Web3 & DeFi Solutions',
  'Smart Contract Development',
  'Custom Software Development',
];

const descriptions2 = [
  'BRT Multi Software specializes in blockchain development with cutting-edge technology and customized blockchain services.',
  'Launch a robust cryptocurrency exchange with BRT Multi Software featuring advanced security and trading capabilities.',
  'Create innovative NFT marketplaces with trading, minting, and auction capabilities for digital assets.',
  'Harness the power of Artificial Intelligence with machine learning, chatbots, and data analytics solutions.',
  'Transform gaming concepts into immersive digital experiences with Unity, Unreal Engine, and custom game development.',
  'Build decentralized applications and DeFi platforms with Web3 integration and blockchain technology.',
  'Develop secure and efficient smart contracts for automation, DApps, and blockchain integration.',
  'Custom software solutions tailored to your business needs with modern technologies and best practices.',
];

const HeroSlider: React.FC = () => {
  const [activeIndex, setActiveIndex] = useState(0);
  const textSwiperRef = useRef<any>(null);
  const imageSwiperRef = useRef<any>(null);
  const [isChanging, setIsChanging] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovered, setIsHovered] = useState(false);
  const [maxReachedIndex, setMaxReachedIndex] = useState(0); // Track highest index reached

  // Mouse tracking for interactive effects
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Enhanced timer with faster transitions
  useEffect(() => {
    const interval = setInterval(() => {
      if (!isChanging && !isHovered) {
        setIsChanging(true);

        const nextIndex = (activeIndex + 1) % textSlides.length;

        // Faster animations
        setTimeout(() => {
          setActiveIndex(nextIndex);
          // Update max reached index to keep track of highest index visited
          setMaxReachedIndex(prev => Math.max(prev, nextIndex));

          if (textSwiperRef.current && textSwiperRef.current.swiper) {
            textSwiperRef.current.swiper.slideTo(nextIndex);
          }
          if (imageSwiperRef.current && imageSwiperRef.current.swiper) {
            imageSwiperRef.current.swiper.slideTo(nextIndex);
          }

          setTimeout(() => {
            setIsChanging(false);
          }, 600);
        }, 100);
      }
    }, 2500);

    return () => clearInterval(interval);
  }, [activeIndex, isChanging, isHovered]);

  const handleSlideChange = () => {
    // Disable automatic slide change handling to prevent conflicts
    // Manual timer will handle all changes
  };

  return (
    <section
      className="relative w-full overflow-hidden pt-12 pb-4 sm:pt-16 sm:py-6 md:py-8 lg:py-10 xl:py-12 text-foreground min-h-[75vh] sm:min-h-[80vh] md:min-h-[85vh] lg:min-h-[90vh] flex items-center"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >

      {/* Interactive mouse follower */}
      <div
        className="absolute w-96 h-96 bg-blue-400/15 rounded-full blur-3xl transition-all duration-300 ease-out pointer-events-none"
        style={{
          left: `${mousePosition.x - 192}px`,
          top: `${mousePosition.y - 192}px`,
          opacity: isHovered ? 0.6 : 0.2,
          transform: `scale(${isHovered ? 1.1 : 0.8})`,
        }}
      ></div>

      {/* Simplified background elements */}
      <div className="absolute top-1/6 left-1/6 w-40 sm:w-56 md:w-72 lg:w-80 h-40 sm:h-56 md:h-72 lg:h-80 bg-blue-500/8 rounded-full blur-3xl"></div>
      <div className="absolute bottom-1/6 right-1/6 w-48 sm:w-64 md:w-80 lg:w-96 h-48 sm:h-64 md:h-80 lg:h-96 bg-blue-600/6 rounded-full blur-3xl"></div>
      <div className="absolute top-1/3 right-1/3 w-32 sm:w-48 md:w-64 lg:w-72 h-32 sm:h-48 md:h-64 lg:h-72 bg-blue-400/5 rounded-full blur-3xl"></div>

      {/* Simple floating particles */}
      <div className="absolute top-20 left-20 w-2 h-2 bg-blue-500/40 rounded-full"></div>
      <div className="absolute top-40 right-32 w-1.5 h-1.5 bg-blue-600/40 rounded-full"></div>
      <div className="absolute bottom-32 left-40 w-2 h-2 bg-blue-500/40 rounded-full"></div>
      <div className="absolute top-60 left-1/2 w-1 h-1 bg-blue-400/40 rounded-full"></div>
      <div className="absolute bottom-40 right-20 w-1.5 h-1.5 bg-blue-600/40 rounded-full"></div>

      {/* Simple geometric shapes */}
      <div className="absolute top-1/4 right-1/4 w-8 h-8 border-2 border-blue-400/20"></div>
      <div className="absolute bottom-1/4 left-1/4 w-6 h-6 border-2 border-blue-500/20"></div>

      <div className="container mx-auto flex flex-col md:flex-row items-center gap-6 sm:gap-8 md:gap-10 lg:gap-12 xl:gap-16 px-3 sm:px-4 md:px-6 lg:px-8 relative z-10">
        <div className="flex-1 w-full max-w-xs sm:max-w-lg md:max-w-xl lg:max-w-2xl space-y-3 sm:space-y-4 md:space-y-6 animate-fade-in-up text-center md:text-left">
          {/* Enhanced responsive main title with more dynamic effects */}
          <div className="relative group">
            <h1 className="text-lg xs:text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-extrabold tracking-tight text-gray-900 dark:text-white drop-shadow-lg leading-tight px-2 sm:px-0 transition-all duration-500 group-hover:scale-105">
              Digital Transformation Company
            </h1>
            <div className="absolute -inset-2 bg-blue-600/20 blur-xl opacity-30 animate-pulse group-hover:opacity-50 transition-opacity duration-500"></div>

            {/* Animated underline */}
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-0 h-1 bg-blue-600 group-hover:w-full transition-all duration-700 ease-out"></div>
          </div>

          {/* Enhanced responsive text slider with more dynamic styling */}
          <div className="hero-text-container relative overflow-hidden h-12 sm:h-14 md:h-16 lg:h-18 animate-fade-in-up bg-white/90 dark:bg-gray-800/90 rounded-2xl p-3 sm:p-4 md:p-5 backdrop-blur-md border border-gray-200 dark:border-gray-700 mx-2 sm:mx-0 group hover:shadow-xl hover:shadow-blue-500/20 transition-all duration-500" style={{ animationDelay: '0.2s' }}>
            <div className="absolute inset-0 bg-blue-50/50 dark:bg-blue-950/30 opacity-0 group-hover:opacity-100 transition-all duration-500"></div>

            {/* Animated border effect */}
            <div className="absolute inset-0 rounded-2xl bg-blue-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm"></div>

            <Swiper
              ref={textSwiperRef}
              effect="fade"
              loop
              spaceBetween={0}
              onSlideChange={handleSlideChange}
              autoplay={false}
              speed={800}
              fadeEffect={{
                crossFade: true
              }}
              slidesPerView={1}
              allowTouchMove={false}
              modules={[EffectFade]}
              className="text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl font-bold h-full relative z-10"
            >
              {textSlides.map((text, idx) => (
                <SwiperSlide key={idx}>
                  <div className="flex items-center justify-center h-full w-full absolute inset-0">
                    <span className="block text-blue-600 dark:text-blue-400 drop-shadow-lg font-extrabold tracking-wide text-center px-1 transform transition-transform duration-300 hover:scale-105">
                      {text}
                    </span>
                  </div>
                </SwiperSlide>
              ))}
            </Swiper>
          </div>

          {/* Enhanced description section with sliding images */}
          <div className="animate-fade-in-up bg-white/90 dark:bg-gray-900/90 backdrop-blur-md rounded-3xl p-6 sm:p-7 border border-gray-200 dark:border-gray-700 shadow-xl hover:shadow-2xl hover:shadow-blue-500/10 min-h-[130px] flex flex-col justify-center group transition-all duration-500 relative overflow-hidden" style={{ animationDelay: '0.4s' }}>

            {/* Sliding images in background */}
            <div className="absolute inset-0 opacity-20 overflow-hidden">
              <div className="relative w-full h-full">
                {imageSlides.slice(0, maxReachedIndex + 1).map((img, idx) => (
                  <div
                    key={`text-bg-${idx}`}
                    className={`absolute transition-all duration-1000 ease-in-out ${
                      idx === activeIndex ? 'opacity-30 scale-110' : 'opacity-10 scale-100'
                    }`}
                    style={{
                      left: `${(idx * 25) % 100}%`,
                      top: `${(idx * 30) % 70}%`,
                      transform: `translate(-50%, -50%) rotate(${idx * 15}deg)`,
                      animation: `slideInText-${idx} 2s ease-in-out infinite`,
                      animationDelay: `${idx * 0.3}s`
                    }}
                  >
                    <Image
                      src={img.src}
                      alt={img.alt}
                      width={60}
                      height={60}
                      className="object-contain filter blur-sm"
                      style={{ background: 'transparent' }}
                    />
                  </div>
                ))}
              </div>
            </div>

            <div className="relative z-10">
              {/* Animated background glow */}
              <div className="absolute -inset-2 bg-blue-600/10 blur-xl opacity-40 animate-pulse group-hover:opacity-60 transition-opacity duration-500"></div>

              <div className={`transition-all duration-500 relative z-10 ${isChanging ? 'opacity-0 transform translate-y-3 scale-95' : 'opacity-100 transform translate-y-0 scale-100'}`}>

                {/* Title with sliding images */}
                <div className="relative mb-3">
                  <span className="text-lg sm:text-xl font-bold text-blue-700 dark:text-blue-300 block tracking-wide group-hover:scale-105 transition-transform duration-300">
                    {descriptions[activeIndex]}
                  </span>

                  {/* Small sliding images next to title */}
                  <div className="absolute -right-2 top-0 flex space-x-1">
                    {imageSlides.slice(Math.max(0, activeIndex - 1), activeIndex + 2).map((img, idx) => (
                      <div
                        key={`title-img-${idx}`}
                        className={`w-6 h-6 rounded-full overflow-hidden transition-all duration-500 ${
                          idx === 1 ? 'scale-125 opacity-100' : 'scale-75 opacity-60'
                        }`}
                        style={{
                          animationDelay: `${idx * 0.1}s`
                        }}
                      >
                        <Image
                          src={img.src}
                          alt={img.alt}
                          width={24}
                          height={24}
                          className="object-contain w-full h-full"
                          style={{ background: 'transparent' }}
                        />
                      </div>
                    ))}
                  </div>
                </div>

                <p className="text-sm sm:text-base text-gray-700 dark:text-gray-300 leading-relaxed font-medium group-hover:text-gray-600 dark:group-hover:text-gray-200 transition-colors duration-300">
                  {descriptions2[activeIndex]}
                </p>
              </div>

              {/* Corner accent with mini image */}
              <div className="absolute top-2 right-2 w-8 h-8 rounded-full overflow-hidden opacity-60 group-hover:opacity-100 transition-opacity duration-300 bg-blue-500/20 backdrop-blur-sm">
                <Image
                  src={imageSlides[activeIndex].src}
                  alt={imageSlides[activeIndex].alt}
                  width={32}
                  height={32}
                  className="object-contain w-full h-full"
                  style={{ background: 'transparent' }}
                />
              </div>
            </div>
          </div>

          {/* Enhanced button section with sliding images */}
          <div className="mt-5 flex flex-col sm:flex-row gap-4 md:gap-6 w-full max-w-xs sm:max-w-none animate-fade-in-up" style={{ animationDelay: '0.6s' }}>
            <button className="group relative w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 sm:px-8 sm:py-4 md:px-10 md:py-4 rounded-2xl font-bold shadow-xl hover:shadow-2xl hover:shadow-blue-500/30 transition-all duration-500 overflow-hidden transform hover:scale-105 hover:-translate-y-1 active:scale-95">

              {/* Sliding images in button background */}
              <div className="absolute inset-0 opacity-10 overflow-hidden rounded-2xl">
                {imageSlides.slice(0, Math.min(maxReachedIndex + 1, 3)).map((img, idx) => (
                  <div
                    key={`btn-img-${idx}`}
                    className="absolute transition-all duration-700 ease-in-out"
                    style={{
                      left: `${(idx * 40) % 80}%`,
                      top: `${(idx * 50) % 60}%`,
                      transform: `translate(-50%, -50%) rotate(${idx * 20}deg) scale(0.8)`,
                      animation: `slideInButton-${idx} 3s ease-in-out infinite`,
                      animationDelay: `${idx * 0.4}s`
                    }}
                  >
                    <Image
                      src={img.src}
                      alt={img.alt}
                      width={30}
                      height={30}
                      className="object-contain filter blur-[1px] group-hover:blur-none transition-all duration-300"
                      style={{ background: 'transparent' }}
                    />
                  </div>
                ))}
              </div>

              {/* Animated background layers */}
              <div className="absolute inset-0 bg-blue-500 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl"></div>
              <div className="absolute inset-0 bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl blur-sm"></div>

              {/* Ripple effect */}
              <div className="absolute inset-0 rounded-2xl bg-white/20 scale-0 group-hover:scale-100 transition-transform duration-500 ease-out"></div>

              <span className="relative z-10 flex items-center justify-center gap-2">
                <span className="group-hover:text-white transition-colors duration-300">Discuss Your Project</span>

                {/* Small image icon next to text */}
                <div className="w-5 h-5 rounded-full overflow-hidden opacity-70 group-hover:opacity-100 transition-opacity duration-300">
                  <Image
                    src={imageSlides[activeIndex].src}
                    alt={imageSlides[activeIndex].alt}
                    width={20}
                    height={20}
                    className="object-contain w-full h-full"
                    style={{ background: 'transparent' }}
                  />
                </div>

                <svg className="w-5 h-5 group-hover:translate-x-2 group-hover:scale-110 transition-all duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </span>

              {/* Glow effect */}
              <div className="absolute -inset-1 bg-blue-600/50 blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl"></div>
            </button>
          </div>
        </div>

        {/* Enhanced Dynamic Image Slider with Progressive Border Addition */}
        <div className="flex-1 flex justify-center items-center w-full animate-fade-in group" style={{ animationDelay: '0.8s' }}>
          <div className="relative w-full max-w-xs sm:max-w-md md:max-w-xl lg:max-w-2xl">
            {/* Circular border container */}
            <div className="relative w-80 h-80 sm:w-96 sm:h-96 md:w-[28rem] md:h-[28rem] lg:w-[32rem] lg:h-[32rem] mx-auto">
              {/* Main circular border */}
              <div className="absolute inset-0 border-2 border-blue-400/20 rounded-full pointer-events-none"></div>
              <div className="absolute inset-2 border border-blue-300/15 rounded-full pointer-events-none"></div>



              {/* Center active image display with border animations */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="relative w-32 h-32 sm:w-40 sm:h-40 md:w-48 md:h-48 lg:w-56 lg:h-56 flex items-center justify-center">

                  {/* Moving border images around main image */}
                  <div className="absolute inset-0 animate-spin" style={{ animationDuration: '25s' }}>
                    {imageSlides.slice(0, maxReachedIndex + 1).map((img, idx) => {
                      const totalVisible = maxReachedIndex + 1;
                      const angle = (idx * 360) / Math.max(totalVisible, 3);
                      const radius = 80; // Close to main image
                      const x = Math.cos((angle * Math.PI) / 180) * radius;
                      const y = Math.sin((angle * Math.PI) / 180) * radius;

                      return (
                        <div
                          key={`main-border-${idx}`}
                          className={`absolute w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 transition-all duration-500 ${
                            idx === activeIndex ? 'scale-125 z-30' : 'scale-100 z-20'
                          }`}
                          style={{
                            left: '50%',
                            top: '50%',
                            transform: `translate(-50%, -50%) translate(${x}px, ${y}px) rotate(-25s)`,
                            animation: `mainOrbit-${idx} 3s ease-in-out infinite`,
                            animationDelay: `${idx * 0.4}s`
                          }}
                        >
                          <div className={`w-full h-full rounded-full p-1 transition-all duration-300 backdrop-blur-sm ${
                            idx === activeIndex
                              ? 'bg-gradient-to-br from-blue-500/40 to-purple-500/40 shadow-lg shadow-blue-500/60 ring-1 ring-blue-400/50'
                              : 'bg-gradient-to-br from-white/25 to-gray-200/25 hover:from-blue-400/30 hover:to-purple-400/30 shadow-md'
                          }`}>
                            <div className="w-full h-full rounded-full overflow-hidden bg-white/15">
                              <Image
                                src={img.src}
                                alt={img.alt}
                                width={50}
                                height={50}
                                className="object-contain w-full h-full transition-all duration-300 filter drop-shadow-sm hover:drop-shadow-md"
                                style={{ background: 'transparent' }}
                              />
                            </div>
                          </div>

                          {/* Small glow for active */}
                          {idx === activeIndex && (
                            <div className="absolute inset-0 rounded-full bg-blue-400/30 blur-sm animate-pulse"></div>
                          )}
                        </div>
                      );
                    })}
                  </div>

                  {/* Main center image */}
                  <div className={`relative z-10 transition-all duration-700 ${isChanging ? 'opacity-0 scale-90 rotate-12' : 'opacity-100 scale-100 rotate-0'}`}>
                    <div className="relative">
                      <Image
                        src={imageSlides[activeIndex].src}
                        alt={imageSlides[activeIndex].alt}
                        width={300}
                        height={300}
                        className="object-contain w-full h-full transition-all duration-700 filter drop-shadow-2xl hover:scale-105"
                        style={{ background: 'transparent' }}
                      />
                      {/* Active image glow effect */}
                      <div className="absolute inset-0 bg-blue-500/10 rounded-full blur-xl opacity-60 animate-pulse"></div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Floating elements */}
              <div className="absolute -top-8 -left-8 w-32 h-32 bg-blue-500/8 rounded-full blur-2xl z-0 pointer-events-none animate-pulse" />
              <div className="absolute -bottom-8 -right-8 w-40 h-40 bg-blue-600/6 rounded-full blur-2xl z-0 pointer-events-none animate-pulse" style={{ animationDelay: '1s' }} />
            </div>
          </div>
        </div>

        {/* Custom CSS for sliding animations */}
        <style jsx>{`
          /* Text section sliding animations */
          @keyframes slideInText-0 {
            0% {
              transform: translate(-50%, -50%) translateX(-100px) rotate(0deg) scale(0.8);
              opacity: 0;
            }
            50% {
              transform: translate(-50%, -50%) translateX(0px) rotate(15deg) scale(1.1);
              opacity: 0.3;
            }
            100% {
              transform: translate(-50%, -50%) translateX(100px) rotate(30deg) scale(0.9);
              opacity: 0.1;
            }
          }
          @keyframes slideInText-1 {
            0% {
              transform: translate(-50%, -50%) translateY(-80px) rotate(0deg) scale(0.9);
              opacity: 0;
            }
            50% {
              transform: translate(-50%, -50%) translateY(0px) rotate(-10deg) scale(1.2);
              opacity: 0.25;
            }
            100% {
              transform: translate(-50%, -50%) translateY(80px) rotate(-20deg) scale(0.8);
              opacity: 0.05;
            }
          }
          @keyframes slideInText-2 {
            0% {
              transform: translate(-50%, -50%) translateX(120px) translateY(-60px) rotate(0deg) scale(0.7);
              opacity: 0;
            }
            50% {
              transform: translate(-50%, -50%) translateX(0px) translateY(0px) rotate(25deg) scale(1.15);
              opacity: 0.35;
            }
            100% {
              transform: translate(-50%, -50%) translateX(-120px) translateY(60px) rotate(50deg) scale(0.85);
              opacity: 0.1;
            }
          }
          @keyframes slideInText-3 {
            0% {
              transform: translate(-50%, -50%) translateY(100px) rotate(0deg) scale(0.6);
              opacity: 0;
            }
            50% {
              transform: translate(-50%, -50%) translateY(0px) rotate(-15deg) scale(1.3);
              opacity: 0.3;
            }
            100% {
              transform: translate(-50%, -50%) translateY(-100px) rotate(-30deg) scale(0.9);
              opacity: 0.08;
            }
          }

          /* Button section sliding animations */
          @keyframes slideInButton-0 {
            0% {
              transform: translate(-50%, -50%) translateX(-60px) rotate(0deg) scale(0.5);
              opacity: 0;
            }
            50% {
              transform: translate(-50%, -50%) translateX(0px) rotate(20deg) scale(1);
              opacity: 0.15;
            }
            100% {
              transform: translate(-50%, -50%) translateX(60px) rotate(40deg) scale(0.7);
              opacity: 0.05;
            }
          }
          @keyframes slideInButton-1 {
            0% {
              transform: translate(-50%, -50%) translateY(-40px) rotate(0deg) scale(0.6);
              opacity: 0;
            }
            50% {
              transform: translate(-50%, -50%) translateY(0px) rotate(-15deg) scale(1.1);
              opacity: 0.12;
            }
            100% {
              transform: translate(-50%, -50%) translateY(40px) rotate(-30deg) scale(0.8);
              opacity: 0.03;
            }
          }
          @keyframes slideInButton-2 {
            0% {
              transform: translate(-50%, -50%) translateX(50px) translateY(-30px) rotate(0deg) scale(0.4);
              opacity: 0;
            }
            50% {
              transform: translate(-50%, -50%) translateX(0px) translateY(0px) rotate(25deg) scale(0.9);
              opacity: 0.18;
            }
            100% {
              transform: translate(-50%, -50%) translateX(-50px) translateY(30px) rotate(50deg) scale(0.6);
              opacity: 0.06;
            }
          }
          @keyframes mainOrbit-0 {
            0%, 100% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1) rotate(0deg);
              filter: brightness(1.1) saturate(1.2);
            }
            50% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1.15) rotate(180deg);
              filter: brightness(1.3) saturate(1.4);
            }
          }
          @keyframes mainOrbit-1 {
            0%, 100% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1) translateY(0px);
              filter: hue-rotate(0deg) brightness(1);
            }
            50% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1.2) translateY(-3px);
              filter: hue-rotate(45deg) brightness(1.2);
            }
          }
          @keyframes mainOrbit-2 {
            0%, 100% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1) rotate(0deg);
              box-shadow: 0 0 0 rgba(59, 130, 246, 0);
            }
            50% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1.1) rotate(180deg);
              box-shadow: 0 0 15px rgba(59, 130, 246, 0.5);
            }
          }
          @keyframes mainOrbit-3 {
            0%, 100% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1) translateX(0px);
              filter: contrast(1) saturate(1);
            }
            50% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1.18) translateX(2px);
              filter: contrast(1.2) saturate(1.3);
            }
          }
          @keyframes mainOrbit-4 {
            0%, 100% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1) rotate(0deg);
              opacity: 1;
            }
            50% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1.12) rotate(180deg);
              opacity: 0.95;
            }
          }
          @keyframes mainOrbit-5 {
            0%, 100% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1) translateY(0px);
              filter: saturate(1.1) hue-rotate(0deg);
            }
            50% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1.14) translateY(-2px);
              filter: saturate(1.4) hue-rotate(30deg);
            }
          }
          @keyframes mainOrbit-6 {
            0%, 100% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1) rotate(0deg);
              filter: brightness(1) blur(0px);
            }
            50% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1.16) rotate(180deg);
              filter: brightness(1.25) blur(0.3px);
            }
          }
          @keyframes mainOrbit-7 {
            0%, 100% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1) translateX(0px) translateY(0px);
              filter: sepia(0) hue-rotate(0deg);
            }
            50% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1.13) translateX(1px) translateY(-1px);
              filter: sepia(0.15) hue-rotate(60deg);
            }
          }
          @keyframes orbit-0 {
            0%, 100% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1) rotate(0deg);
              filter: brightness(1) saturate(1);
            }
            25% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1.1) rotate(90deg);
              filter: brightness(1.2) saturate(1.3);
            }
            50% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1.05) rotate(180deg);
              filter: brightness(1.1) saturate(1.2);
            }
            75% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1.1) rotate(270deg);
              filter: brightness(1.2) saturate(1.3);
            }
          }
          @keyframes orbit-1 {
            0%, 100% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1) rotate(0deg) translateY(0px);
              filter: hue-rotate(0deg);
            }
            33% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1.15) rotate(120deg) translateY(-5px);
              filter: hue-rotate(60deg);
            }
            66% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1.08) rotate(240deg) translateY(3px);
              filter: hue-rotate(120deg);
            }
          }
          @keyframes orbit-2 {
            0%, 100% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1) rotate(0deg);
              box-shadow: 0 0 0 rgba(59, 130, 246, 0);
            }
            50% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1.12) rotate(180deg);
              box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
            }
          }
          @keyframes orbit-3 {
            0%, 100% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1) rotate(0deg) translateX(0px);
              filter: brightness(1) contrast(1);
            }
            25% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1.08) rotate(90deg) translateX(3px);
              filter: brightness(1.15) contrast(1.1);
            }
            75% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1.08) rotate(270deg) translateX(-3px);
              filter: brightness(1.15) contrast(1.1);
            }
          }
          @keyframes orbit-4 {
            0%, 100% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1) rotate(0deg);
              opacity: 1;
            }
            50% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1.2) rotate(180deg);
              opacity: 0.9;
            }
          }
          @keyframes orbit-5 {
            0%, 100% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1) rotate(0deg) translateY(0px);
              filter: saturate(1) hue-rotate(0deg);
            }
            40% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1.1) rotate(144deg) translateY(-4px);
              filter: saturate(1.3) hue-rotate(30deg);
            }
            80% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1.05) rotate(288deg) translateY(2px);
              filter: saturate(1.2) hue-rotate(60deg);
            }
          }
          @keyframes orbit-6 {
            0%, 100% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1) rotate(0deg);
              filter: blur(0px) brightness(1);
            }
            50% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1.15) rotate(180deg);
              filter: blur(0.5px) brightness(1.2);
            }
          }
          @keyframes orbit-7 {
            0%, 100% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1) rotate(0deg) translateX(0px) translateY(0px);
              filter: sepia(0) hue-rotate(0deg);
            }
            33% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1.12) rotate(120deg) translateX(2px) translateY(-3px);
              filter: sepia(0.2) hue-rotate(45deg);
            }
            66% {
              transform: translate(-50%, -50%) translate(var(--x), var(--y)) scale(1.06) rotate(240deg) translateX(-2px) translateY(3px);
              filter: sepia(0.1) hue-rotate(90deg);
            }
          }
        `}</style>
      </div>
    </section>
  );
};

export default HeroSlider;
