"use client";

import * as React from "react";
import Link from "next/link";
import { useTheme } from "next-themes";
import { Moon, Sun, Menu, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import { usePathname } from "next/navigation";

export function MarketingNavigation() {
  const { theme, setTheme } = useTheme();
  const [isMenuOpen, setIsMenuOpen] = React.useState(false);
  const pathname = usePathname();

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  const navItems = [
    { href: "/digital-marketing", label: "Home" },
    { href: "/digital-marketing/services", label: "Services" },
    { href: "/digital-marketing/about", label: "About" },
    { href: "/digital-marketing/contact", label: "Contact" },
    { href: "/digital-marketing/faq", label: "FAQ" },
  ];

  return (
    <nav className="fixed top-0 w-full z-50 bg-blue-900/90 backdrop-blur-md border-b border-blue-500/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <Image
              src="https://brtmultisoftware.com/img/BRTLOGO.png"
              alt="BRT Logo"
              width={40}
              height={40}
              className="h-10 w-auto"
            />
            <span className="text-xl font-bold text-blue-300">BRT Marketing</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className={`text-blue-200 hover:text-blue-100 transition-colors duration-200 hover:scale-105 ${
                  pathname === item.href ? 'font-semibold' : ''
                }`}
              >
                {item.label}
              </Link>
            ))}
            <Link
              href="/"
              className="text-blue-200 hover:text-blue-100 transition-colors duration-200 hover:scale-105 border border-blue-500/30 px-3 py-1 rounded-md"
            >
              All Services
            </Link>
          </div>

          {/* Theme Toggle & Mobile Menu */}
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
              className="hover:bg-blue-800/50 text-blue-200 transition-colors"
            >
              <Sun className="h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
              <Moon className="absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
              <span className="sr-only">Toggle theme</span>
            </Button>

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden text-blue-200 hover:bg-blue-800/50"
              onClick={toggleMenu}
            >
              {isMenuOpen ? (
                <X className="h-5 w-5" />
              ) : (
                <Menu className="h-5 w-5" />
              )}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-blue-800/80 backdrop-blur-md rounded-lg mt-2 border border-blue-500/30">
              {navItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`block px-3 py-2 text-blue-200 hover:text-blue-100 hover:bg-blue-700/50 rounded-md transition-colors ${
                    pathname === item.href ? 'font-semibold' : ''
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.label}
                </Link>
              ))}
              <Link
                href="/"
                className="block px-3 py-2 text-blue-200 hover:text-blue-100 hover:bg-blue-700/50 rounded-md transition-colors border-t border-blue-500/30 mt-2 pt-3"
                onClick={() => setIsMenuOpen(false)}
              >
                All Services
              </Link>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}