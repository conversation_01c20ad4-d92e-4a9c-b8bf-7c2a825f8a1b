"use client";

import * as React from "react";
import Link from "next/link";
import { useTheme } from "next-themes";
import { Moon, Sun, Menu, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import { usePathname } from "next/navigation";

interface NavigationProps {
  serviceType: "gaming" | "graphics" | "marketing" | "software";
  brandName: string;
  brandColor: string;
}

export function SharedNavigation({ serviceType, brandName, brandColor }: NavigationProps) {
  const { theme, setTheme } = useTheme();
  const [isMenuOpen, setIsMenuOpen] = React.useState(false);
  const pathname = usePathname();

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  const getServicePath = (page: string) => {
    const basePaths = {
      gaming: "/gaming-software",
      graphics: "/graphics-design", 
      marketing: "/digital-marketing",
      software: "/software-development"
    };
    return `${basePaths[serviceType]}${page === "home" ? "" : `/${page}`}`;
  };

  const navItems = [
    { href: getServicePath("home"), label: "Home" },
    { href: getServicePath("services"), label: "Services" },
    { href: getServicePath("about"), label: "About" },
    { href: getServicePath("contact"), label: "Contact" },
    { href: getServicePath("faq"), label: "FAQ" },
  ];

  const colorClasses = {
    gaming: {
      bg: "bg-slate-900/90",
      border: "border-sky-500/30",
      text: "text-sky-200",
      textHover: "hover:text-sky-100",
      brandText: "text-sky-300",
      hoverBg: "hover:bg-slate-800/50",
      mobileBg: "bg-slate-800/80",
      mobileBorder: "border-sky-500/30",
      mobileHover: "hover:bg-slate-700/50"
    },
    graphics: {
      bg: "bg-slate-900/90",
      border: "border-purple-500/30",
      text: "text-purple-200",
      textHover: "hover:text-purple-100",
      brandText: "text-purple-300",
      hoverBg: "hover:bg-slate-800/50",
      mobileBg: "bg-slate-800/80",
      mobileBorder: "border-purple-500/30",
      mobileHover: "hover:bg-slate-700/50"
    },
    marketing: {
      bg: "bg-slate-900/90",
      border: "border-emerald-500/30",
      text: "text-emerald-200",
      textHover: "hover:text-emerald-100",
      brandText: "text-emerald-300",
      hoverBg: "hover:bg-slate-800/50",
      mobileBg: "bg-slate-800/80",
      mobileBorder: "border-emerald-500/30",
      mobileHover: "hover:bg-slate-700/50"
    },
    software: {
      bg: "bg-background/90",
      border: "border-border",
      text: "text-foreground",
      textHover: "hover:text-primary",
      brandText: "text-primary",
      hoverBg: "hover:bg-accent/50",
      mobileBg: "bg-background/95",
      mobileBorder: "border-border",
      mobileHover: "hover:bg-accent/50"
    }
  };

  const colors = colorClasses[serviceType];

  return (
    <nav className={`fixed top-0 w-full z-50 ${colors.bg} backdrop-blur-md border-b ${colors.border}`}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <Image
              src="https://brtmultisoftware.com/img/BRTLOGO.png"
              alt="BRT Logo"
              width={40}
              height={40}
              className="h-10 w-auto"
            />
            <span className={`text-xl font-bold ${colors.brandText}`}>{brandName}</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className={`${colors.text} ${colors.textHover} transition-colors duration-200 hover:scale-105 ${
                  pathname === item.href ? 'font-semibold' : ''
                }`}
              >
                {item.label}
              </Link>
            ))}
            <Link
              href="/"
              className={`${colors.text} ${colors.textHover} transition-colors duration-200 hover:scale-105 border ${colors.border} px-3 py-1 rounded-md`}
            >
              All Services
            </Link>
          </div>

          {/* Theme Toggle & Mobile Menu */}
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
              className={`${colors.hoverBg} ${colors.text} transition-colors`}
            >
              <Sun className="h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
              <Moon className="absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
              <span className="sr-only">Toggle theme</span>
            </Button>

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="icon"
              className={`md:hidden ${colors.text} ${colors.hoverBg}`}
              onClick={toggleMenu}
            >
              {isMenuOpen ? (
                <X className="h-5 w-5" />
              ) : (
                <Menu className="h-5 w-5" />
              )}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className={`px-2 pt-2 pb-3 space-y-1 sm:px-3 ${colors.mobileBg} backdrop-blur-md rounded-lg mt-2 border ${colors.mobileBorder}`}>
              {navItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`block px-3 py-2 ${colors.text} ${colors.textHover} ${colors.mobileHover} rounded-md transition-colors ${
                    pathname === item.href ? 'font-semibold' : ''
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.label}
                </Link>
              ))}
              <Link
                href="/"
                className={`block px-3 py-2 ${colors.text} ${colors.textHover} ${colors.mobileHover} rounded-md transition-colors border-t ${colors.border} mt-2 pt-3`}
                onClick={() => setIsMenuOpen(false)}
              >
                All Services
              </Link>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}