'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Sparkles, TrendingUp, Target } from 'lucide-react';

const points = [
  {
    icon: Sparkles,
    title: 'Advanced, Customized Solutions',
    desc: 'Offering advanced, customized solutions to meet unique business goals.',
    color: '#3b82f6', // blue
  },
  {
    icon: TrendingUp,
    title: 'Brand Growth & Visibility',
    desc: 'Driving brand growth and visibility through innovative digital strategies.',
    color: '#8b5cf6', // violet
  },
  {
    icon: Target,
    title: 'Empowering Success',
    desc: 'Empowering businesses to achieve sustainable success in a tech-driven world.',
    color: '#06b6d4', // cyan
  },
];

const AboutUsSection: React.FC = () => (
  <section className="relative w-full mx-auto py-8 xs:py-10 sm:py-12 md:py-16 lg:py-20 px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 bg-transparent overflow-hidden">
    {/* Enhanced Responsive Background decorative elements */}
    <div className="absolute top-10 xs:top-15 sm:top-20 right-10 xs:right-15 sm:right-20 w-16 xs:w-24 sm:w-32 h-16 xs:h-24 sm:h-32 bg-blue-200 dark:bg-blue-800 rounded-full opacity-20 blur-2xl xs:blur-3xl"></div>
    <div className="absolute bottom-10 xs:bottom-15 sm:bottom-20 left-10 xs:left-15 sm:left-20 w-20 xs:w-30 sm:w-40 h-20 xs:h-30 sm:h-40 bg-purple-200 dark:bg-purple-800 rounded-full opacity-20 blur-2xl xs:blur-3xl"></div>

    <div className="container-responsive relative z-10">
      {/* Enhanced Responsive Header Section */}
      <div className="text-center mb-8 xs:mb-10 sm:mb-12 md:mb-16 animate-fade-in-up">
        <h2 className="text-responsive-xl font-extrabold mb-3 xs:mb-4 text-blue-600 dark:text-blue-400">
          <span className="hidden sm:inline">About BRT Multi Software</span>
          <span className="sm:hidden">About BRT</span>
        </h2>
        <p className="text-responsive-base text-gray-600 dark:text-gray-300 max-w-xs xs:max-w-sm sm:max-w-md md:max-w-lg lg:max-w-4xl mx-auto leading-relaxed">
          <span className="hidden md:inline">Leading the digital transformation with innovative blockchain and crypto solutions, empowering businesses worldwide with cutting-edge technology.</span>
          <span className="md:hidden">Digital transformation with blockchain & crypto solutions for businesses worldwide.</span>
        </p>
      </div>

      {/* Enhanced Responsive Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 xs:gap-10 sm:gap-12 md:gap-16 items-center mb-8 xs:mb-10 sm:mb-12 md:mb-16">
        {/* Enhanced Responsive Left Content */}
        <div className="space-y-4 xs:space-y-5 sm:space-y-6 md:space-y-8 animate-fade-in-up order-2 lg:order-1">
          <div className="bg-white dark:bg-gray-800 rounded-responsive padding-responsive-lg shadow-xl border border-gray-100 dark:border-gray-700">
            <h3 className="text-responsive-lg font-bold text-gray-900 dark:text-white mb-3 xs:mb-4">Our Mission</h3>
            <p className="text-responsive-sm text-gray-600 dark:text-gray-300 leading-relaxed">
              <span className="hidden sm:inline">To reshape the digital world by delivering advanced technologies that drive success for businesses across various industries. We stay ahead of the curve with innovative solutions.</span>
              <span className="sm:hidden">Reshape digital world with advanced technologies for business success.</span>
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-responsive padding-responsive-lg shadow-xl border border-gray-100 dark:border-gray-700">
            <h3 className="text-responsive-lg font-bold text-gray-900 dark:text-white mb-3 xs:mb-4">Our Vision</h3>
            <p className="text-responsive-sm text-gray-600 dark:text-gray-300 leading-relaxed">
              <span className="hidden sm:inline">To be the global leader in digital transformation, continuously offering cutting-edge solutions that empower businesses to achieve sustainable success in a tech-driven world.</span>
              <span className="sm:hidden">Global leader in digital transformation with cutting-edge solutions.</span>
            </p>
          </div>
        </div>

        {/* Enhanced Responsive Right Image */}
        <div className="flex justify-center animate-slide-in-right order-1 lg:order-2">
          <div className="relative group">
            <div className="absolute inset-0 bg-blue-200 dark:bg-blue-800 rounded-responsive blur-xl xs:blur-2xl opacity-20 group-hover:opacity-30 transition-opacity duration-500"></div>
            <Image
              src="/assets/about-us.png"
              alt="About Us Illustration"
              width={500}
              height={500}
              className="relative w-full max-w-xs xs:max-w-sm sm:max-w-md md:max-w-lg h-auto object-contain drop-shadow-2xl group-hover:scale-105 transition-transform duration-500 rounded-responsive"
              priority
              sizes="(max-width: 475px) 280px, (max-width: 640px) 320px, (max-width: 768px) 400px, (max-width: 1024px) 500px, 500px"
            />
          </div>
        </div>
      </div>
      {/* Enhanced Responsive Features Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 xs:gap-5 sm:gap-6 md:gap-8 mb-8 xs:mb-10 sm:mb-12 md:mb-16">
        {points.map((point, idx) => {
          const IconComponent = point.icon;
          return (
            <div key={idx} className="group relative bg-white dark:bg-gray-800 rounded-responsive padding-responsive-lg shadow-xl hover:shadow-2xl border border-gray-100 dark:border-gray-700 transition-all duration-500 hover:-translate-y-2 animate-fade-in-up overflow-hidden" style={{ animationDelay: `${0.4 + idx * 0.1}s` }}>

              {/* Enhanced Responsive Solid top bar */}
              <div className="absolute top-0 left-0 w-full h-0.5 xs:h-1 rounded-t-responsive" style={{ backgroundColor: point.color }}></div>

              {/* Enhanced Responsive Corner decoration */}
              <div className="absolute -top-2 xs:-top-3 -right-2 xs:-right-3 w-12 xs:w-16 sm:w-20 h-12 xs:h-16 sm:h-20 rounded-full opacity-10 group-hover:opacity-20 transition-opacity duration-500" style={{ backgroundColor: point.color }}></div>

              {/* Enhanced Responsive Icon container */}
              <div className="relative mb-4 xs:mb-5 sm:mb-6 w-10 xs:w-12 sm:w-14 md:w-16 h-10 xs:h-12 sm:h-14 md:h-16 mx-auto flex items-center justify-center rounded-responsive group-hover:scale-110 transition-all duration-500 shadow-lg" style={{ backgroundColor: `${point.color}20` }}>
                <IconComponent className="w-5 xs:w-6 sm:w-7 md:w-8 h-5 xs:h-6 sm:h-7 md:h-8 transition-all duration-500" style={{ color: point.color }} />
              </div>

              {/* Enhanced Responsive Content */}
              <h3 className="text-responsive-base font-bold mb-3 xs:mb-4 text-center text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-500">
                {point.title}
              </h3>

              <p className="text-responsive-sm text-gray-600 dark:text-gray-300 text-center leading-relaxed group-hover:text-gray-800 dark:group-hover:text-gray-200 transition-colors duration-500">
                {point.desc}
              </p>

              {/* Enhanced Responsive Hover border */}
              <div className="absolute inset-0 rounded-responsive border-2 border-transparent group-hover:border-blue-400 transition-all duration-500"></div>
            </div>
          );
        })}
      </div>

      {/* Enhanced Responsive CTA Section */}
      <div className="text-center animate-fade-in-up" style={{ animationDelay: '0.8s' }}>
        <Link href="/software-development" className="group inline-flex items-center px-6 xs:px-8 sm:px-10 py-3 xs:py-3.5 sm:py-4 bg-blue-600 hover:bg-blue-700 text-white rounded-full font-semibold shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300 text-sm xs:text-base">
          <span className="hidden xs:inline">Explore Our Services</span>
          <span className="xs:hidden">Our Services</span>
          <svg className="ml-2 xs:ml-3 w-4 xs:w-5 h-4 xs:h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
          </svg>
        </Link>
      </div>
    </div>
  </section>
);

export default AboutUsSection;
