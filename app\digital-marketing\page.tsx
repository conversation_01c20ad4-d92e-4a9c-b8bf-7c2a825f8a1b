import { MarketingNavigation } from "@/components/marketing-navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { TrendingUp, BarChart3, Target, Zap, Globe, Users, MessageSquare, Search } from "lucide-react";
import Link from "next/link";

export default function DigitalMarketingPage() {
  const strategies = [
    {
      icon: Search,
      title: "SEO Optimization",
      description: "Advanced SEO strategies that improve your search rankings and drive organic traffic growth.",
      metrics: "Average 200% increase in organic traffic"
    },
    {
      icon: MessageSquare,
      title: "Social Media Marketing",
      description: "Engaging social campaigns that build communities and drive brand awareness across platforms.",
      metrics: "Up to 300% engagement boost"
    },
    {
      icon: Target,
      title: "PPC Advertising",
      description: "Data-driven paid advertising campaigns that maximize ROI and conversion rates.",
      metrics: "Average 150% ROI improvement"
    },
    {
      icon: BarChart3,
      title: "Analytics & Reporting",
      description: "Comprehensive tracking and analysis to optimize performance and demonstrate clear results.",
      metrics: "Real-time performance insights"
    }
  ];

  const caseStudies = [
    {
      client: "E-commerce Retailer",
      challenge: "Low online visibility and poor conversion rates",
      solution: "Complete SEO overhaul and PPC campaign optimization",
      results: ["400% increase in organic traffic", "250% boost in conversions", "ROI increased by 180%"],
      industry: "Retail"
    },
    {
      client: "SaaS Startup",
      challenge: "Limited brand awareness and lead generation",
      solution: "Multi-channel content marketing and social media strategy",
      results: ["500% growth in social following", "300% increase in qualified leads", "40% reduction in CAC"],
      industry: "Technology"
    },
    {
      client: "Local Service Business",
      challenge: "Poor local search presence and online reputation",
      solution: "Local SEO optimization and reputation management",
      results: ["#1 ranking for key local terms", "95% positive review rating", "200% increase in bookings"],
      industry: "Services"
    }
  ];

  return (
    <main className="min-h-screen">
      <MarketingNavigation />
      
      {/* Hero Section */}
      <section className="pt-24 pb-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-blue-900/20 to-blue-900/20">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="flex items-center mb-6">
                <div className="p-4 rounded-2xl bg-gradient-to-br from-blue-500 to-blue-700 shadow-lg mr-4">
                  <TrendingUp className="h-12 w-12 text-white" />
                </div>
                <h1 className="text-4xl md:text-5xl font-bold hero-title">
                  Digital Marketing
                </h1>
              </div>
              <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
                Accelerate your business growth with data-driven marketing strategies that deliver measurable results and maximize your return on investment.
              </p>
              <div className="grid grid-cols-2 gap-4 mb-8">
                <div className="text-center p-4 bg-blue-500/10 rounded-lg border border-blue-500/20">
                  <div className="text-2xl font-bold text-blue-500">500+</div>
                  <div className="text-sm text-muted-foreground">Campaigns Launched</div>
                </div>
                <div className="text-center p-4 bg-blue-500/10 rounded-lg border border-blue-500/20">
                  <div className="text-2xl font-bold text-blue-500">250%</div>
                  <div className="text-sm text-muted-foreground">Average ROI Increase</div>
                </div>
              </div>
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3">
                <Zap className="mr-2 h-5 w-5" />
                Boost Your Growth
              </Button>
            </div>
            <div className="space-y-4">
              {[
                { icon: Globe, title: "Global Reach", desc: "Expand your market presence worldwide" },
                { icon: Users, title: "Target Audience", desc: "Connect with your ideal customers" },
                { icon: BarChart3, title: "Data-Driven", desc: "Make decisions based on real insights" },
              ].map((feature, index) => (
                <div key={index} className="flex items-center space-x-4 p-4 service-card rounded-lg">
                  <div className="p-2 rounded-lg bg-blue-500/20">
                    <feature.icon className="h-6 w-6 text-blue-500" />
                  </div>
                  <div>
                    <h3 className="font-semibold">{feature.title}</h3>
                    <p className="text-sm text-muted-foreground">{feature.desc}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Strategies Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-16 hero-title">
            Marketing Strategies That Work
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {strategies.map((strategy, index) => (
              <Card key={index} className="service-card hover:scale-105 transition-all duration-300 group">
                <CardContent className="p-8">
                  <div className="flex items-center mb-4">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-blue-500 to-blue-700 shadow-lg mr-4 group-hover:shadow-xl transition-all duration-300">
                      <strategy.icon className="h-8 w-8 text-white" />
                    </div>
                    <h3 className="text-xl font-bold group-hover:text-blue-500 transition-colors">
                      {strategy.title}
                    </h3>
                  </div>
                  <p className="text-muted-foreground mb-4 leading-relaxed">
                    {strategy.description}
                  </p>
                  <div className="text-sm font-semibold text-blue-500 bg-blue-500/10 px-3 py-2 rounded-full inline-block">
                    {strategy.metrics}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Case Studies */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-muted/20">
        <div className="container mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-16 hero-title">
            Success Stories
          </h2>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {caseStudies.map((study, index) => (
              <Card key={index} className="service-card hover:scale-105 transition-all duration-300">
                <CardContent className="p-8">
                  <div className="text-sm text-blue-500 font-semibold mb-2">{study.industry}</div>
                  <h3 className="text-xl font-bold mb-4">{study.client}</h3>
                  
                  <div className="mb-4">
                    <h4 className="font-semibold text-sm mb-2 text-muted-foreground">Challenge:</h4>
                    <p className="text-sm">{study.challenge}</p>
                  </div>
                  
                  <div className="mb-6">
                    <h4 className="font-semibold text-sm mb-2 text-muted-foreground">Solution:</h4>
                    <p className="text-sm">{study.solution}</p>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold text-sm mb-3 text-muted-foreground">Results:</h4>
                    <ul className="space-y-2">
                      {study.results.map((result, resultIndex) => (
                        <li key={resultIndex} className="flex items-center text-sm">
                          <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-3" />
                          {result}
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Tools & Platforms */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-16 hero-title">
            Platforms We Master
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8">
            {[
              "Google Ads", "Facebook Ads", "Instagram", "LinkedIn", "Twitter", "TikTok",
              "Google Analytics", "SEMrush", "HubSpot", "Mailchimp", "Hootsuite", "Shopify"
            ].map((platform, index) => (
              <div key={index} className="p-4 service-card rounded-lg hover:scale-105 transition-all duration-300 group">
                <div className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-700 rounded-lg mx-auto mb-2 flex items-center justify-center group-hover:shadow-lg transition-all duration-300">
                    <span className="text-white font-bold text-xs text-center">{platform}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-blue-600 to-blue-600 text-white">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Accelerate Your Growth?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Partner with us to create marketing campaigns that deliver exceptional results and drive sustainable business growth.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg" 
              variant="secondary"
              className="px-8 py-3"
            >
              Get Free Strategy Session
            </Button>
            <Button 
              size="lg" 
              variant="outline"
              className="px-8 py-3 border-white text-white hover:bg-white hover:text-blue-600"
              asChild
            >
              <Link href="/">
                View All Services
              </Link>
            </Button>
          </div>
        </div>
      </section>
    </main>
  );
}