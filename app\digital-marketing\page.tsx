import { MarketingNavigation } from "@/components/marketing-navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  TrendingUp, BarChart3, Target, Zap, Globe, Users, MessageSquare, Search,
  ArrowRight, CheckCircle, Star, Play, Rocket, MousePointer,
  Heart, Smartphone, Headphones, Mail, Phone, Calendar,
  Linkedin, Activity
} from "lucide-react";
import Link from "next/link";

export default function DigitalMarketingPage() {
  const services = [
    {
      icon: Search,
      title: "Search Engine Optimization",
      description: "Boost your organic visibility with advanced SEO strategies that drive qualified traffic and improve search rankings.",
      features: ["Keyword Research", "On-Page SEO", "Technical SEO", "Link Building"],
      price: "Starting at $1,500/month",
      popular: false
    },
    {
      icon: MousePointer,
      title: "Pay-Per-Click Advertising",
      description: "Maximize ROI with data-driven PPC campaigns across Google Ads, Facebook, and other platforms.",
      features: ["Campaign Setup", "Ad Creation", "Bid Management", "Performance Tracking"],
      price: "Starting at $2,000/month",
      popular: true
    },
    {
      icon: MessageSquare,
      title: "Social Media Marketing",
      description: "Build engaged communities and amplify your brand presence across all major social platforms.",
      features: ["Content Creation", "Community Management", "Influencer Outreach", "Social Advertising"],
      price: "Starting at $1,200/month",
      popular: false
    },
    {
      icon: Mail,
      title: "Email Marketing",
      description: "Nurture leads and retain customers with personalized email campaigns that convert.",
      features: ["Email Design", "Automation Setup", "List Management", "A/B Testing"],
      price: "Starting at $800/month",
      popular: false
    },
    {
      icon: BarChart3,
      title: "Analytics & Reporting",
      description: "Make data-driven decisions with comprehensive tracking and detailed performance insights.",
      features: ["Custom Dashboards", "Monthly Reports", "Goal Tracking", "ROI Analysis"],
      price: "Starting at $600/month",
      popular: false
    },
    {
      icon: Globe,
      title: "Content Marketing",
      description: "Attract and engage your audience with high-quality content that drives organic growth.",
      features: ["Content Strategy", "Blog Writing", "Video Production", "Content Distribution"],
      price: "Starting at $1,000/month",
      popular: false
    }
  ];

  const stats = [
    { number: "500+", label: "Successful Campaigns", icon: Rocket },
    { number: "250%", label: "Average ROI Increase", icon: TrendingUp },
    { number: "98%", label: "Client Satisfaction", icon: Star },
    { number: "24/7", label: "Support Available", icon: Headphones }
  ];

  const testimonials = [
    {
      name: "Sarah Johnson",
      position: "Marketing Director",
      company: "TechStart Inc.",
      content: "Their digital marketing strategies transformed our online presence. We saw a 300% increase in qualified leads within just 3 months.",
      rating: 5,
      image: "/api/placeholder/60/60"
    },
    {
      name: "Michael Chen",
      position: "CEO",
      company: "E-commerce Plus",
      content: "The ROI from their PPC campaigns exceeded our expectations. Professional team with exceptional results.",
      rating: 5,
      image: "/api/placeholder/60/60"
    },
    {
      name: "Emily Rodriguez",
      position: "Founder",
      company: "Local Services Co.",
      content: "Outstanding SEO work that got us ranking #1 for our key terms. Highly recommend their services.",
      rating: 5,
      image: "/api/placeholder/60/60"
    }
  ];

  const process = [
    {
      step: "01",
      title: "Strategy & Planning",
      description: "We analyze your business, competitors, and target audience to create a comprehensive digital marketing strategy.",
      icon: Target
    },
    {
      step: "02",
      title: "Campaign Setup",
      description: "Our experts set up and optimize your campaigns across all relevant platforms and channels.",
      icon: Rocket
    },
    {
      step: "03",
      title: "Execution & Monitoring",
      description: "We launch your campaigns and continuously monitor performance to ensure optimal results.",
      icon: Activity
    },
    {
      step: "04",
      title: "Analysis & Optimization",
      description: "Regular analysis and optimization ensure your campaigns continue to improve and deliver better ROI.",
      icon: BarChart3
    }
  ];

  return (
    <main className="min-h-screen">
      <MarketingNavigation />

      {/* Hero Section */}
      <section className="relative pt-24 pb-20 px-4 sm:px-6 lg:px-8 overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900/10 via-purple-900/5 to-blue-900/10"></div>
        <div className="absolute top-20 left-10 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>

        <div className="container mx-auto relative z-10">
          <div className="text-center max-w-4xl mx-auto mb-16">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-blue-500/10 border border-blue-500/20 mb-6">
              <Rocket className="h-4 w-4 text-blue-500 mr-2" />
              <span className="text-sm font-medium text-blue-500">Digital Marketing Excellence</span>
            </div>

            <h1 className="text-5xl md:text-7xl font-bold mb-6 hero-title leading-tight">
              Accelerate Your
              <span className="block bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent">
                Digital Growth
              </span>
            </h1>

            <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed max-w-3xl mx-auto">
              Transform your business with data-driven marketing strategies that deliver measurable results and maximize ROI across all digital channels.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg group">
                <Zap className="mr-2 h-5 w-5 group-hover:animate-pulse" />
                Start Your Growth Journey
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button size="lg" variant="outline" className="px-8 py-4 text-lg group">
                <Play className="mr-2 h-5 w-5" />
                Watch Success Stories
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              {stats.map((stat, index) => (
                <div key={index} className="text-center p-6 service-card rounded-xl hover:scale-105 transition-all duration-300">
                  <div className="inline-flex items-center justify-center w-12 h-12 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 mb-3">
                    <stat.icon className="h-6 w-6 text-white" />
                  </div>
                  <div className="text-2xl md:text-3xl font-bold text-blue-500 mb-1">{stat.number}</div>
                  <div className="text-sm text-muted-foreground">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 hero-title">
              Our Digital Marketing Services
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Comprehensive digital marketing solutions designed to drive growth, increase visibility, and maximize your return on investment.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <Card key={index} className={`service-card hover:scale-105 transition-all duration-300 group relative ${service.popular ? 'ring-2 ring-blue-500' : ''}`}>
                {service.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-blue-500 text-white px-4 py-1">Most Popular</Badge>
                  </div>
                )}
                <CardContent className="p-8">
                  <div className="flex items-center mb-6">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 shadow-lg mr-4 group-hover:shadow-xl transition-all duration-300">
                      <service.icon className="h-8 w-8 text-white" />
                    </div>
                    <h3 className="text-xl font-bold group-hover:text-blue-500 transition-colors">
                      {service.title}
                    </h3>
                  </div>

                  <p className="text-muted-foreground mb-6 leading-relaxed">
                    {service.description}
                  </p>

                  <ul className="space-y-3 mb-6">
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-3 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>

                  <div className="border-t pt-6">
                    <div className="text-2xl font-bold text-blue-500 mb-4">{service.price}</div>
                    <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white group">
                      Get Started
                      <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-muted/20">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 hero-title">
              Our Proven Process
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              A systematic approach to digital marketing that ensures consistent results and continuous growth for your business.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {process.map((step, index) => (
              <div key={index} className="relative">
                {/* Connection Line */}
                {index < process.length - 1 && (
                  <div className="hidden lg:block absolute top-16 left-full w-full h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 z-0"></div>
                )}

                <Card className="service-card hover:scale-105 transition-all duration-300 group relative z-10">
                  <CardContent className="p-8 text-center">
                    <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 mb-6 group-hover:shadow-xl transition-all duration-300">
                      <step.icon className="h-8 w-8 text-white" />
                    </div>

                    <div className="text-3xl font-bold text-blue-500 mb-2">{step.step}</div>
                    <h3 className="text-xl font-bold mb-4 group-hover:text-blue-500 transition-colors">
                      {step.title}
                    </h3>
                    <p className="text-muted-foreground leading-relaxed">
                      {step.description}
                    </p>
                  </CardContent>
                </Card>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 hero-title">
              What Our Clients Say
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Don't just take our word for it. Here's what our satisfied clients have to say about our digital marketing services.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="service-card hover:scale-105 transition-all duration-300 group">
                <CardContent className="p-8">
                  <div className="flex items-center mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                    ))}
                  </div>

                  <p className="text-muted-foreground mb-6 leading-relaxed italic">
                    "{testimonial.content}"
                  </p>

                  <div className="flex items-center">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center mr-4">
                      <span className="text-white font-bold text-lg">
                        {testimonial.name.charAt(0)}
                      </span>
                    </div>
                    <div>
                      <div className="font-semibold">{testimonial.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {testimonial.position} at {testimonial.company}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Tools & Platforms */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-muted/20">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 hero-title">
              Platforms & Tools We Master
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              We leverage the latest tools and platforms to deliver exceptional results across all digital marketing channels.
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {[
              { name: "Google Ads", icon: Search, category: "PPC" },
              { name: "Facebook", icon: MessageSquare, category: "Social" },
              { name: "Instagram", icon: Heart, category: "Social" },
              { name: "LinkedIn", icon: Linkedin, category: "B2B" },
              { name: "Twitter", icon: MessageSquare, category: "Social" },
              { name: "YouTube", icon: Play, category: "Video" },
              { name: "Analytics", icon: BarChart3, category: "Data" },
              { name: "Email", icon: Mail, category: "Email" },
              { name: "SEO Tools", icon: TrendingUp, category: "SEO" },
              { name: "CRM", icon: Users, category: "Management" },
              { name: "Automation", icon: Zap, category: "Tools" },
              { name: "Mobile", icon: Smartphone, category: "Mobile" }
            ].map((platform, index) => (
              <Card key={index} className="service-card hover:scale-105 transition-all duration-300 group text-center p-6">
                <div className="inline-flex items-center justify-center w-16 h-16 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 mb-4 group-hover:shadow-xl transition-all duration-300">
                  <platform.icon className="h-8 w-8 text-white" />
                </div>
                <h3 className="font-semibold mb-2 group-hover:text-blue-500 transition-colors">
                  {platform.name}
                </h3>
                <Badge variant="secondary" className="text-xs">
                  {platform.category}
                </Badge>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 hero-title">
              Frequently Asked Questions
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Get answers to common questions about our digital marketing services and how we can help grow your business.
            </p>
          </div>

          <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8">
            {[
              {
                question: "How long does it take to see results?",
                answer: "Results vary by service, but you can typically expect to see initial improvements within 30-60 days, with significant growth within 3-6 months."
              },
              {
                question: "Do you work with small businesses?",
                answer: "Absolutely! We work with businesses of all sizes, from startups to enterprise companies, tailoring our strategies to fit your budget and goals."
              },
              {
                question: "What's included in your reporting?",
                answer: "Our comprehensive reports include traffic analytics, conversion tracking, ROI analysis, and actionable insights for continuous improvement."
              },
              {
                question: "Can you help with our existing campaigns?",
                answer: "Yes, we can audit and optimize your existing campaigns to improve performance and maximize your current marketing investments."
              }
            ].map((faq, index) => (
              <Card key={index} className="service-card p-6">
                <h3 className="text-lg font-semibold mb-3 text-blue-500">{faq.question}</h3>
                <p className="text-muted-foreground leading-relaxed">{faq.answer}</p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8 overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600 via-blue-700 to-purple-700"></div>
        <div className="absolute top-0 left-0 w-full h-full bg-[url('/api/placeholder/1920/1080')] opacity-10 bg-cover bg-center"></div>
        <div className="absolute top-10 left-10 w-72 h-72 bg-white/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-10 right-10 w-96 h-96 bg-white/5 rounded-full blur-3xl"></div>

        <div className="container mx-auto text-center relative z-10">
          <div className="max-w-4xl mx-auto text-white">
            <h2 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
              Ready to Transform Your
              <span className="block bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">
                Digital Presence?
              </span>
            </h2>

            <p className="text-xl md:text-2xl mb-8 opacity-90 leading-relaxed">
              Join hundreds of successful businesses that have accelerated their growth with our proven digital marketing strategies.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center mb-12">
              <Button
                size="lg"
                className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 text-lg font-semibold group"
              >
                <Calendar className="mr-2 h-5 w-5" />
                Get Free Strategy Session
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4 text-lg font-semibold"
                asChild
              >
                <Link href="/contact">
                  <Phone className="mr-2 h-5 w-5" />
                  Call Us Now
                </Link>
              </Button>
            </div>

            <div className="flex flex-col sm:flex-row items-center justify-center gap-8 text-sm opacity-80">
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 mr-2 text-green-400" />
                Free consultation & audit
              </div>
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 mr-2 text-green-400" />
                No long-term contracts
              </div>
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 mr-2 text-green-400" />
                Proven results guaranteed
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}