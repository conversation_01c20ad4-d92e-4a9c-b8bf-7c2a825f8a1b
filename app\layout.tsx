import './globals.css';
import type { Metadata } from 'next';
import { ThemeProvider } from '@/components/theme-provider';
import { PostHogProvider } from '@/components/PostHogProvider';
import { lazy, Suspense } from 'react';

// Lazy load non-critical components
const SocialSidebar = lazy(() => import('@/components/social-sidebar').then(module => ({ default: module.SocialSidebar })));
const WhatsAppButton = lazy(() => import('@/components/whatsapp-button').then(module => ({ default: module.WhatsAppButton })));
const FooterVisibility = lazy(() => import('@/components/FooterVisibility'));

export const metadata: Metadata = {
  metadataBase: new URL('https://brtmultisoftware.com'),
  title: 'BRT - Gaming Software, Graphics & Digital Marketing Services',
  description: 'Professional gaming software development, graphics design, and digital marketing services by BRT',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = typeof window !== 'undefined' ? window.location.pathname : '';
  // For SSR/Next.js, use usePathname hook
  // But since this is a layout, we need to use a Client Component for hooks
  // So, wrap the ThemeProvider children in a ClientOnly component
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link 
          rel="stylesheet" 
          href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" 
        />
        {/* Favicon */}
        <link rel="icon" href="https://brtmultisoftware.com/img/BRTLOGO.png" type="image/png" />
        {/* Canonical URL */}
        <link rel="canonical" href="https://brtmultisoftware.com/" />
        {/* Robots */}
        <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
        {/* Open Graph Meta Tags */}
        <meta property="og:type" content="website" />
        <meta property="og:locale" content="en_US" />
        <meta property="og:url" content="https://brtmultisoftware.com/" />
        <meta property="og:site_name" content="BRT Multi Software" />
        <meta property="og:title" content="BRT - Gaming Software, Graphics & Digital Marketing Services" />
        <meta property="og:description" content="Professional gaming software development, graphics design, and digital marketing services by BRT." />
        <meta property="og:image" content="https://brtmultisoftware.com/img/BRTLOGO.png" />
        <meta property="og:image:alt" content="BRT Logo" />
        {/* Twitter Card Meta Tags */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="BRT - Gaming Software, Graphics & Digital Marketing Services" />
        <meta name="twitter:description" content="Professional gaming software development, graphics design, and digital marketing services by BRT." />
        <meta name="twitter:image" content="https://brtmultisoftware.com/img/BRTLOGO.png" />
        <meta name="twitter:site" content="@brtmultisoftware" />
        {/* Theme Color */}
        <meta name="theme-color" content="#000000" />
        {/* Author */}
        <meta name="author" content="BRT Multi Software" />
        {/* Additional SEO */}
        <meta name="keywords" content="Gaming Software, Software Development, Graphics Design, Digital Marketing, BRT, Web Development, Branding, India" />
      </head>
      <body style={{ fontFamily: 'Inter, sans-serif' }}>
        <PostHogProvider>
          <ThemeProvider
            attribute="class"
            defaultTheme="dark"
            enableSystem
            disableTransitionOnChange={false}
          >
            {children}

            <Suspense fallback={<div />}>
              <FooterVisibility />
            </Suspense>
            <Suspense fallback={<div />}>
              <SocialSidebar />
            </Suspense>
            <Suspense fallback={<div />}>
              <WhatsAppButton />
            </Suspense>
          </ThemeProvider>
        </PostHogProvider>
      </body>
    </html>
  );
}