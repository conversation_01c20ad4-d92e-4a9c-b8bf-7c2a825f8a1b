import { gamesData } from '@/components/data/games-data';
import GamesListPage from '@/components/games/GamesListPage';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  metadataBase: new URL('https://brtmultisoftware.com'),
  title: 'Premium Games Collection | BRT Multi Software',
  description: 'Explore our collection of premium games including Teen Patti, Rummy, Ludo, and Poker. Professional game development with cutting-edge technology.',
  keywords: [
    'Games Collection',
    'Teen Patti',
    'Rummy Game',
    'Ludo Game',
    'Poker Game',
    'Mobile Games',
    'Game Development',
    'BRT Multi Software',
    'Gaming Solutions',
    'Multiplayer Games'
  ],
  authors: [{ name: 'BRT Multi Software', url: 'https://brtmultisoftware.com/' }],
  creator: 'BRT Multi Software',
  openGraph: {
    title: 'Premium Games Collection | BRT Multi Software',
    description: 'Explore our collection of premium games including Teen Patti, Rummy, Ludo, and Poker.',
    url: 'https://brtmultisoftware.com/games',
    siteName: 'BRT Multi Software',
    images: [
      {
        url: '/gaming/ludogame.png',
        width: 1200,
        height: 630,
        alt: 'Premium Games Collection by BRT Multi Software',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Premium Games Collection | BRT Multi Software',
    description: 'Explore our collection of premium games including Teen Patti, Rummy, Ludo, and Poker.',
    images: ['/gaming/ludogame.png'],
  },
  alternates: {
    canonical: 'https://brtmultisoftware.com/games',
  },
};

export default function GamesPage() {
  return <GamesListPage games={gamesData} />;
}
