import Image from 'next/image';
import { Button } from '@/components/ui/button';

export default function HeroSection() {
  return (
    <section className="relative w-full min-h-screen flex items-center py-8 sm:py-12 md:py-16 lg:py-20 xl:py-24 transition-colors duration-300 bg-white dark:bg-slate-950 text-foreground overflow-hidden">
      {/* Optimized SVG Gaming Shapes - Responsive positioning */}
      <svg className="absolute left-2 sm:left-4 md:left-8 lg:left-10 top-4 sm:top-6 md:top-8 lg:top-10 w-12 h-12 sm:w-16 sm:h-16 md:w-20 md:h-20 lg:w-24 lg:h-24 opacity-20 md:opacity-30 z-0 animate-float" viewBox="0 0 64 64" fill="none">
        <circle cx="32" cy="32" r="32" fill="#0EA5E9" />
      </svg>

      <svg className="absolute right-2 sm:right-4 md:right-8 lg:right-10 top-8 sm:top-12 md:top-16 lg:top-24 w-10 h-10 sm:w-12 sm:h-12 md:w-16 md:h-16 lg:w-20 lg:h-20 opacity-25 md:opacity-40 z-0 animate-float" viewBox="0 0 48 48" fill="none">
        <rect x="8" y="8" width="32" height="32" rx="8" fill="#A855F7" fillOpacity="0.3" />
        <rect x="14" y="14" width="20" height="20" rx="5" fill="#10B981" fillOpacity="0.4" />
      </svg>

      {/* Controller SVG - Hidden on mobile for cleaner look */}
      <svg className="hidden md:block absolute left-1/2 -translate-x-1/2 top-0 w-24 h-12 lg:w-32 lg:h-16 opacity-15 lg:opacity-20 z-0 animate-float" viewBox="0 0 128 64" fill="none">
        <ellipse cx="64" cy="32" rx="60" ry="20" fill="#0EA5E9" fillOpacity="0.15" />
        <rect x="44" y="20" width="40" height="24" rx="12" fill="#A855F7" fillOpacity="0.18" />
      </svg>

      {/* Corner decorative elements */}
      <svg className="absolute left-4 sm:left-8 md:left-1/4 bottom-4 sm:bottom-6 md:bottom-10 w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 opacity-30 md:opacity-40 z-0" viewBox="0 0 32 32" fill="none">
        <circle cx="16" cy="16" r="16" fill="#10B981" fillOpacity="0.5" />
      </svg>

      <svg className="absolute right-4 sm:right-8 md:right-1/4 bottom-0 w-8 h-12 sm:w-12 sm:h-18 md:w-16 md:h-24 opacity-20 md:opacity-30 z-0 animate-float" viewBox="0 0 48 72" fill="none">
        <rect x="4" y="8" width="40" height="56" rx="8" fill="#fff" fillOpacity="0.15" stroke="#0EA5E9" strokeWidth="2" />
        <circle cx="24" cy="36" r="8" fill="#A855F7" fillOpacity="0.2" />
      </svg>

      {/* Main Content Container - Fully responsive */}
      <div className="relative container mx-auto px-4 sm:px-6 lg:px-8 flex flex-col lg:flex-row items-center justify-between gap-8 sm:gap-10 md:gap-12 lg:gap-16 xl:gap-20 z-10 w-full">
        {/* Left Side: Content */}
        <div className="flex-1 w-full max-w-none lg:max-w-2xl space-y-4 sm:space-y-6 md:space-y-8 text-center lg:text-left">
          {/* Badge */}
          <span className="inline-block px-3 py-1.5 sm:px-4 sm:py-2 md:px-5 md:py-2 rounded-full bg-sky-500 dark:bg-sky-400 text-white text-xs sm:text-sm font-semibold tracking-wider shadow-md">
            Your Trusted
          </span>

          {/* Main Heading - Responsive typography */}
          <h1 className="text-2xl xs:text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-extrabold leading-tight
            text-slate-900 dark:text-sky-100">
            Game Development Partner
          </h1>

          {/* Description - Responsive text sizing */}
          <p className="text-sm xs:text-base sm:text-lg md:text-xl lg:text-2xl xl:text-3xl max-w-full lg:max-w-2xl text-slate-700 dark:text-slate-300 leading-relaxed">
            Level up your ideas with our expert game development services. We turn your vision into engaging, high-quality games for all platforms.
          </p>

          {/* CTA Button - Responsive sizing */}
          <div className="pt-2 sm:pt-4 md:pt-6">
            <Button
              size="lg"
              className="text-sm sm:text-base md:text-lg px-6 py-3 sm:px-8 sm:py-4 bg-sky-500 hover:bg-sky-600 text-white dark:bg-sky-400 dark:hover:bg-sky-500 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl"
            >
              Start Creating
            </Button>
          </div>
        </div>

        {/* Right Side: Image - Fully responsive */}
        <div className="flex-1 w-full flex justify-center items-center relative mt-8 lg:mt-0">
          {/* Glow effect - Responsive sizing */}
          <div className="absolute -z-10 w-64 h-64 xs:w-80 xs:h-80 sm:w-96 sm:h-96 md:w-[440px] md:h-[440px] lg:w-[480px] lg:h-[480px] xl:w-[560px] xl:h-[560px] rounded-full bg-sky-400/20 dark:bg-purple-600/30 blur-2xl" />

          {/* Image container - Responsive sizing */}
          <div className="relative w-56 h-56 xs:w-72 xs:h-72 sm:w-80 sm:h-80 md:w-[400px] md:h-[400px] lg:w-[440px] lg:h-[440px] xl:w-[520px] xl:h-[520px] drop-shadow-2xl">
            <Image
              src="/gaming/game-banner.png"
              alt="Gaming Hero Banner"
              fill
              className="object-contain"
              priority
              sizes="(max-width: 475px) 224px, (max-width: 640px) 288px, (max-width: 768px) 320px, (max-width: 1024px) 400px, (max-width: 1280px) 440px, 520px"
            />
          </div>
        </div>
      </div>
    </section>
  );
}