'use client';

import React from 'react';
import { Code, ShieldCheck, Clock, Award, Zap, ThumbsUp, Target, Rocket, Users, Settings } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

const whyChooseUs = [
  {
    title: 'Expert Developers',
    description: 'Skilled team with deep expertise in the latest development technologies.',
    icon: Code,
  },
  {
    title: 'Secure & Scalable',
    description: 'Robust architectures that ensure data protection and performance.',
    icon: ShieldCheck,
  },
  {
    title: 'Timely Delivery',
    description: 'On-time project delivery with a clear focus on milestones and quality.',
    icon: Clock,
  },
  {
    title: 'Award-Winning Support',
    description: '24/7 dedicated technical support to keep your business running smoothly.',
    icon: Award,
  },
  {
    title: 'Innovative Approach',
    description: 'Out-of-the-box thinking to build future-ready digital solutions.',
    icon: Zap,
  },
  {
    title: 'Client Satisfaction',
    description: 'Long-term relationships driven by transparency, honesty, and value.',
    icon: ThumbsUp,
  },

  {
    title: 'Cutting-Edge Technology',
    description: 'We use the latest technologies and frameworks to build future-ready applications.',
    icon: Rocket,
  },
  {
    title: 'Expert Team',
    description: 'Experienced developers, designers, and project managers dedicated to your success.',
    icon: Users,
  },
  {
    title: 'Agile Development',
    description: 'Flexible development methodology with regular updates and client collaboration.',
    icon: Settings,
  },
];

const cornerColors = [
  '#3b82f6', // blue-500
  '#8b5cf6', // violet-500
  '#06b6d4', // cyan-500
  '#10b981', // emerald-500
  '#f59e0b', // amber-500
  '#ef4444', // red-500
  '#ec4899', // pink-500
  '#6366f1', // indigo-500
  '#84cc16', // lime-500
];

const WhyChooseCard = ({ title, description, Icon, colorIndex }: { title: string; description: string; Icon: React.FC<{ className?: string }>; colorIndex: number }) => (
  <Card className="hover:scale-105 transition-all duration-500 group bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 shadow-xl hover:shadow-2xl rounded-responsive relative overflow-hidden animate-fade-in-up" style={{ animationDelay: `${colorIndex * 0.1}s` }}>
    {/* Enhanced Responsive Right corner shape only */}
    <svg className="absolute -top-0.5 xs:-top-1 sm:-top-2 -right-0.5 xs:-right-1 sm:-right-2 w-8 xs:w-10 sm:w-12 md:w-16 h-8 xs:h-10 sm:h-12 md:h-16 z-10 group-hover:scale-110 transition-transform duration-500" viewBox="0 0 64 64">
      <path d="M0,0 Q64,0 64,64 L0,0 Z" fill={cornerColors[colorIndex % cornerColors.length]} />
    </svg>

    {/* Enhanced responsive background with solid colors */}
    <div className="absolute inset-0 bg-blue-50 dark:bg-gray-800 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

    <CardContent className="padding-responsive-lg text-center flex flex-col items-center relative z-10">
      <div className="flex justify-center mb-3 xs:mb-4 sm:mb-6 md:mb-8">
        <div className="relative w-10 xs:w-12 sm:w-14 md:w-16 lg:w-18 xl:w-20 h-10 xs:h-12 sm:h-14 md:h-16 lg:h-18 xl:h-20 flex items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900 group-hover:scale-125 transition-all duration-500 group-hover:rotate-12 shadow-lg border-2 border-gray-200 dark:border-gray-700">
          {/* Solid ring effect */}
          <div className="absolute inset-0 rounded-full bg-blue-500 opacity-0 group-hover:opacity-20 transition-opacity duration-500"></div>
          <Icon className="h-4 xs:h-5 sm:h-6 md:h-8 lg:h-9 xl:h-10 w-4 xs:w-5 sm:w-6 md:w-8 lg:w-9 xl:w-10 text-gray-700 dark:text-gray-300 group-hover:text-blue-600 dark:group-hover:text-blue-400 relative z-10 group-hover:scale-110 transition-all duration-500" />
        </div>
      </div>
      <h3 className="text-responsive-lg font-bold text-gray-900 dark:text-white mb-2 xs:mb-3 sm:mb-4 md:mb-6 leading-tight group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-all duration-500">{title}</h3>
      <p className="text-responsive-sm text-gray-600 dark:text-gray-400 group-hover:text-gray-800 dark:group-hover:text-gray-200 leading-relaxed transition-colors duration-500">{description}</p>

      {/* Enhanced responsive border with solid color */}
      <div className="absolute inset-0 rounded-responsive border-2 border-transparent group-hover:border-blue-400 transition-all duration-500"></div>
    </CardContent>
  </Card>
);

const WhyChooseUs = () => {
  return (
    <section className="relative py-4 xs:py-6 sm:py-8 md:py-12 lg:py-16 xl:py-20 px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 bg-transparent overflow-hidden">

      <div className="relative z-10 container-responsive">
        <div className="text-center mb-6 xs:mb-7 sm:mb-8 md:mb-12 lg:mb-16 animate-fade-in-up">
          <h2 className="text-responsive-xl font-extrabold text-blue-600 dark:text-blue-400 mb-2 xs:mb-3 sm:mb-4 tracking-tight">
            <span className="hidden sm:inline">Why Choose BRT Software Development</span>
            <span className="sm:hidden">Why Choose BRT</span>
          </h2>
          <p className="text-responsive-base text-gray-600 dark:text-gray-300 container-responsive font-medium leading-relaxed">
            <span className="hidden md:inline">We combine technical expertise with business acumen to deliver software solutions that drive real results.</span>
            <span className="md:hidden">Technical expertise with business acumen for real results.</span>
          </p>
        </div>
        <div className="grid grid-cols-1 xs:grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-responsive">
          {whyChooseUs.map((item, index) => (
            <WhyChooseCard
              key={index}
              title={item.title}
              description={item.description}
              Icon={item.icon}
              colorIndex={index}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default WhyChooseUs;
