import { SharedNavigation } from "@/components/shared-navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Gamepad2, Code, Zap, Users, Trophy, Rocket, Shield, Globe } from "lucide-react";
import Link from "next/link";

export default function GamingServicesPage() {
  const services = [
    {
      icon: Code,
      title: "Custom Game Development",
      description: "End-to-end game development from concept to launch, tailored to your vision.",
      features: ["Unity & Unreal Engine", "Cross-platform deployment", "Custom gameplay mechanics", "Performance optimization"],
      price: "Starting from $15,000"
    },
    {
      icon: Zap,
      title: "Game Engine Development",
      description: "High-performance custom game engines built for your specific requirements.",
      features: ["Custom rendering pipeline", "Physics simulation", "Audio system integration", "Multi-threading support"],
      price: "Starting from $25,000"
    },
    {
      icon: Users,
      title: "Multiplayer Solutions",
      description: "Robust multiplayer architecture supporting thousands of concurrent players.",
      features: ["Real-time networking", "Dedicated servers", "Anti-cheat systems", "Matchmaking algorithms"],
      price: "Starting from $20,000"
    },
    {
      icon: Trophy,
      title: "Game Design Consultation",
      description: "Expert game design advice to create engaging and monetizable experiences.",
      features: ["Game mechanics design", "Monetization strategies", "User experience optimization", "Market analysis"],
      price: "Starting from $5,000"
    },
    {
      icon: Shield,
      title: "Game Testing & QA",
      description: "Comprehensive testing services to ensure bug-free gaming experiences.",
      features: ["Automated testing", "Performance testing", "Security testing", "User acceptance testing"],
      price: "Starting from $3,000"
    },
    {
      icon: Globe,
      title: "Game Porting Services",
      description: "Port your games across multiple platforms and devices seamlessly.",
      features: ["Console porting", "Mobile optimization", "VR/AR adaptation", "Cloud gaming support"],
      price: "Starting from $10,000"
    }
  ];

  return (
    <main className="min-h-screen">
      <SharedNavigation 
        serviceType="gaming" 
        brandName="BRT Gaming" 
        brandColor="blue"
      />
      
      {/* Hero Section */}
      <section className="pt-24 pb-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-blue-900/20 to-blue-900/20">
        <div className="container mx-auto text-center">
          <div className="flex justify-center mb-6">
            <div className="p-4 rounded-2xl bg-gradient-to-br from-blue-500 to-blue-700 shadow-lg">
              <Gamepad2 className="h-12 w-12 text-white" />
            </div>
          </div>
          <h1 className="text-4xl md:text-6xl font-bold mb-6 hero-title">
            Gaming Development Services
          </h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
            Comprehensive gaming solutions from concept to launch. We provide everything you need to create exceptional gaming experiences.
          </p>
          <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3">
            <Rocket className="mr-2 h-5 w-5" />
            Get Started Today
          </Button>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-16 hero-title">
            Our Gaming Services
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <Card key={index} className="service-card hover:scale-105 transition-all duration-300 group">
                <CardContent className="p-8">
                  <div className="flex justify-center mb-4">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-blue-500 to-blue-700 shadow-lg group-hover:shadow-xl transition-all duration-300">
                      <service.icon className="h-8 w-8 text-white" />
                    </div>
                  </div>
                  <h3 className="text-xl font-bold mb-3 text-center group-hover:text-blue-500 transition-colors">
                    {service.title}
                  </h3>
                  <p className="text-muted-foreground mb-4 text-center">
                    {service.description}
                  </p>
                  <ul className="space-y-2 mb-6">
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm">
                        <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-3" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <div className="text-center">
                    <div className="text-lg font-bold text-blue-500 mb-4">{service.price}</div>
                    <Button className="w-full bg-blue-600 hover:bg-blue-700">
                      Learn More
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-muted/20">
        <div className="container mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-16 hero-title">
            Our Development Process
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              { step: "01", title: "Concept & Planning", desc: "Define game mechanics, target audience, and technical requirements" },
              { step: "02", title: "Design & Prototyping", desc: "Create game design documents and build playable prototypes" },
              { step: "03", title: "Development & Testing", desc: "Full development cycle with continuous testing and iteration" },
              { step: "04", title: "Launch & Support", desc: "Game deployment and ongoing maintenance and updates" }
            ].map((phase, index) => (
              <div key={index} className="text-center group">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-700 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-white font-bold text-lg">{phase.step}</span>
                </div>
                <h3 className="text-xl font-bold mb-2">{phase.title}</h3>
                <p className="text-muted-foreground text-sm">{phase.desc}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-blue-600 to-blue-600 text-white">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Start Your Gaming Project?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Contact us today to discuss your gaming project and get a custom quote tailored to your needs.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg" 
              variant="secondary"
              className="px-8 py-3"
              asChild
            >
              <Link href="/gaming-software/contact">
                Get Free Quote
              </Link>
            </Button>
            <Button 
              size="lg" 
              variant="outline"
              className="px-8 py-3 border-white text-white hover:bg-white hover:text-blue-600"
              asChild
            >
              <Link href="/gaming-software">
                Back to Home
              </Link>
            </Button>
          </div>
        </div>
      </section>
    </main>
  );
}