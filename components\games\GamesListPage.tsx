'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { 
  Search, 
  Filter, 
  Star, 
  Users, 
  Download, 
  Play,
  Trophy,
  Gamepad2,
  Grid3X3,
  List
} from 'lucide-react';
import { Game } from '@/components/data/games-data';
import { Button } from '@/components/ui/button';

interface GamesListPageProps {
  games: Game[];
}

export default function GamesListPage({ games }: GamesListPageProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const categories = ['All', ...Array.from(new Set(games.map(game => game.category)))];

  const filteredGames = games.filter(game => {
    const matchesSearch = game.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         game.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'All' || game.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const getColorClasses = (color: string) => {
    switch (color) {
      case 'sky':
        return {
          bg: 'bg-sky-500',
          text: 'text-sky-500',
          border: 'border-sky-500',
          gradient: 'from-sky-500 to-sky-600',
          light: 'bg-sky-50 dark:bg-sky-900/20',
          hover: 'hover:bg-sky-500'
        };
      case 'purple':
        return {
          bg: 'bg-purple-500',
          text: 'text-purple-500',
          border: 'border-purple-500',
          gradient: 'from-purple-500 to-purple-600',
          light: 'bg-purple-50 dark:bg-purple-900/20',
          hover: 'hover:bg-purple-500'
        };
      case 'emerald':
        return {
          bg: 'bg-emerald-500',
          text: 'text-emerald-500',
          border: 'border-emerald-500',
          gradient: 'from-emerald-500 to-emerald-600',
          light: 'bg-emerald-50 dark:bg-emerald-900/20',
          hover: 'hover:bg-emerald-500'
        };
      case 'orange':
        return {
          bg: 'bg-orange-500',
          text: 'text-orange-500',
          border: 'border-orange-500',
          gradient: 'from-orange-500 to-orange-600',
          light: 'bg-orange-50 dark:bg-orange-900/20',
          hover: 'hover:bg-orange-500'
        };
      default:
        return {
          bg: 'bg-sky-500',
          text: 'text-sky-500',
          border: 'border-sky-500',
          gradient: 'from-sky-500 to-sky-600',
          light: 'bg-sky-50 dark:bg-sky-900/20',
          hover: 'hover:bg-sky-500'
        };
    }
  };

  return (
    <main className="min-h-screen bg-white dark:bg-slate-950 text-foreground">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800 py-16 overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-20 left-10 w-32 h-32 bg-sky-50 dark:bg-sky-900/30 rounded-full opacity-60 animate-float"></div>
          <div className="absolute bottom-20 right-10 w-24 h-24 bg-purple-100 dark:bg-purple-800/30 rounded-full opacity-60 animate-float" style={{ animationDelay: '2s' }}></div>
          <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-emerald-200 dark:bg-emerald-700/30 rounded-full opacity-60 animate-float" style={{ animationDelay: '4s' }}></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center max-w-4xl mx-auto">
            <div className="inline-flex items-center gap-3 px-6 py-3 bg-white dark:bg-slate-800 rounded-full mb-8 shadow-lg border-2 border-sky-200 dark:border-sky-700">
              <Trophy className="w-6 h-6 text-sky-500" />
              <span className="text-lg font-bold text-sky-600 dark:text-sky-300">Premium Games Collection</span>
            </div>

            <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6 leading-tight">
              Discover Amazing <span className="text-sky-500">Games</span>
            </h1>

            <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 leading-relaxed">
              Explore our collection of premium games crafted with cutting-edge technology and innovative gameplay mechanics.
            </p>

            {/* Search and Filter */}
            <div className="flex flex-col sm:flex-row gap-4 max-w-2xl mx-auto">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search games..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 bg-white dark:bg-slate-800 border border-gray-300 dark:border-slate-600 rounded-xl focus:ring-2 focus:ring-sky-500 focus:border-transparent transition-all duration-300"
                />
              </div>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-4 py-3 bg-white dark:bg-slate-800 border border-gray-300 dark:border-slate-600 rounded-xl focus:ring-2 focus:ring-sky-500 focus:border-transparent transition-all duration-300"
              >
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </section>

      {/* Games Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          {/* View Controls */}
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              {filteredGames.length} Games Found
            </h2>
            <div className="flex items-center gap-2">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-lg transition-all duration-300 ${
                  viewMode === 'grid' 
                    ? 'bg-sky-500 text-white' 
                    : 'bg-gray-100 dark:bg-slate-800 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-slate-700'
                }`}
              >
                <Grid3X3 className="w-5 h-5" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-lg transition-all duration-300 ${
                  viewMode === 'list' 
                    ? 'bg-sky-500 text-white' 
                    : 'bg-gray-100 dark:bg-slate-800 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-slate-700'
                }`}
              >
                <List className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Games Grid/List */}
          {viewMode === 'grid' ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {filteredGames.map((game, index) => {
                const colors = getColorClasses(game.color);
                return (
                  <Link
                    key={game.id}
                    href={`/games/${game.id}`}
                    className="group bg-white dark:bg-slate-900 rounded-2xl shadow-lg hover:shadow-2xl border border-gray-200 dark:border-slate-700 transition-all duration-500 hover:-translate-y-2 hover:scale-105 overflow-hidden"
                    style={{ animationDelay: `${index * 0.1}s` }}
                  >
                    {/* Game Image */}
                    <div className="relative h-48 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900">
                      <Image
                        src={game.img}
                        alt={game.title}
                        fill
                        className="object-contain p-4 group-hover:scale-110 transition-transform duration-500"
                      />
                      
                      {/* Highlight Badge */}
                      <div className="absolute top-3 right-3">
                        <span className={`px-3 py-1 ${colors.bg} text-white text-xs font-bold rounded-lg shadow-lg`}>
                          {game.highlight}
                        </span>
                      </div>
                    </div>

                    {/* Game Info */}
                    <div className="p-6">
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2 group-hover:text-sky-500 transition-colors duration-300">
                        {game.name}
                      </h3>
                      
                      <p className="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-2">
                        {game.description}
                      </p>

                      {/* Stats */}
                      <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                        <div className="flex items-center gap-1">
                          <Star className="w-4 h-4 text-yellow-400 fill-current" />
                          <span>{game.downloadStats.rating}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Download className="w-4 h-4" />
                          <span>{game.downloadStats.downloads}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Users className="w-4 h-4" />
                          <span>{game.players}</span>
                        </div>
                      </div>

                      {/* Play Button */}
                      <Button 
                        className={`w-full bg-gradient-to-r ${colors.gradient} hover:opacity-90 text-white font-semibold py-2 rounded-xl transition-all duration-300 group-hover:scale-105`}
                      >
                        <Play className="w-4 h-4 mr-2" />
                        Play Now
                      </Button>
                    </div>
                  </Link>
                );
              })}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredGames.map((game, index) => {
                const colors = getColorClasses(game.color);
                return (
                  <Link
                    key={game.id}
                    href={`/games/${game.id}`}
                    className="group flex items-center gap-6 bg-white dark:bg-slate-900 rounded-2xl shadow-lg hover:shadow-2xl border border-gray-200 dark:border-slate-700 transition-all duration-500 hover:scale-102 p-6"
                  >
                    {/* Game Image */}
                    <div className="relative w-24 h-24 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900 rounded-xl flex-shrink-0">
                      <Image
                        src={game.img}
                        alt={game.title}
                        fill
                        className="object-contain p-2 group-hover:scale-110 transition-transform duration-500"
                      />
                    </div>

                    {/* Game Info */}
                    <div className="flex-1">
                      <div className="flex items-start justify-between mb-2">
                        <h3 className="text-xl font-bold text-gray-900 dark:text-white group-hover:text-sky-500 transition-colors duration-300">
                          {game.name}
                        </h3>
                        <span className={`px-3 py-1 ${colors.bg} text-white text-xs font-bold rounded-lg`}>
                          {game.highlight}
                        </span>
                      </div>
                      
                      <p className="text-gray-600 dark:text-gray-300 mb-3">
                        {game.description}
                      </p>

                      <div className="flex items-center gap-6 text-sm text-gray-500">
                        <div className="flex items-center gap-1">
                          <Star className="w-4 h-4 text-yellow-400 fill-current" />
                          <span>{game.downloadStats.rating}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Download className="w-4 h-4" />
                          <span>{game.downloadStats.downloads}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Users className="w-4 h-4" />
                          <span>{game.players}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Gamepad2 className="w-4 h-4" />
                          <span>{game.category}</span>
                        </div>
                      </div>
                    </div>

                    {/* Play Button */}
                    <Button 
                      className={`bg-gradient-to-r ${colors.gradient} hover:opacity-90 text-white font-semibold px-6 py-2 rounded-xl transition-all duration-300 group-hover:scale-105`}
                    >
                      <Play className="w-4 h-4 mr-2" />
                      Play Now
                    </Button>
                  </Link>
                );
              })}
            </div>
          )}

          {/* No Results */}
          {filteredGames.length === 0 && (
            <div className="text-center py-16">
              <Gamepad2 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">No games found</h3>
              <p className="text-gray-600 dark:text-gray-300">Try adjusting your search or filter criteria.</p>
            </div>
          )}
        </div>
      </section>
    </main>
  );
}
