"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar, Phone, ArrowRight, CheckCircle } from "lucide-react";
import Link from "next/link";

export function CTASection() {
  return (
    <section className="relative py-20 px-4 sm:px-6 lg:px-8 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-600 via-blue-700 to-purple-700"></div>
      <div className="absolute top-0 left-0 w-full h-full bg-[url('/api/placeholder/1920/1080')] opacity-10 bg-cover bg-center"></div>
      <div className="absolute top-10 left-10 w-72 h-72 bg-white/10 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute bottom-10 right-10 w-96 h-96 bg-white/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
      
      {/* Floating Elements */}
      <div className="absolute top-20 right-20 w-4 h-4 bg-white/20 rounded-full animate-float"></div>
      <div className="absolute bottom-32 left-16 w-6 h-6 bg-white/15 rounded-full animate-float delay-500"></div>
      <div className="absolute top-1/2 left-10 w-3 h-3 bg-white/25 rounded-full animate-float delay-1000"></div>
      
      <div className="container mx-auto text-center relative z-10">
        <div className="max-w-4xl mx-auto text-white">
          <div className="inline-flex items-center px-6 py-3 rounded-full bg-white/10 border border-white/20 mb-8 animate-fade-in-up">
            <span className="text-sm font-medium">🚀 Ready to Scale Your Business?</span>
          </div>
          
          <h2 className="text-4xl md:text-6xl font-bold mb-6 leading-tight animate-fade-in-up">
            Ready to Transform Your
            <span className="block bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">
              Digital Presence?
            </span>
          </h2>
          
          <p className="text-xl md:text-2xl mb-8 opacity-90 leading-relaxed animate-fade-in-up">
            Join hundreds of successful businesses that have accelerated their growth with our proven digital marketing strategies.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-6 justify-center mb-12 animate-fade-in-up">
            <Button 
              size="lg" 
              className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 text-lg font-semibold group shadow-xl hover:shadow-2xl transition-all duration-300"
            >
              <Calendar className="mr-2 h-5 w-5" />
              Get Free Strategy Session
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Button>
            <Button 
              size="lg" 
              variant="outline"
              className="border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4 text-lg font-semibold transition-all duration-300"
              asChild
            >
              <Link href="/contact">
                <Phone className="mr-2 h-5 w-5" />
                Call Us Now
              </Link>
            </Button>
          </div>
          
          {/* Trust Indicators */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-8 text-sm opacity-80 mb-8">
            <div className="flex items-center">
              <CheckCircle className="h-5 w-5 mr-2 text-green-400" />
              Free consultation & audit
            </div>
            <div className="flex items-center">
              <CheckCircle className="h-5 w-5 mr-2 text-green-400" />
              No long-term contracts
            </div>
            <div className="flex items-center">
              <CheckCircle className="h-5 w-5 mr-2 text-green-400" />
              Proven results guaranteed
            </div>
          </div>
          
          {/* Contact Info */}
          <div className="border-t border-white/20 pt-8 opacity-75">
            <p className="text-lg mb-4">
              Or reach out directly:
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-6">
              <a 
                href="tel:+1234567890" 
                className="flex items-center hover:text-yellow-400 transition-colors"
              >
                <Phone className="h-4 w-4 mr-2" />
                +****************
              </a>
              <a 
                href="mailto:<EMAIL>" 
                className="flex items-center hover:text-yellow-400 transition-colors"
              >
                <Calendar className="h-4 w-4 mr-2" />
                <EMAIL>
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
