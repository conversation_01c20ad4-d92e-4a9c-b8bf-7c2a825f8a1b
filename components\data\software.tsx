export interface SoftwareService {
  title: string;
  description: string;
  features: string[];
  image: string;
  lightColor: string;
  darkColor: string;
}

export const softwareServices: SoftwareService[] = [
    {
    title: "Blockchain Development",
    description: "BRT Multi Software leads the way in blockchain development...",
    features: [
      "Custom Blockchain Solutions",
      "Decentralized Application (DApp) Development",
      "Token and Cryptocurrency Development",
      "Blockchain Consultation and Integration"
    ],
    image: "/software/Blockchain.png",
    lightColor: "#dbeafe",
    darkColor: "#2563eb"
  },
  
  {
    title: "AI Development Services",
    description: "Harness the power of Artificial Intelligence...",
    features: [
      "AI-Powered Chatbots",
      "Machine Learning & Deep Learning Models",
      "AI-Based Data Analytics",
      "Computer Vision Solutions"
    ],
    image: "/software/AI.png",
    lightColor: "#dcfce7",
    darkColor: "#16a34a"
  },
    {
    title: "Game Development",
    description: "Transform your gaming concepts into immersive digital experiences...",
    features: [
      "Mobile Game Development",
      "PC and Console Game Development",
      "AR/VR Game Development",
      "2D & 3D Game Design and Development"
    ],
    image: "/software/GameDev.png",
    lightColor: "#fef3c7",
    darkColor: "#d97706"
  },
   {
    title: "Crypto Exchange Development",
    description: "As pioneers in crypto exchange development...",
    features: [
      "Centralized Exchange Development",
      "Decentralized Exchange Development (DEX)",
      "Hybrid Crypto Exchange Solutions",
      "Peer-to-Peer (P2P) Crypto Exchange"
    ],
    image: "/software/CryptoExchange.png",
    lightColor: "#fce7f3",
    darkColor: "#be185d"
  },
  
 
  {
    title: "Centralized & Decentralized Development",
    description: "BRT Multi Software provides both centralized and decentralized development services...",
    features: [
      "Centralized System Development",
      "Decentralized Network Solutions",
      "Hybrid Solutions for Scalable Systems",
      "Blockchain Integration for Centralized Platforms"
    ],
    image: "/software/CentralizedDecentralized.png",
    lightColor: "#fef2f2",
    darkColor: "#dc2626"
  },
  {
    title: "Coin Listing",
    description: "BRT Multi Software offers expert coin listing services...",
    features: [
      "Crypto Token Listing Services",
      "Exchange Listing Consultation",
      "Token Evaluation and Auditing",
      "Exchange Onboarding Assistance"
    ],
    image: "/software/CoinListing.png",
    lightColor: "#f0f9ff",
    darkColor: "#0284c7"
  },  {
    title: "Smart Contract Development",
    description: "At BRT Multi Software, we specialize in creating secure and efficient smart contracts...",
    features: [
      "Custom Smart Contract Development",
      "Auditing and Optimization",
      "Smart Contract Integration for DApps",
      "Blockchain Smart Contract Solutions"
    ],
    image: "/software/SmartContract.png",
    lightColor: "#f3e8ff",
    darkColor: "#7c3aed"
  },

  {
    title: "Web3 Development",
    description: "As the internet evolves, BRT Multi Software empowers businesses with Web3...",
    features: [
      "Web3 Exchange Development",
      "Web3 Wallet Integration",
      "Decentralized Applications (DApps)",
      "Web3 NFT and Gaming Solutions"
    ],
    image: "/software/Web3.png",
    lightColor: "#ecfdf5",
    darkColor: "#059669"
  },
  {
    title: "DeFi Development",
    description:
      "BRT Multi Software offers comprehensive DeFi development services that enable businesses to launch decentralized financial platforms. We help create decentralized applications, protocols, and platforms that eliminate intermediaries, empowering users to access financial services directly.",
    features: [
      "DeFi DApp Development",
      "DeFi Staking Platforms",
      "DeFi Smart Contract Development",
      "Decentralized Finance Token Development",
    ],
    image: "/software/defi.png",
    lightColor: "#fffbeb",
    darkColor: "#ea580c"
  },
  {
    title: "NFT Development",
    description: "Unlock the potential of NFTs with BRT Multi Software...",
    features: [
      "NFT Marketplace Development",
      "NFT Token Creation and Minting",
      "NFT Game Development",
      "NFT Staking Platforms"
    ],
    image: "/software/NFT.png",
    lightColor: "#f5d0fe",
    darkColor: "#a21caf"
  }
  
]; 