"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight, Phone, Calendar, CheckCircle } from "lucide-react";
import Link from "next/link";

interface ServiceCTAProps {
  title: string;
  subtitle: string;
  primaryCTA: string;
  secondaryCTA: string;
  features: string[];
  backgroundGradient?: string;
}

export function ServiceCTA({
  title,
  subtitle,
  primaryCTA,
  secondaryCTA,
  features,
  backgroundGradient = "from-blue-600 via-blue-700 to-purple-700"
}: ServiceCTAProps) {
  return (
    <section className="relative py-20 px-4 sm:px-6 lg:px-8 overflow-hidden">
      {/* 3D Background Elements */}
      <div className={`absolute inset-0 bg-gradient-to-br ${backgroundGradient}`}></div>
      
      {/* Animated Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `
            radial-gradient(circle at 25% 25%, rgba(255,255,255,0.2) 2px, transparent 2px),
            radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px',
          animation: 'float 20s ease-in-out infinite'
        }}></div>
      </div>
      
      {/* Floating 3D Elements */}
      <div className="absolute top-10 left-10 w-72 h-72 bg-white/5 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute bottom-10 right-10 w-96 h-96 bg-white/3 rounded-full blur-3xl animate-pulse delay-1000"></div>
      
      {/* Geometric Shapes */}
      <div className="absolute top-20 right-20 w-4 h-4 bg-white/20 rounded-full animate-bounce"></div>
      <div className="absolute bottom-32 left-16 w-6 h-6 bg-white/15 rounded-full animate-bounce delay-500"></div>
      <div className="absolute top-1/2 left-10 w-3 h-3 bg-white/25 rounded-full animate-bounce delay-1000"></div>
      
      <div className="container mx-auto text-center relative z-10">
        <div className="max-w-4xl mx-auto text-white">
          <div className="inline-flex items-center px-6 py-3 rounded-full bg-white/10 border border-white/20 mb-8 animate-fade-in-up backdrop-blur-sm">
            <span className="text-sm font-medium">🚀 Ready to Get Started?</span>
          </div>
          
          <h2 className="text-4xl md:text-6xl font-bold mb-6 leading-tight animate-fade-in-up">
            {title}
          </h2>
          
          <p className="text-xl md:text-2xl mb-8 opacity-90 leading-relaxed animate-fade-in-up">
            {subtitle}
          </p>
          
          <div className="flex flex-col sm:flex-row gap-6 justify-center mb-12 animate-fade-in-up">
            <Button 
              size="lg" 
              className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 text-lg font-semibold group shadow-2xl hover:shadow-3xl transition-all duration-300 hover:scale-105"
            >
              <Calendar className="mr-2 h-5 w-5" />
              {primaryCTA}
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Button>
            <Button 
              size="lg" 
              variant="outline"
              className="border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4 text-lg font-semibold transition-all duration-300 backdrop-blur-sm"
              asChild
            >
              <Link href="/contact">
                <Phone className="mr-2 h-5 w-5" />
                {secondaryCTA}
              </Link>
            </Button>
          </div>
          
          {/* Trust Indicators */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-8 text-sm opacity-80 mb-8">
            {features.map((feature, index) => (
              <div 
                key={index} 
                className="flex items-center animate-fade-in-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <CheckCircle className="h-5 w-5 mr-2 text-green-400" />
                {feature}
              </div>
            ))}
          </div>
          
          {/* Contact Info */}
          <div className="border-t border-white/20 pt-8 opacity-75">
            <p className="text-lg mb-4">
              Or reach out directly:
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-6">
              <a 
                href="tel:+1234567890" 
                className="flex items-center hover:text-yellow-400 transition-colors group"
              >
                <Phone className="h-4 w-4 mr-2 group-hover:animate-pulse" />
                +****************
              </a>
              <a 
                href="mailto:<EMAIL>" 
                className="flex items-center hover:text-yellow-400 transition-colors group"
              >
                <Calendar className="h-4 w-4 mr-2 group-hover:animate-pulse" />
                <EMAIL>
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
