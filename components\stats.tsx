'use client';
import React, { useEffect, useRef, useState } from "react";

interface Stat {
  number: number;
  label: string;
  icon?: string;
}

interface StatsProps {
  stats: Stat[];
}

// SVG Shape Components
const CirclePattern = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 100 100" fill="none">
    <circle cx="50" cy="50" r="45" stroke="currentColor" strokeWidth="2" strokeDasharray="5,5" opacity="0.3" />
    <circle cx="50" cy="50" r="30" stroke="currentColor" strokeWidth="1" opacity="0.2" />
    <circle cx="50" cy="50" r="15" fill="currentColor" opacity="0.1" />
  </svg>
);

const HexagonPattern = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 100 100" fill="none">
    <path d="M50 5 L85 25 L85 75 L50 95 L15 75 L15 25 Z" stroke="currentColor" strokeWidth="2" fill="none" opacity="0.3" />
    <path d="M50 15 L75 30 L75 70 L50 85 L25 70 L25 30 Z" stroke="currentColor" strokeWidth="1" fill="none" opacity="0.2" />
    <circle cx="50" cy="50" r="8" fill="currentColor" opacity="0.1" />
  </svg>
);

const TrianglePattern = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 100 100" fill="none">
    <path d="M50 10 L85 80 L15 80 Z" stroke="currentColor" strokeWidth="2" fill="none" opacity="0.3" />
    <path d="M50 25 L70 65 L30 65 Z" stroke="currentColor" strokeWidth="1" fill="none" opacity="0.2" />
    <circle cx="50" cy="55" r="6" fill="currentColor" opacity="0.1" />
  </svg>
);

const DiamondPattern = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 100 100" fill="none">
    <path d="M50 10 L80 50 L50 90 L20 50 Z" stroke="currentColor" strokeWidth="2" fill="none" opacity="0.3" />
    <path d="M50 25 L65 50 L50 75 L35 50 Z" stroke="currentColor" strokeWidth="1" fill="none" opacity="0.2" />
    <circle cx="50" cy="50" r="5" fill="currentColor" opacity="0.1" />
  </svg>
);

const patterns = [CirclePattern, HexagonPattern, TrianglePattern, DiamondPattern];

export const Stats: React.FC<StatsProps> = ({ stats }) => {
  const [counts, setCounts] = useState(stats.map(() => 0));
  const sectionRef = useRef<HTMLDivElement | null>(null);
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    const observer = new window.IntersectionObserver(
      ([entry]) => {
        setVisible(entry.isIntersecting);
      },
      { threshold: 0.3 }
    );
    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }
    return () => {
      if (sectionRef.current) observer.unobserve(sectionRef.current);
    };
  }, []);

  useEffect(() => {
    if (!visible) {
      setCounts(stats.map(() => 0));
      return;
    }
    const intervals: NodeJS.Timeout[] = [];
    stats.forEach((stat, i) => {
      let current = 0;
      const increment = Math.max(1, Math.ceil(stat.number / 80));
      intervals[i] = setInterval(() => {
        current += increment;
        if (current >= stat.number) {
          current = stat.number;
          setCounts(prev => {
            const updated = [...prev];
            updated[i] = current;
            return updated;
          });
          clearInterval(intervals[i]);
        } else {
          setCounts(prev => {
            const updated = [...prev];
            updated[i] = current;
            return updated;
          });
        }
      }, 20);
    });
    return () => intervals.forEach(clearInterval);
  }, [visible, stats]);

  return (
    <div className="relative bg-muted/20 py-4 xs:py-5 sm:py-6 md:py-8 px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 mb-2 xs:mb-3 sm:mb-4 overflow-hidden">
      {/* Enhanced Responsive Floating SVG Background Elements */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-2 xs:top-3 sm:top-4 left-4 xs:left-6 sm:left-8 w-10 xs:w-12 sm:w-16 h-10 xs:h-12 sm:h-16 text-sky-500/10 animate-float">
          <CirclePattern className="w-full h-full" />
        </div>
        <div className="absolute top-6 xs:top-8 sm:top-12 right-6 xs:right-8 sm:right-12 w-12 xs:w-16 sm:w-20 h-12 xs:h-16 sm:h-20 text-purple-500/10 animate-float" style={{ animationDelay: '1s' }}>
          <HexagonPattern className="w-full h-full" />
        </div>
        <div className="absolute bottom-4 xs:bottom-6 sm:bottom-8 left-1/4 w-8 xs:w-10 sm:w-14 h-8 xs:h-10 sm:h-14 text-emerald-500/10 animate-float" style={{ animationDelay: '2s' }}>
          <TrianglePattern className="w-full h-full" />
        </div>
        <div className="absolute bottom-2 xs:bottom-3 sm:bottom-4 right-1/4 xs:right-1/3 w-10 xs:w-12 sm:w-18 h-10 xs:h-12 sm:h-18 text-orange-500/10 animate-float" style={{ animationDelay: '0.5s' }}>
          <DiamondPattern className="w-full h-full" />
        </div>
      </div>

      <div ref={sectionRef} className="relative z-10 grid grid-cols-2 md:grid-cols-4 gap-responsive container-responsive">
        {stats.map((stat, i) => {
          const PatternComponent = patterns[i % patterns.length];
          const colors = ['text-sky-500', 'text-purple-500', 'text-emerald-500', 'text-orange-500'];
          const bgColors = ['bg-sky-50', 'bg-purple-50', 'bg-emerald-50', 'bg-orange-50'];
          const darkBgColors = ['dark:bg-sky-950/20', 'dark:bg-purple-950/20', 'dark:bg-emerald-950/20', 'dark:bg-orange-950/20'];

          return (
            <div
              key={i}
              className={`group relative text-center ${bgColors[i % bgColors.length]} ${darkBgColors[i % darkBgColors.length]} backdrop-blur-sm rounded-responsive shadow-md hover:shadow-xl padding-responsive border border-border/20 transition-all duration-500 hover:scale-105 hover:-translate-y-1 animate-fade-in-up`}
              style={{ animationDelay: `${i * 0.15}s` }}
            >
              {/* Enhanced Responsive Background Pattern */}
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                <PatternComponent className={`absolute top-1 xs:top-1.5 sm:top-2 right-1 xs:right-1.5 sm:right-2 w-4 xs:w-6 sm:w-8 h-4 xs:h-6 sm:h-8 ${colors[i % colors.length]} animate-spin-slow`} />
              </div>

              {/* Enhanced Responsive Animated Border Effect */}
              <div className="absolute inset-0 rounded-responsive border-2 border-transparent group-hover:border-current transition-all duration-500"
                   style={{ color: `var(--${colors[i % colors.length].split('-')[1]}-500)` }}></div>

              {/* Enhanced Responsive Main Pattern Icon */}
              <div className={`mx-auto mb-1.5 xs:mb-2 sm:mb-3 w-6 xs:w-7 sm:w-8 md:w-10 lg:w-12 h-6 xs:h-7 sm:h-8 md:h-10 lg:h-12 ${colors[i % colors.length]} group-hover:animate-pulse`}>
                <PatternComponent className="w-full h-full" />
              </div>

              {/* Enhanced Responsive Counter */}
              <div className={`text-responsive-xl font-bold ${colors[i % colors.length]} drop-shadow-sm animate-stats-bounce`}>
                {counts[i]}+
              </div>

              {/* Enhanced Responsive Label */}
              <div className="text-responsive-xs text-muted-foreground mt-1 xs:mt-1.5 sm:mt-2 font-medium group-hover:text-foreground transition-colors duration-300">
                {stat.label}
              </div>

              {/* Hover Glow Effect */}
              <div className={`absolute inset-0 rounded-xl opacity-0 group-hover:opacity-20 transition-opacity duration-500 ${bgColors[i % bgColors.length]}`}></div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

 